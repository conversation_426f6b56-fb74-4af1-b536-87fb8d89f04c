name: Build and Test
on:
  push:
    branches:
      - master
      - hotfix
  pull_request:
    types: [opened, synchronize, reopened]
concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true
env:
  REPOSITORY_URL: 917043647191.dkr.ecr.eu-north-1.amazonaws.com/vitec-data-sync-service
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: eu-north-1
  ROOT_FOLDER_PATH: ${{ github.workspace }}
jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    container: node:18-alpine
    steps:
      - name: Add utilities
        run: apk add --no-cache tar
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          cache: 'yarn'
      - name: Install Yarn
        run: yarn --frozen-lockfile
      - name: Lint
        run: yarn lint
      - name: Test
        run: yarn test
  build:
    name: Build
    runs-on: ubuntu-latest
    container: docker:stable
    services:
      docker:stable-dind:
        image: docker:stable-dind
    steps:
      - name: Add utilities
        run: apk add --no-cache curl jq python3 py-pip
      - name: PIP install
        run: pip install awscli
      - name: Set AWS login
        run: $(aws ecr get-login --no-include-email --region $AWS_DEFAULT_REGION)
      - name: Checkout
        uses: actions/checkout@v3
      - name: Build image
        run: docker build -t $REPOSITORY_URL:latest .
      - name: Tag image
        run: docker tag $REPOSITORY_URL:latest $REPOSITORY_URL:$GITHUB_SHA
      - name: Push image as latest
        run: docker push $REPOSITORY_URL:latest
      - name: Push image with commit sha
        run: docker push $REPOSITORY_URL:$GITHUB_SHA
