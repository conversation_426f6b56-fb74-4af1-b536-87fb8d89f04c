name: Deploy
run-name: Deploy ${{ github.ref }} to ${{ github.event.inputs.environment }}
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        type: environment
        required: true
env:
  REPOSITORY_URL: 917043647191.dkr.ecr.eu-north-1.amazonaws.com/vitec-data-sync-service
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: eu-north-1
  ROOT_FOLDER_PATH: ${{ github.workspace }}
  SERVICE_NAME: ${{ vars.SERVICE_NAME }}
  TASK_DEFINITION_NAME: ${{ vars.TASK_DEFINITION_NAME }}
  CLUSTER_NAME: ${{ vars.CLUSTER_NAME }}
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - run: sed -i "s~VAR_S3_BUCKET~${{ vars.S3_BUCKET }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_CLOUDFRONT_URL~${{ vars.CLOUDFRONT_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_MONGODB_URI~${{ vars.MONGODB_URI }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_VITEC_API_URL~${{ vars.VITEC_API_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_VITEC_INSTALLATION_ID~${{ vars.VITEC_INSTALLATION_ID }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_VITEC_PASSWORD~${{ vars.VITEC_PASSWORD }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_VITEC_USER_NAME~${{ vars.VITEC_USER_NAME }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_AWS_REGION~$AWS_DEFAULT_REGION~g" deploy/task_definition.json
      - run: sed -i "s~VAR_FAMILY~${{ vars.TASK_DEFINITION_NAME }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_IMAGE_URL~$REPOSITORY_URL:$GITHUB_SHA~g" deploy/task_definition.json
      - run: sed -i "s~VAR_AWS_LOG_GROUP~${{ vars.AWS_LOG_GROUP }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_S3_SECRET_ACCESS_KEY~${{ vars.S3_SECRET_ACCESS_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_S3_ACCESS_KEY_ID~${{ vars.S3_ACCESS_KEY_ID }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_TWILIO_ACCOUNT_SID~${{ vars.TWILIO_ACCOUNT_SID }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_TWILIO_AUTH_TOKEN~${{ vars.TWILIO_AUTH_TOKEN }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_TWILIO_SMS_PHONE_NUMBER~${{ vars.TWILIO_SMS_PHONE_NUMBER }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_UNLEASH_URL~${{ vars.UNLEASH_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_UNLEASH_INSTANCE_ID~""~g" deploy/task_definition.json
      - run: sed -i "s~VAR_UNLEASH_APP_NAME~${{ vars.UNLEASH_APP_NAME }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_UNLEASH_AUTHORIZATION_HEADER~${{ vars.UNLEASH_AUTHORIZATION_HEADER }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_APP_URL~${{ vars.APP_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_API_KEY~${{ vars.API_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_POSTGRES_USERNAME~${{ vars.POSTGRES_USERNAME }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_POSTGRES_PASSWORD~${{ vars.POSTGRES_PASSWORD }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_POSTGRES_HOST~${{ vars.POSTGRES_HOST }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_POSTGRES_DB~${{ vars.POSTGRES_DB }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_POSTGRES_PORT~${{ vars.POSTGRES_PORT }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_FIREBASE_PROJECT_ID~${{ vars.FIREBASE_PROJECT_ID }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_FIREBASE_PRIVATE_KEY~${{ vars.FIREBASE_PRIVATE_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_FIREBASE_CLIENT_EMAIL~${{ vars.FIREBASE_CLIENT_EMAIL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_SQS_URL~${{ vars.SQS_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_DISABLE_SQS~${{ vars.DISABLE_SQS }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL~${{ vars.EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_EIENDOMSVERDI_SERVICE_URL~${{ vars.EIENDOMSVERDI_SERVICE_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_EIENDOMSVERDI_USER~${{ vars.EIENDOMSVERDI_USER }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_EIENDOMSVERDI_PASSWORD~${{ vars.EIENDOMSVERDI_PASSWORD }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_MAPBOX_ACCESS_TOKEN~${{ vars.MAPBOX_ACCESS_TOKEN }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_MAPBOX_STYLE~${{ vars.MAPBOX_STYLE }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_MAPBOX_USER_NAME~${{ vars.MAPBOX_USER_NAME }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_TOKEN_SECRET~""~g" deploy/task_definition.json
      - run: sed -i "s~VAR_BACKEND_URL~${{ vars.BACKEND_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_SENDGRID_API_KEY~${{ vars.SENDGRID_API_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_SLACK_WEBHOOK_BASE_URI~${{ vars.SLACK_WEBHOOK_BASE_URI }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_SLACK_DOCUMENT_ERROR_POSTFIX~${{ vars.SLACK_DOCUMENT_ERROR_POSTFIX }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_SLACK_SMS_DUPLICATION_ERROR_POSTFIX~${{ vars.SLACK_SMS_DUPLICATION_ERROR_POSTFIX }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_IDFY_ID~${{ vars.IDFY_ID }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_IDFY_SECRET~${{ vars.IDFY_SECRET }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_BACKEND_API_KEY~${{ vars.BACKEND_API_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_LIPSCORE_API_KEY~${{ vars.LIPSCORE_API_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_LIPSCORE_SECRET_API_KEY~${{ vars.LIPSCORE_SECRET_API_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_DD_API_KEY~${{ secrets.DD_API_KEY }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_DD_SITE~${{ secrets.DD_SITE }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_ECS_FARGATE~${{ vars.ECS_FARGATE }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_DD_APM_ENABLED~${{ vars.DD_APM_ENABLED }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_ENVIRONMENT~${{ vars.ENVIRONMENT }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_LIPSCORE_API_URL~${{ secrets.LIPSCORE_API_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_CRAFT_CMS_URL~${{ vars.CRAFT_CMS_URL }}~g" deploy/task_definition.json
      - run: sed -i "s~VAR_CRAFT_CMS_API_KEY~${{ vars.CRAFT_CMS_API_KEY }}~g" deploy/task_definition.json
      - run: cat deploy/task_definition.json | base64 | base64 | base64
      - name: Register Task Definition
        run: aws ecs register-task-definition --cli-input-json file://deploy/task_definition.json
      - name: Update Service
        run: aws ecs update-service --region "${AWS_DEFAULT_REGION}" --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}"  --task-definition "${TASK_DEFINITION_NAME}"
