version: "3.9"

services:
  mongo:
    image: mongo:4.4-bionic
    environment:
      MONGO_INITDB_ROOT_USERNAME: docker_user
      MONGO_INITDB_ROOT_PASSWORD: docker_password
      MONGO_INITDB_DATABASE: docker_db
    ports:
      - 27017:27017

  mongo-express:
    image: mongo-express
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_MONGODB_ADMINUSERNAME=docker_user
      - ME_CONFIG_MONGODB_ADMINPASSWORD=docker_password
    ports:
      - 8081:8081

  redis:
    image: redis:alpine
    ports:
      - 6379:6379
