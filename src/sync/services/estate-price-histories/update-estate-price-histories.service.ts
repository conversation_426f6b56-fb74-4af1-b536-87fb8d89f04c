import { ConsoleLogger, Injectable } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import { EiendomsverdiService } from '../../../eiendomsverdi/eiendomsverdi.service';
import { EstatePriceHistoriesService } from '../../../pg/estate-price-histories/estate-price-histories.service';
import { EstateProjected, EstateService } from '../../../pg/estate/estate.service';

const EVERY_28TH_DAY_OF_MONTH_AT_1_AM = '0 1 28 * *';

@Injectable()
export class UpdateEstatePriceHistoriesService {
  constructor(
    private readonly estatePriceHistoriesService: EstatePriceHistoriesService,
    private readonly evService: EiendomsverdiService,
    private readonly estateService: EstateService,
    private readonly logger: ConsoleLogger,
  ) {
    this.logger.setContext(UpdateEstatePriceHistoriesService.name);
  }

  @Cron(EVERY_28TH_DAY_OF_MONTH_AT_1_AM, {
    name: UpdateEstatePriceHistoriesService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('UpdateEstatePriceHistoriesService update started');
    await this.trigger();
    this.logger.log('UpdateEstatePriceHistoriesService update finished');
  }

  async trigger(): Promise<void> {
    const allEstatesInPg = await this.estateService.getAllNonArchivedProjected();
    let progress = 1;
    for (const estateInPg of allEstatesInPg) {
      this.logger.log(`Progress of estateHistorySave ${estateInPg.id}: ${progress++} / ${allEstatesInPg.length}`);
      if (!estateInPg.landIdentificationMatrix) {
        this.logger.warn(
          `No landIdentificationMatrix for estate ${estateInPg.id}, skipping price history saving for this record`,
        );
        continue;
      }
      await this.saveEstatePriceHistory(estateInPg);
      await new Promise((resolve) => setTimeout(resolve, 50));
    }
  }

  async triggerForOneEstate(pgEstateId: string): Promise<void> {
    const estate = await this.estateService.findByIdProjected(pgEstateId);
    await this.saveEstatePriceHistory(estate);
  }

  private async saveEstatePriceHistory(estateInPg: EstateProjected) {
    const newValuation = await this.evService.getEstateEvaluation(estateInPg.landIdentificationMatrix, {
      organizationNumber: estateInPg.OrganizationNumber,
      shareNumber: estateInPg.ShareNumber,
    });
    await this.estatePriceHistoriesService.createOneRecordForOneEstate({
      postgresEstateId: estateInPg.id,
      landIdentificationMatrix: estateInPg.landIdentificationMatrix,
      evPrice: newValuation?.valuation || -1,
      actualPriceWithVitecOffset: (newValuation?.valuation || -1) + (estateInPg.estimationOffset || 0),
    });
  }
}
