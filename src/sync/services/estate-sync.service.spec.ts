import { SchedulerRegistry } from '@nestjs/schedule';
import { add, endOfDay, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService from '../../authorization/authorization.service';
import { EiendomsverdiService } from '../../eiendomsverdi/eiendomsverdi.service';
import { mocAppConfigServiceFactory } from '../../mock-classes/mock-app-config-service';
import { mockAuthorizationServiceFactory } from '../../mock-classes/mock-authorization-service';
import { mockEstateServiceFactory } from '../../mock-classes/mock-estate-service';
import { mockEvServiceFactory } from '../../mock-classes/mock-ev-service';
import { mockModelFactory } from '../../mock-classes/mock-model';
import { mockMongoServiceFactory } from '../../mock-classes/mock-mongo-service';
import { mockNotificationServiceFactory } from '../../mock-classes/mock-notification-service';
import { mockOvertakeProtocolNotificationServiceFactory } from '../../mock-classes/mock-overtake-protocol-notification-service';
import { mockPoliticallyExposedPersonFormNotificationServiceFactory } from '../../mock-classes/mock-politically-exposed-person-form-notification-service';
import { mockS3ServiceFactory } from '../../mock-classes/mock-s3-service';
import { mockSchedulerRegistryFactory } from '../../mock-classes/mock-scheduler-registry';
import { mockSettlementBuyerNotificationServiceFactory } from '../../mock-classes/mock-settlement-buyer-notification-service';
import { mockSettlementSellerNotificationServiceFactory } from '../../mock-classes/mock-settlement-seller-notification-service';
import { mockSyncServiceFactory } from '../../mock-classes/mock-sync-service';
import { mockUserNotifierServiceFactory } from '../../mock-classes/mock-user-notifier-service';
import { mockVitecServiceFactory } from '../../mock-classes/mock-vitec-service';
import { MongoService } from '../../mongo/mongo.service';
import { NotificationService } from '../../notification/notification.service';
import { OvertakeProtocolNotificationService } from '../../notification/services/overtake-protocol-notification.service';
import { PoliticallyExposedPersonFormNotificationService } from '../../notification/services/politically-exposed-person-form-notification.service';
import { SettlementBuyerNotificationService } from '../../notification/services/settlement-buyer-notification.service';
import { SettlementSellerNotificationService } from '../../notification/services/settlement-seller-notification.service';
import { UserNotifierService } from '../../notification/user-notifier.service';
import { EstateService } from '../../pg/estate/estate.service';
import { S3Service } from '../../s3/s3.service';
import EstateDto from '../../vitec/dto/estate.dto';
import { VitecService } from '../../vitec/vitec.service';
import { BuyerDocument } from '../schema/buyer.schema';
import { ContactDocument } from '../schema/contact.schema';
import { DepartmentDocument } from '../schema/department.schema';
import { EmployeeDocument } from '../schema/employee.schema';
import { BrokerRole, EstateBaseType, EstateDocument } from '../schema/estate.schema';
import { ProxyDocument } from '../schema/proxy.schema';
import { SellerDocument } from '../schema/seller.schema';
import { SyncService } from '../sync.service';
import { EndpointType, EstateSyncService } from './estate-sync.service';

const getMockDependencies = (): {
  mockSchedulerRegistry: SchedulerRegistry;
  mockVitecService: VitecService;
  mockMongoService: MongoService;
  mockSyncService: SyncService;
  mockS3Service: S3Service;
  mockAuthorizationService: AuthorizationService;
  mockNotificationService: NotificationService;
  mockAppConfigService: AppConfigService;
  mockUserNotifierService: UserNotifierService;
  mockSettlementBuyerNotificationService: SettlementBuyerNotificationService;
  mockSettlementSellerNotificationService: SettlementSellerNotificationService;
  mockOvertakeProtocolNotificationService: OvertakeProtocolNotificationService;
  mockPoliticallyExposedPersonFormNotificationService: PoliticallyExposedPersonFormNotificationService;
  estateModel: Model<EstateDocument>;
  departmentModel: Model<DepartmentDocument>;
  employeeModel: Model<EmployeeDocument>;
  sellerModel: Model<SellerDocument>;
  buyerModel: Model<BuyerDocument>;
  proxyModel: Model<ProxyDocument>;
  contactModel: Model<ContactDocument>;
  estateService: EstateService;
  evService: EiendomsverdiService;
} => ({
  mockSchedulerRegistry: mockSchedulerRegistryFactory(),
  mockVitecService: mockVitecServiceFactory(),
  mockMongoService: mockMongoServiceFactory(),
  mockSyncService: mockSyncServiceFactory(),
  mockS3Service: mockS3ServiceFactory(),
  mockAuthorizationService: mockAuthorizationServiceFactory(),
  mockNotificationService: mockNotificationServiceFactory(),
  mockAppConfigService: mocAppConfigServiceFactory(),
  mockUserNotifierService: mockUserNotifierServiceFactory(),
  mockSettlementBuyerNotificationService: mockSettlementBuyerNotificationServiceFactory(),
  mockSettlementSellerNotificationService: mockSettlementSellerNotificationServiceFactory(),
  mockOvertakeProtocolNotificationService: mockOvertakeProtocolNotificationServiceFactory(),
  mockPoliticallyExposedPersonFormNotificationService: mockPoliticallyExposedPersonFormNotificationServiceFactory(),
  estateModel: mockModelFactory<EstateDocument>(),
  departmentModel: mockModelFactory<DepartmentDocument>(),
  employeeModel: mockModelFactory<EmployeeDocument>(),
  sellerModel: mockModelFactory<SellerDocument>(),
  buyerModel: mockModelFactory<BuyerDocument>(),
  proxyModel: mockModelFactory<ProxyDocument>(),
  contactModel: mockModelFactory<ContactDocument>(),
  estateService: mockEstateServiceFactory(),
  evService: mockEvServiceFactory(),
});

describe('EstateSyncService', () => {
  describe('upsertEstate', () => {
    it('should call updateEstateBase and upsert, BUT not call getContactInformation in case of status 2 if endpoint is Estates', async () => {
      const deps = getMockDependencies();

      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };
      (deps.employeeModel.findOne as jest.Mock).mockReturnValue({
        ...mainEmployeeWithoutId,
        employeeId: 'mockEmployeeId',
      });
      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 2,
        brokersIdWithRoles,
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);

      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );

      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date('2022-02-18');
      const existingOldEstate: Partial<EstateDocument> = { estateBaseType: EstateBaseType.DETACHED };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.employeeModel.findOne).toHaveBeenCalledWith({ employeeId: 'mockEmployeeId' });
      expect(deps.departmentModel.findOne).toHaveBeenCalledWith({ departmentId: 123 });
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.estateModel,
        { estateId: 'mockVitecEstateId' },
        {
          estateId: 'mockVitecEstateId',
          departmentId: 123,
          brokersIdWithRoles,
          url: '/boliger/mockVitecEstateId',
          location: { type: 'Point', coordinates: [0, 0] },
          employeeId: 'mockEmployeeId',
          employee: { ...mainEmployeeWithoutId, employeeId: 'mockEmployeeId' },
          department,
          textFields: undefined,
          facilities: undefined,
          partOwnership: undefined,
        },
      );
      // Explicit update after updateEstateBase
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.estateModel,
        { estateId: 'mockVitecEstateId' },
        { lastSuccessfulSyncStartDate: syncReceiveDate },
      );
      expect(deps.mockVitecService.getContactInformation).not.toHaveBeenCalled();
    });
    it('should call updateEstateBase and upsert, AND call getContactInformation in case of status 3 if endpoint is Estates', async () => {
      const deps = getMockDependencies();
      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      (deps.buyerModel.updateMany as jest.Mock).mockReturnValue({ nModified: 5 });
      (deps.buyerModel.find as jest.Mock).mockReturnValue([]);
      (deps.sellerModel.updateMany as jest.Mock).mockReturnValue({ nModified: 5 });
      (deps.sellerModel.find as jest.Mock).mockReturnValue([]);
      (deps.proxyModel.updateMany as jest.Mock).mockReturnValue({ nModified: 5 });
      (deps.proxyModel.find as jest.Mock).mockReturnValue([]);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };
      (deps.employeeModel.findOne as jest.Mock).mockReturnValue({
        ...mainEmployeeWithoutId,
        employeeId: 'mockEmployeeId',
      });
      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 3,
        brokersIdWithRoles,
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockVitecService.getContactInformation as jest.Mock).mockReturnValue({
        sellers: [
          {
            contactId: 'mockSellerContactId',
            contactType: 1,
            companyName: 'mockSellerCompanyName',
            firstName: 'mockSellerFirstName',
            lastName: 'mockSellerLastName',
            mobilePhone: '01 2345 67',
            socialSecurity: 'mockSellerSocialSecurity',
          },
        ],
        buyers: [
          {
            contactId: 'mockBuyerContactId',
            contactType: 0,
            companyName: 'mockBuyerCompanyName',
            firstName: 'mockBuyerFirstName',
            lastName: 'mockBuyerLastName',
            mobilePhone: '12 345',
            socialSecurity: 'mockBuyerSocialSecurity',
          },
          {
            contactId: 'mockBuyerContactId2',
            contactType: 0,
            companyName: 'mockBuyerCompanyName2',
            firstName: 'mockBuyerFirstName2',
            lastName: 'mockBuyerLastName2',
          },
        ],
        proxies: [],
      });
      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );
      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date('2022-02-18');
      const existingOldEstate: Partial<EstateDocument> = { estateBaseType: EstateBaseType.DETACHED };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.employeeModel.findOne).toHaveBeenCalledWith({ employeeId: 'mockEmployeeId' });
      expect(deps.departmentModel.findOne).toHaveBeenCalledWith({ departmentId: 123 });
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.estateModel,
        { estateId: 'mockVitecEstateId' },
        {
          estateId: 'mockVitecEstateId',
          departmentId: 123,
          brokersIdWithRoles,
          url: '/boliger/mockVitecEstateId',
          location: { type: 'Point', coordinates: [0, 0] },
          employeeId: 'mockEmployeeId',
          employee: { ...mainEmployeeWithoutId, employeeId: 'mockEmployeeId' },
          department,
          textFields: undefined,
          facilities: undefined,
          partOwnership: undefined,
        },
      );
      // Explicit update after updateEstateBase
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.estateModel,
        { estateId: 'mockVitecEstateId' },
        { lastSuccessfulSyncStartDate: syncReceiveDate },
      );
      expect(deps.mockVitecService.getContactInformation).toHaveBeenCalledWith('mockVitecEstateId');
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.sellerModel,
        { contactId: 'mockSellerContactId' },
        {
          contactId: 'mockSellerContactId',
          contactType: 1,
          companyName: 'mockSellerCompanyName',
          firstName: 'mockSellerFirstName',
          lastName: 'mockSellerLastName',
          mobilePhone: '01234567',
          $addToSet: {
            estates: estate,
          },
        },
      );

      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.buyerModel,
        { contactId: 'mockBuyerContactId' },
        {
          contactId: 'mockBuyerContactId',
          contactType: 0,
          companyName: 'mockBuyerCompanyName',
          firstName: 'mockBuyerFirstName',
          lastName: 'mockBuyerLastName',
          mobilePhone: '12345',
          $addToSet: {
            estates: estate,
          },
        },
      );
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.buyerModel,
        { contactId: 'mockBuyerContactId2' },
        {
          contactId: 'mockBuyerContactId2',
          contactType: 0,
          companyName: 'mockBuyerCompanyName2',
          firstName: 'mockBuyerFirstName2',
          lastName: 'mockBuyerLastName2',
          mobilePhone: null,
          $addToSet: {
            estates: estate,
          },
        },
      );
      expect(deps.mockMongoService.upsert).toHaveBeenCalledWith(
        deps.estateModel,
        { estateId: 'mockVitecEstateId' },
        {
          sellers: [
            {
              contactId: 'mockSellerContactId',
              contactType: 1,
              companyName: 'mockSellerCompanyName',
              firstName: 'mockSellerFirstName',
              lastName: 'mockSellerLastName',
              mobilePhone: '01234567',
            },
          ],
          buyers: [
            {
              contactId: 'mockBuyerContactId',
              contactType: 0,
              companyName: 'mockBuyerCompanyName',
              firstName: 'mockBuyerFirstName',
              lastName: 'mockBuyerLastName',
              mobilePhone: '12345',
            },
            {
              contactId: 'mockBuyerContactId2',
              contactType: 0,
              companyName: 'mockBuyerCompanyName2',
              firstName: 'mockBuyerFirstName2',
              lastName: 'mockBuyerLastName2',
              mobilePhone: null,
            },
          ],
          proxies: [],
        },
      );
    });
    it('should call sendNotificationsIfFeatureFlagEnabled when new takeover date is today, but old takeover date was in the far past', async () => {
      const deps = getMockDependencies();
      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };

      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 3,
        brokersIdWithRoles,
        takeOverDate: sub(startOfDay(new Date()), { hours: 2 }), // takeover is today
        assignmentTypeGroup: 1,
        checkList: {
          checkListItems: [{ tags: ['HVITVASK_OPPGJOR'], value: 2, changedBy: 'test', changedDate: new Date() }],
          lastChanged: new Date(),
        },
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);

      (deps.mockOvertakeProtocolNotificationService.shouldCreateOtpForEstate as jest.Mock).mockReturnValue(true);
      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );
      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date();
      const existingOldEstate: Partial<EstateDocument> = {
        ...finalNewEstate,
        estateBaseType: EstateBaseType.DETACHED,
        takeOverDate: sub(startOfDay(sub(new Date(), { days: 10 })), { hours: 2 }), // takeover was 10 days ago
      };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.mockOvertakeProtocolNotificationService.sendNotificationsIfFeatureFlagEnabled).toHaveBeenCalledWith(
        finalNewEstate,
      );
    });

    it('should call sendNotificationsIfFeatureFlagEnabled when new takeover date is today, but old takeover date was in the far future', async () => {
      const deps = getMockDependencies();
      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };

      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 3,
        brokersIdWithRoles,
        takeOverDate: sub(startOfDay(new Date()), { hours: 2 }), // takeover is today
        assignmentTypeGroup: 1,
        checkList: {
          checkListItems: [{ tags: ['HVITVASK_OPPGJOR'], value: 2, changedBy: 'test', changedDate: new Date() }],
          lastChanged: new Date(),
        },
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);

      (deps.mockOvertakeProtocolNotificationService.shouldCreateOtpForEstate as jest.Mock).mockReturnValue(true);
      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );
      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date();
      const existingOldEstate: Partial<EstateDocument> = {
        ...finalNewEstate,
        estateBaseType: EstateBaseType.DETACHED,
        takeOverDate: sub(startOfDay(add(new Date(), { days: 10 })), { hours: 2 }), // takeover would be 10 days from now
      };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.mockOvertakeProtocolNotificationService.sendNotificationsIfFeatureFlagEnabled).toHaveBeenCalledWith(
        finalNewEstate,
      );
    });

    it('should call sendNotificationsIfFeatureFlagEnabled when new takeover date is today, but old takeover date was tomorrow, it may be a duplicated, because today morning we sent out due to its happenning tomorrow, but its important to renotify them being it today', async () => {
      const deps = getMockDependencies();
      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };

      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 3,
        brokersIdWithRoles,
        takeOverDate: sub(startOfDay(new Date()), { hours: 2 }), // takeover is today
        assignmentTypeGroup: 1,
        checkList: {
          checkListItems: [{ tags: ['HVITVASK_OPPGJOR'], value: 2, changedBy: 'test', changedDate: new Date() }],
          lastChanged: new Date(),
        },
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);

      (deps.mockOvertakeProtocolNotificationService.shouldCreateOtpForEstate as jest.Mock).mockReturnValue(true);
      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );
      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date();
      const existingOldEstate: Partial<EstateDocument> = {
        ...finalNewEstate,
        estateBaseType: EstateBaseType.DETACHED,
        takeOverDate: sub(startOfDay(add(new Date(), { days: 1 })), { hours: 2 }), // takeover was tomorrow
      };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.mockOvertakeProtocolNotificationService.sendNotificationsIfFeatureFlagEnabled).toHaveBeenCalledWith(
        finalNewEstate,
      );
    });

    it('should NOT call sendNotificationsIfFeatureFlagEnabled when new takeover date is today, but old takeover date was also today, because it was sent out yesterday morning', async () => {
      const deps = getMockDependencies();
      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };

      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 3,
        brokersIdWithRoles,
        takeOverDate: sub(startOfDay(new Date()), { hours: 2 }), // takeover is today
        assignmentTypeGroup: 1,
        checkList: {
          checkListItems: [{ tags: ['HVITVASK_OPPGJOR'], value: 2, changedBy: 'test', changedDate: new Date() }],
          lastChanged: new Date(),
        },
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);

      (deps.mockOvertakeProtocolNotificationService.shouldCreateOtpForEstate as jest.Mock).mockReturnValue(true);
      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );
      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date();
      const existingOldEstate: Partial<EstateDocument> = {
        ...finalNewEstate,
        estateBaseType: EstateBaseType.DETACHED,
      };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.mockOvertakeProtocolNotificationService.sendNotificationsIfFeatureFlagEnabled).not.toHaveBeenCalled();
    });

    it('should NOT call sendNotificationsIfFeatureFlagEnabled when new takeover date is tomorrow, but old takeover date was also tomorrow, because it was/will be sent out today morning at 7:55', async () => {
      const deps = getMockDependencies();
      (deps.mockAppConfigService.getDisableSqs as jest.Mock).mockReturnValue(true);
      const mainEmployeeWithoutId: Partial<EmployeeDocument> = {
        image: { small: 'bs', medium: 'bm', large: 'bl' },
        slug: 'bslug',
        name: 'bName',
        title: 'bTitle',
        mobilePhone: 'bPhone',
        workPhone: 'bWorkPhone',
        email: 'bEmail',
      };

      const department: Partial<DepartmentDocument> = {};
      (deps.departmentModel.findOne as jest.Mock).mockReturnValue(department);
      const brokersIdWithRoles = [
        {
          employeeId: 'mockEmployeeId',
          brokerRole: BrokerRole.MAIN_BROKER,
          employee: mainEmployeeWithoutId as EmployeeDocument,
        },
      ];
      const finalNewEstate: Partial<EstateDocument> = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 3,
        brokersIdWithRoles,
        takeOverDate: sub(endOfDay(new Date()), { hours: 2 }), // takeover is tomorrow
        assignmentTypeGroup: 1,
        checkList: {
          checkListItems: [{ tags: ['HVITVASK_OPPGJOR'], value: 2, changedBy: 'test', changedDate: new Date() }],
          lastChanged: new Date(),
        },
      };
      (deps.estateModel.findOne as jest.Mock).mockReturnValue(finalNewEstate);
      (deps.mockMongoService.upsert as jest.Mock).mockReturnValue(finalNewEstate);

      (deps.mockOvertakeProtocolNotificationService.shouldCreateOtpForEstate as jest.Mock).mockReturnValue(true);
      const estateSyncService = new EstateSyncService(
        deps.mockSchedulerRegistry,
        deps.mockVitecService,
        deps.mockMongoService,
        deps.mockSyncService,
        deps.estateService,
        deps.evService,
        deps.mockS3Service,
        deps.mockAuthorizationService,
        deps.mockAppConfigService,
        deps.mockUserNotifierService,
        deps.mockOvertakeProtocolNotificationService,
        deps.mockPoliticallyExposedPersonFormNotificationService,
        deps.mockSettlementSellerNotificationService,
        deps.mockSettlementBuyerNotificationService,
        deps.estateModel,
        deps.departmentModel,
        deps.employeeModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
        deps.contactModel,
      );
      const estate: Partial<EstateDto> = finalNewEstate as any as EstateDto;
      const syncReceiveDate: Date = new Date();
      const existingOldEstate: Partial<EstateDocument> = {
        ...finalNewEstate,
        estateBaseType: EstateBaseType.DETACHED,
      };
      const endpoint: EndpointType = EndpointType.Estates;

      await estateSyncService.upsertEstate({
        estate: estate as EstateDto,
        syncReceiveDate,
        existingOldEstate: existingOldEstate as EstateDocument,
        endpoint,
        _timeLogUniqueId: 'timeLogUuid',
      });

      expect(deps.mockOvertakeProtocolNotificationService.sendNotificationsIfFeatureFlagEnabled).not.toHaveBeenCalled();
    });
  });
});
