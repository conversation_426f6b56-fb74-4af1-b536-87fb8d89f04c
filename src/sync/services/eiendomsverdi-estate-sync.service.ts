import { ConsoleLogger, Injectable } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import _ from 'lodash';
import AppConfigService from '../../app-config/app-config.service';
import { EiendomsverdiService } from '../../eiendomsverdi/eiendomsverdi.service';
import { MapboxService } from '../../mapbox/mapbox.service';
import { Estate } from '../../pg/estate/estate.model';
import { EstateService } from '../../pg/estate/estate.service';
import { UserService } from '../../pg/user/user.service';
import { S3Service } from '../../s3/s3.service';
import UpdateEiendomsverdiEstatesJob from '../jobs/update-eiendomsverdi-estates.job';
import { SyncService } from '../sync.service';

@Injectable()
export class EiendomsverdiEstateSyncService {
  private readonly logger = new ConsoleLogger(EiendomsverdiEstateSyncService.name);

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly eiendomsverdiService: EiendomsverdiService,
    private readonly userService: UserService,
    private readonly estateService: EstateService,
    private readonly mapboxService: MapboxService,
    private readonly s3Service: S3Service,
    private readonly syncService: SyncService,
  ) {}

  /*
  @Cron(CronExpression.EVERY_DAY_AT_4AM, {
    name: EiendomsverdiEstateSyncService.name,
    timeZone: 'Europe/Oslo',
  })
  */
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(EiendomsverdiEstateSyncService.name);
    cronJob.stop();

    this.logger.log('Scheduled eiendomsverdi estates update triggered');

    const job = new UpdateEiendomsverdiEstatesJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`Scheduled eiendomsverdi estates update finished with result: ${result}`);
    cronJob.start();
  }

  private static getEVProps(estate: Partial<Estate>) {
    return {
      address: estate.address,
      landIdentificationMatrix: estate.landIdentificationMatrix,
      propertyType: estate.propertyType,
      numberOfBedrooms: estate.numberOfBedrooms,
      livingArea: estate.livingArea,
      buildYear: estate.buildYear,
      floor: estate.floor,
      ownership: estate.ownership,
      EVAddressID: estate.EVAddressID,
      EVEstateID: estate.EVEstateID,
    };
  }

  private isUpToDate(currentEstate: Partial<Estate>, newEstate: Partial<Estate>) {
    const currentEstateProps = EiendomsverdiEstateSyncService.getEVProps(currentEstate);
    const newEstateProps = EiendomsverdiEstateSyncService.getEVProps(newEstate);

    const isEqual = _.isEqual(currentEstateProps, newEstateProps);

    if (!isEqual) {
      const differentKeys = _.reduce(
        currentEstateProps,
        (result, value, key) => {
          return _.isEqual(value, (newEstateProps as any)[key]) ? result : result.concat(key as any);
        },
        [],
      );

      if (differentKeys.length > 0) {
        for (const differentKey of differentKeys) {
          this.logger.debug(
            `${currentEstate.EVEstateID};${currentEstate.EVAddressID};${differentKey};${currentEstate[differentKey]};${newEstate[differentKey]}`,
          );

          this.logger.log(
            `Setting ${currentEstate.address}'s ${differentKey} to ${newEstate[differentKey]} (was: ${currentEstate[differentKey]}) -> UPDATE ESTATE`,
          );
        }
      }
    }

    return isEqual;
  }

  async deleteSoldEstates() {
    this.logger.log('Deleting sold estates');

    const users = await this.userService.findAll();
    this.logger.debug(`There are ${users.length} users in the database`);

    for (const user of users) {
      const currentEstatesInOurDatabase = await this.estateService.findByUser(user);
      this.logger.debug(`There are ${currentEstatesInOurDatabase.length} estates in the database for user ${user.id}`);

      if (!user.name || !user.birthday) {
        this.logger.warn(`User "${user.name}" (${user.id}) has missing name/birth date -> CANNOT SYNC`);
        continue;
      }

      const currentEstatesInEiendomsverdi = await this.eiendomsverdiService.getOwnedEstatesByNameAndBirthDate(
        user.name,
        user.birthday,
      );

      for (const currentEstateInOurDatabase of currentEstatesInOurDatabase) {
        const isInEiendomsverdi = currentEstatesInEiendomsverdi.some(
          (estate) =>
            estate.estateId === currentEstateInOurDatabase.EVEstateID &&
            estate.addressId === currentEstateInOurDatabase.EVAddressID &&
            estate.organizationNumber === currentEstateInOurDatabase.OrganizationNumber &&
            estate.shareNumber === currentEstateInOurDatabase.ShareNumber,
        );

        if (!isInEiendomsverdi) {
          this.logger.log(
            `Estate ${currentEstateInOurDatabase.address} (${currentEstateInOurDatabase.EVEstateID},${currentEstateInOurDatabase.EVAddressID},${currentEstateInOurDatabase.OrganizationNumber},${currentEstateInOurDatabase.ShareNumber}) no longer belongs to user ${user.name} -> DELETE ESTATE`,
          );

          await this.estateService.delete({
            id: currentEstateInOurDatabase.id,
          });
        }
      }
    }
  }

  async getImageUrlByAddress(address: string): Promise<string | null> {
    try {
      const geoCode = await this.mapboxService.getGeoCodeFromAddress(address);
      if (!geoCode) {
        return null;
      }
      const image = await this.mapboxService.getImageFromGeoCode(geoCode);
      if (!image) {
        return null;
      }
      const imageUrl = await this.s3Service.uploadSingleImage({
        folder: 'eiendomsverdi-map-images',
        imgExt: 'png',
        body: image,
        bucket: this.appConfigService.getS3Bucket(),
        acl: 'public-read',
      });
      return imageUrl;
    } catch (e) {
      return null;
    }
  }

  async createBoughtEstates() {
    this.logger.log('Creating bought estates');

    const users = await this.userService.findAll();
    this.logger.debug(`There are ${users.length} users in the database`);

    for (const user of users) {
      const currentEstatesInOurDatabase = await this.estateService.findByUser(user);
      this.logger.debug(`There are ${currentEstatesInOurDatabase.length} estates in the database for user ${user.id}`);

      if (!user.name || !user.birthday) {
        this.logger.warn(`User "${user.name}" (${user.id}) has missing name/birth date -> CANNOT SYNC`);
        continue;
      }

      const currentEstatesInEiendomsverdi = await this.eiendomsverdiService.getOwnedEstatesByNameAndBirthDate(
        user.name,
        user.birthday,
      );

      for (const currentEstateInEiendomsverdi of currentEstatesInEiendomsverdi) {
        const isInOurDatabase = currentEstatesInOurDatabase.some(
          (estate) =>
            estate.EVEstateID === currentEstateInEiendomsverdi.estateId &&
            estate.EVAddressID === currentEstateInEiendomsverdi.addressId &&
            estate.OrganizationNumber === currentEstateInEiendomsverdi.organizationNumber &&
            estate.ShareNumber === currentEstateInEiendomsverdi.shareNumber,
        );

        if (!isInOurDatabase) {
          const newEstate = await this.eiendomsverdiService.getEstate(
            user,
            currentEstateInEiendomsverdi.addressId,
            currentEstateInEiendomsverdi.estateId,
            currentEstateInEiendomsverdi.organizationNumber,
            currentEstateInEiendomsverdi.shareNumber,
          );

          await this.estateService.create(user, {
            ...newEstate,
            imageUrl: await this.getImageUrlByAddress(newEstate.address),
          });

          this.logger.log(
            `User ${user.name} has new estate ${newEstate.address} (${newEstate.EVEstateID},${newEstate.EVAddressID},${newEstate.OrganizationNumber},${newEstate.ShareNumber}) -> CREATE ESTATE`,
          );
        }
      }
    }
  }

  async updateExistingEstates(): Promise<void> {
    this.logger.log('Updating existing estates');

    const users = await this.userService.findAll();
    this.logger.debug(`There are ${users.length} users in the database`);

    for (const user of users) {
      const currentEstatesInOurDatabase = await this.estateService.findByUser(user);
      this.logger.debug(`There are ${currentEstatesInOurDatabase.length} estates in the database for user ${user.id}`);

      for (const currentEstateInOurDatabase of currentEstatesInOurDatabase) {
        const updatedEstate = await this.eiendomsverdiService.getEstate(
          user,
          currentEstateInOurDatabase.EVAddressID,
          currentEstateInOurDatabase.EVEstateID,
          currentEstateInOurDatabase.OrganizationNumber,
          currentEstateInOurDatabase.ShareNumber,
        );

        if (!updatedEstate) {
          this.logger.debug(`Estate ${currentEstateInOurDatabase.id} is no longer in Eiendomsverdi`);
          continue;
        }

        if (this.isUpToDate(currentEstateInOurDatabase, updatedEstate)) {
          this.logger.debug(`Estate ${currentEstateInOurDatabase.id} is up to date`);
          continue;
        }

        this.logger.debug(`Updating estate ${currentEstateInOurDatabase.id}`);
        await this.estateService.update(currentEstateInOurDatabase.id, updatedEstate);
      }
    }
  }
}
