import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { MongoService } from '../../mongo/mongo.service';
import { VitecService } from '../../vitec/vitec.service';
import UpdateTipsJob from '../jobs/update-tips.job';
import { Contact, ContactDocument } from '../schema/contact.schema';
import { SyncState } from '../schema/sync-state';
import { Tip, TipDocument } from '../schema/tip.schema';
import { SyncService } from '../sync.service';

@Injectable()
export class TipSyncService {
  private readonly logger = new ConsoleLogger(TipSyncService.name);

  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly vitecService: VitecService,
    private readonly mongoService: MongoService,
    private readonly syncService: SyncService,
    @InjectModel(Tip.name) private tipModel: Model<TipDocument>,
    @InjectModel(Contact.name) private contactModel: Model<ContactDocument>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_3AM, {
    name: UpdateTipsJob.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(UpdateTipsJob.name);
    cronJob.stop();

    this.logger.log('Scheduled tip update triggered');

    const job = new UpdateTipsJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`Scheduled tip update finished with result: ${result}`);
    cronJob.start();
  }

  async updateTips(syncState: SyncState | null): Promise<void> {
    this.logger.log('Updating tips');

    const tips = await this.vitecService.getTips(syncState ? syncState.lastSuccessfulSyncDate : new Date());

    this.logger.debug(`There are ${tips.tips.length} tip origin types`);

    await this.mongoService.runInTransaction(async () => {
      for (const originTypeTip of tips.tips) {
        this.logger.debug(`There are ${originTypeTip.tips.length} tips for origin type ${originTypeTip.originType}`);

        for (const tip of originTypeTip.tips) {
          const tipObject = {
            ...tip,
            originationType: originTypeTip.originType,
          };

          if (tipObject.contactId) {
            const contact = await this.vitecService.getContact(tipObject.contactId);

            await this.mongoService.upsert(this.contactModel, { contactId: contact.contactId }, contact);
          }

          await this.mongoService.upsert(this.tipModel, { tipId: tipObject.tipId }, { ...tipObject });
        }
      }
    });
  }
}
