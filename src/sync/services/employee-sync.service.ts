import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { withFile } from 'tmp-promise';
import { MongoService } from '../../mongo/mongo.service';
import { S3Service } from '../../s3/s3.service';
import { generateSlug } from '../../utils/slug.utils';
import EmployeeDto from '../../vitec/dto/employee.dto';
import { VitecService } from '../../vitec/vitec.service';
import UpdateEmployeesJob from '../jobs/update-employees.job';
import { Department, DepartmentDocument } from '../schema/department.schema';
import { Employee, EmployeeDocument } from '../schema/employee.schema';
import { SyncState } from '../schema/sync-state';
import { SyncService } from '../sync.service';

function hasImage(employee: EmployeeDto) {
  return !!employee.imageTimestamp;
}

function shouldUpdateImage(employee: EmployeeDto, syncState: SyncState | null) {
  return !syncState || employee.imageTimestamp >= syncState.lastSuccessfulSyncDate;
}

@Injectable()
export class EmployeeSyncService {
  private readonly logger = new ConsoleLogger(EmployeeSyncService.name);

  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly vitecService: VitecService,
    private readonly mongoService: MongoService,
    private readonly syncService: SyncService,
    private readonly s3Service: S3Service,
    @InjectModel(Department.name) private departmentModel: Model<DepartmentDocument>,
    @InjectModel(Employee.name) private employeeModel: Model<EmployeeDocument>,
  ) {}

  @Cron(CronExpression.EVERY_HOUR, {
    name: UpdateEmployeesJob.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(UpdateEmployeesJob.name);
    cronJob.stop();

    this.logger.log('Scheduled employee update triggered');

    const job = new UpdateEmployeesJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`Scheduled employee update finished with result: ${result}`);
    cronJob.start();
  }

  private async updateImage(employee: EmployeeDto, syncState: SyncState | null): Promise<void> {
    if (hasImage(employee)) {
      if (shouldUpdateImage(employee, syncState)) {
        await withFile(async ({ path }) => {
          await this.vitecService.downloadEmployeeImage(employee.employeeId, path);

          const image = await this.s3Service.uploadEmployeeImagesToS3(path, generateSlug(employee.name));

          await this.mongoService.upsert(
            this.employeeModel,
            {
              employeeId: employee.employeeId,
            },
            {
              image,
            },
          );
        });
      }
    } else {
      await this.mongoService.upsert(
        this.employeeModel,
        {
          employeeId: employee.employeeId,
        },
        {
          image: {
            small: null,
            medium: null,
            large: null,
          },
        },
      );
    }
  }

  async updateEmployee(employee: EmployeeDto, syncState: SyncState | null): Promise<void> {
    this.logger.debug(`Updating employee ${employee.employeeId}`);

    if (employee.departmentId.length !== 1) {
      this.logger.warn(
        `Employee ${employee.employeeId} has multiple departments associated: ${JSON.stringify(employee.departmentId)}`,
      );
    }

    const departmentId = employee.departmentId[0];

    const department = await this.departmentModel.findOne({
      departmentId,
    });

    if (!department) {
      this.logger.warn(`Department ${departmentId} not found for employee ${employee.employeeId} in our database`);
    }

    await this.mongoService.upsert(
      this.employeeModel,
      {
        employeeId: employee.employeeId,
      },
      {
        departmentId: employee.departmentId,
        employeeId: employee.employeeId,
        title: employee.title,
        name: employee.name,
        mobilePhone: employee.mobilePhone,
        workPhone: employee.workPhone,
        email: employee.email,
        employeeActive: employee.employeeActive,
        changedDate: employee.changedDate,
        department,
        aboutMe: employee.aboutMe,
        student: employee.student,
        webPublish: employee.webPublish,
        imageTimestamp: employee.imageTimestamp,
        slug: generateSlug(employee.name),
      },
    );

    await this.updateImage(employee, syncState);
  }

  async updateEmployees(syncState: SyncState | null): Promise<void> {
    this.logger.log('Updating employees');

    const employees = await this.vitecService.getEmployees();

    const changedEmployees = syncState
      ? employees.filter(
          (employee) =>
            employee.changedDate >= syncState.lastSuccessfulSyncDate ||
            employee.imageTimestamp >= syncState.lastSuccessfulSyncDate,
        )
      : employees;

    await this.mongoService.runInTransaction(async () => {
      for (const employee of changedEmployees) {
        await this.updateEmployee(employee, syncState);
      }
    });
  }
}
