import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import urljoin from 'url-join';
import AppConfigService from '../../../app-config/app-config.service';
import { SettlementBuyerService } from '../../../pg/settlement-buyer/settlement-buyer.service';
import { EstateBaseType } from '../../schema/estate.schema';
import {
  autoFinalizeFunctionFactory,
  getTimesHalfHourAgoTillIDFYDocumentValidity,
} from './auto-finalize-function.factory';

@Injectable()
export class SettlementBuyerFormAutoFinalizationService {
  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly settlementBuyerService: SettlementBuyerService,
    private readonly logger: ConsoleLogger,
  ) {
    this.logger.setContext(SettlementBuyerFormAutoFinalizationService.name);
  }

  private readonly client = new IdfyClient(this.appConfigService.getIdfyId(), this.appConfigService.getIdfySecret(), [
    'document_read',
    'document_file',
    'document_write',
  ]);

  private generateUrl = (estateVitecId, isProject = false) =>
    urljoin(
      this.appConfigService.getBackendUrl(),
      '/estate',
      estateVitecId,
      '/finalize-settlement-buyer',
      isProject ? `?estateBaseType=${EstateBaseType.PROJECT}` : '',
    );

  private autoFinalize = autoFinalizeFunctionFactory({ logger: this.logger, idfyClient: this.client });

  @Cron(CronExpression.EVERY_30_MINUTES, {
    name: SettlementBuyerFormAutoFinalizationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('SettlementBuyerForm auto-finalization started');
    await this.trigger();
    this.logger.log('SettlementBuyerForm auto-finalization finished');
  }

  async trigger(): Promise<void> {
    const { startDate, endDate } = getTimesHalfHourAgoTillIDFYDocumentValidity();

    const settlementBuyerForms = await this.settlementBuyerService.findAllUnfinishedBetweenSignStartDates(
      startDate,
      endDate,
    );
    for (const settlementBuyerForm of settlementBuyerForms) {
      await this.autoFinalize({
        estateVitecId: settlementBuyerForm.estateVitecId,
        idfyDocumentId: settlementBuyerForm.idfyDocumentId,
        signingFinished: settlementBuyerForm.signingFinished,
        signingStarted: settlementBuyerForm.signingStarted,
        finalizeUrl: this.generateUrl(
          settlementBuyerForm.estateVitecId,
          settlementBuyerForm.estateBaseType === EstateBaseType.PROJECT,
        ),
        entityName: 'SettlementBuyerForm',
      });
    }
  }
}
