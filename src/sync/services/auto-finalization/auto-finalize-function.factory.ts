import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger } from '@nestjs/common';
import axios from 'axios';
import { sub } from 'date-fns';

type AutoFinalizeFunction = (params: {
  idfyDocumentId: string;
  finalizeUrl: string;
  signingStarted: Date;
  signingFinished: Date;
  estateVitecId: string;
  entityName: string;
  beforeFinalizeApiCallHook?: () => Promise<void>;
}) => Promise<void>;

type AutoFinalizeFunctionFactory = (deps: { logger: ConsoleLogger; idfyClient: IdfyClient }) => AutoFinalizeFunction;

export const autoFinalizeFunctionFactory: AutoFinalizeFunctionFactory =
  ({ logger, idfyClient }) =>
  async ({
    idfyDocumentId,
    signingStarted,
    signingFinished,
    finalizeUrl,
    estateVitecId,
    entityName,
    beforeFinalizeApiCallHook,
  }) => {
    if (!idfyDocumentId) {
      logger.warn(
        `${entityName} for estate ${estateVitecId} has started signing at ${signingStarted}, but has no document`,
      );
      return;
    }

    if (signingFinished) {
      logger.log(`${entityName} for estate ${estateVitecId} signing already finished`);
      return;
    }

    try {
      const signers = await idfyClient.signature.listSigners(idfyDocumentId);
      if (!signers.length) {
        logger.error(
          `Signers were not found for estate: ${estateVitecId}, document id in idfy: ${idfyDocumentId}, likely reason is that SigningStarted date is older than 186 hours and IDFY deletes the document file`,
        );
      }
      const signersWhoDidNotSign = signers.filter((s) => !s.documentSignature);
      if (signersWhoDidNotSign.length) {
        logger.log(
          `${entityName} for estate ${estateVitecId} document is not yet signed by all participants, Postgres ids of them: ${signersWhoDidNotSign
            .map((s) => s.externalSignerId) // this is what we provided to connect them to our DB representation
            .join(', ')}`,
        );
        return;
      }
    } catch (error) {
      logger.error('IdfyClient responded with an error', error);
      return;
    }

    logger.log(
      `${entityName} for estate ${estateVitecId} has started signing, but is not yet finished. Trying to auto-finalize it`,
    );
    if (beforeFinalizeApiCallHook) {
      await beforeFinalizeApiCallHook();
    }
    const response = await axios.post(finalizeUrl, {}, { validateStatus: () => true });
    if (response.status !== 201) {
      logger.warn(
        `${entityName} for estate ${estateVitecId}, auto-finishing failed with: ${response.status}, ${response.data}`,
      );
      return;
    }
    logger.log(`${entityName} for estate ${estateVitecId} has auto-finalization finished`);
  };

export const getTimesHalfHourAgoTillIDFYDocumentValidity = (): { startDate: Date; endDate: Date } => {
  const endDate = sub(new Date(), { minutes: 30 }); // only try finishing forms that are in signing stage for at least 30 mins
  const startDate = sub(new Date(), { hours: 167 }); // Signicat/idfy only stores documents for 168 hours
  return { startDate, endDate };
};
