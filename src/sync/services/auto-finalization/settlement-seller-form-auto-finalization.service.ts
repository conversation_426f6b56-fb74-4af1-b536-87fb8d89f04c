import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import urljoin from 'url-join';
import AppConfigService from '../../../app-config/app-config.service';
import { SettlementSellerService } from '../../../pg/settlement-seller/settlement-seller.service';
import {
  autoFinalizeFunctionFactory,
  getTimesHalfHourAgoTillIDFYDocumentValidity,
} from './auto-finalize-function.factory';

@Injectable()
export class SettlementSellerFormAutoFinalizationService {
  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly settlementSellerService: SettlementSellerService,
    private readonly logger: ConsoleLogger,
  ) {
    this.logger.setContext(SettlementSellerFormAutoFinalizationService.name);
  }

  private readonly client = new IdfyClient(this.appConfigService.getIdfyId(), this.appConfigService.getIdfySecret(), [
    'document_read',
    'document_file',
    'document_write',
  ]);

  private generateUrl = ({ estateVitecId }) =>
    urljoin(this.appConfigService.getBackendUrl(), '/estate', estateVitecId, '/finalize-settlement-seller');

  private autoFinalize = autoFinalizeFunctionFactory({ logger: this.logger, idfyClient: this.client });

  @Cron(CronExpression.EVERY_30_MINUTES, {
    name: SettlementSellerFormAutoFinalizationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('SettlementSellerForm auto-finalization started');
    await this.trigger();
    this.logger.log('SettlementSellerForm auto-finalization finished');
  }

  async trigger(): Promise<void> {
    const { startDate, endDate } = getTimesHalfHourAgoTillIDFYDocumentValidity();

    const settlementSellerForms = await this.settlementSellerService.findAllUnfinishedBetweenSignStartDates(
      startDate,
      endDate,
    );
    for (const settlementSellerForm of settlementSellerForms) {
      await this.autoFinalize({
        estateVitecId: settlementSellerForm.estateVitecId,
        idfyDocumentId: settlementSellerForm.idfyDocumentId,
        signingFinished: settlementSellerForm.signingFinished,
        signingStarted: settlementSellerForm.signingStarted,
        finalizeUrl: this.generateUrl(settlementSellerForm),
        entityName: 'SettlementSellerForm',
      });
    }
  }
}
