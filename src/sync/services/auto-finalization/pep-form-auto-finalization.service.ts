import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import urljoin from 'url-join';
import AppConfigService from '../../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../../authorization/authorization.service';
import { PEPFormService } from '../../../pg/pep-form/pep-form.service';
import {
  autoFinalizeFunctionFactory,
  getTimesHalfHourAgoTillIDFYDocumentValidity,
} from './auto-finalize-function.factory';

@Injectable()
export class PEPFormAutoFinalizationService {
  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly pepFormService: PEPFormService,
    private readonly logger: ConsoleLogger,
    private readonly authService: AuthorizationService,
  ) {
    this.logger.setContext(PEPFormAutoFinalizationService.name);
  }

  private readonly client = new IdfyClient(this.appConfigService.getIdfyId(), this.appConfigService.getIdfySecret(), [
    'document_read',
    'document_file',
    'document_write',
  ]);

  private generateUrl = ({ estateVitecId }) =>
    urljoin(this.appConfigService.getBackendUrl(), '/estate', estateVitecId, '/pep/finalize');

  private autoFinalize = autoFinalizeFunctionFactory({ logger: this.logger, idfyClient: this.client });

  @Cron(CronExpression.EVERY_30_MINUTES, {
    name: PEPFormAutoFinalizationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    if (!this.authService.isFeatureEnabled(FeatureFlag.AutoFinalizePep)) {
      this.logger.warn(`PEP Form auto-finalization is disabled because of this FF: ${FeatureFlag.AutoFinalizePep}`);
      return;
    }
    this.logger.log('PEP Form auto-finalization started');
    await this.trigger();
    this.logger.log('PEP Form auto-finalization finished');
  }

  async trigger(): Promise<void> {
    const { startDate, endDate } = getTimesHalfHourAgoTillIDFYDocumentValidity();

    const pepForms = await this.pepFormService.findAllUnfinishedBetweenSignStartDates(startDate, endDate);
    for (const pepForm of pepForms) {
      await this.autoFinalize({
        estateVitecId: pepForm.estateVitecId,
        idfyDocumentId: pepForm.idfyDocumentId,
        signingFinished: pepForm.signingFinished,
        signingStarted: pepForm.signingStarted,
        finalizeUrl: this.generateUrl(pepForm),
        entityName: 'PEPForm',
      });
    }
  }
}
