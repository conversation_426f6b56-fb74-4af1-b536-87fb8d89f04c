import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import urljoin from 'url-join';
import AppConfigService from '../../../app-config/app-config.service';
import { OvertakeProtocolService } from '../../../pg/overtake-protocol/overtake-protocol.service';
import AutoFinishOtpsJob from '../../jobs/auto-finish-otps.job';
import { SyncService } from '../../sync.service';
import {
  autoFinalizeFunctionFactory,
  getTimesHalfHourAgoTillIDFYDocumentValidity,
} from './auto-finalize-function.factory';

@Injectable()
export class OtpAutoFinalizationService {
  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly appConfigService: AppConfigService,
    private readonly otpService: OvertakeProtocolService,
    private readonly syncService: SyncService,
    private readonly logger: ConsoleLogger,
  ) {
    this.logger.setContext(OtpAutoFinalizationService.name);
  }

  private readonly client = new IdfyClient(this.appConfigService.getIdfyId(), this.appConfigService.getIdfySecret(), [
    'document_read',
    'document_file',
    'document_write',
  ]);

  private generateUrl = ({ estateVitecId }) =>
    urljoin(this.appConfigService.getBackendUrl(), '/estate', estateVitecId, '/finish-otp');

  private autoFinalize = autoFinalizeFunctionFactory({ logger: this.logger, idfyClient: this.client });

  @Cron(CronExpression.EVERY_30_MINUTES, {
    name: AutoFinishOtpsJob.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(AutoFinishOtpsJob.name);
    cronJob.stop();

    this.logger.log('OTP auto-finalization started');

    const job = new AutoFinishOtpsJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`OTP auto-finalization ended with ${result}`);
    cronJob.start();
  }

  async autoFinalizeOtps(): Promise<void> {
    const { startDate, endDate } = getTimesHalfHourAgoTillIDFYDocumentValidity();

    const allOtpsStartedSigningHalfHourAgo = await this.otpService.findAllUnfinishedBetweenSignStartDates(
      startDate,
      endDate,
    );

    for (const otp of allOtpsStartedSigningHalfHourAgo) {
      await this.autoFinalize({
        estateVitecId: otp.estateVitecId,
        idfyDocumentId: otp.idfyDocumentId,
        signingFinished: otp.signingFinished,
        signingStarted: otp.signingStarted,
        finalizeUrl: this.generateUrl(otp),
        entityName: 'OvertakeProtocol',
        beforeFinalizeApiCallHook: async () => {
          await this.otpService.unlockOtp(otp.id);
        },
      });
    }
  }
}
