import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { SQS } from 'aws-sdk';
import { Promise as PromiseBB } from 'bluebird';
import { add, isToday, isTomorrow } from 'date-fns';
import https from 'https';
import _, { omit, uniqBy } from 'lodash';
import { Model, UpdateWriteOpResult } from 'mongoose';
import { flatten } from 'ramda';
import { Consumer } from 'sqs-consumer';
import { Producer } from 'sqs-producer';
import urljoin from 'url-join';
import { v4 } from 'uuid';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { EiendomsverdiService } from '../../eiendomsverdi/eiendomsverdi.service';
import { MongoService } from '../../mongo/mongo.service';
import { OvertakeProtocolNotificationService } from '../../notification/services/overtake-protocol-notification.service';
import { PoliticallyExposedPersonFormNotificationService } from '../../notification/services/politically-exposed-person-form-notification.service';
import { SettlementBuyerNotificationService } from '../../notification/services/settlement-buyer-notification.service';
import { SettlementSellerNotificationService } from '../../notification/services/settlement-seller-notification.service';
import { UserNotifierService } from '../../notification/user-notifier.service';
import { Estate as EstateEV } from '../../pg/estate/estate.model';
import { EstateService } from '../../pg/estate/estate.service';
import { S3Service } from '../../s3/s3.service';
import { chainByKey } from '../../utils/promise.utils';
import EstateDto from '../../vitec/dto/estate.dto';
import { VitecService } from '../../vitec/vitec.service';
import UpdateEstatesJob from '../jobs/update-estates.job';
import { Buyer, BuyerDocument } from '../schema/buyer.schema';
import { Contact, ContactDocument } from '../schema/contact.schema';
import { Department, DepartmentDocument } from '../schema/department.schema';
import { Employee, EmployeeDocument } from '../schema/employee.schema';
import {
  BrokerRole,
  Estate,
  EstateBaseType,
  EstateBrokerIdWithRolesNested,
  EstateDocument,
} from '../schema/estate.schema';
import { Proxy, ProxyDocument } from '../schema/proxy.schema';
import { Seller, SellerDocument } from '../schema/seller.schema';
import { SyncState } from '../schema/sync-state';
import { SyncService } from '../sync.service';

type EstatePrice = {
  priceSuggestion: number;
  soldPrice: number;
  estimatedValue: number;
  communityTax: number;
  communityTaxYear: number;
  salesCostDescription: string;
  changedDate: Date;
};

export enum EndpointType {
  Estates = 'Estates',
  ContactRelations = 'ContactRelations',
  Bids = 'Bids',
  ContactInformation = 'ContactInformation',
  Activities = 'Activities',
  CheckList = 'CheckList',
  AccountingEstates = 'AccountingEstates',
  All = 'All',
}

const compareEstatePrice = (newEstatePrice: any, oldEstatePrice: any): boolean => {
  if (!oldEstatePrice || !newEstatePrice) {
    return false;
  }

  return _.isEqual(toEstatePriceComparable(newEstatePrice), toEstatePriceComparable(oldEstatePrice));
};

const toEstatePriceComparable = (estatePrice: any): Partial<EstatePrice> => ({
  priceSuggestion: estatePrice.priceSuggestion,
  soldPrice: estatePrice.soldPrice,
  estimatedValue: estatePrice.estimatedValue,
  communityTax: estatePrice.communityTax,
  communityTaxYear: estatePrice.communityTaxYear,
  salesCostDescription: estatePrice.salesCostDescription,
});

export function isTakeOverDateTodayOrTomorrow(estate?: Estate): boolean {
  return (
    (estate && isToday(add(estate.takeOverDate, { hours: 2 }))) || isTomorrow(add(estate.takeOverDate, { hours: 2 }))
  );
}

@Injectable()
export class EstateSyncService {
  private readonly logger = new ConsoleLogger(EstateSyncService.name);

  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly vitecService: VitecService,
    private readonly mongoService: MongoService,
    private readonly syncService: SyncService,
    private readonly estateService: EstateService,
    private readonly evService: EiendomsverdiService,
    private readonly s3Service: S3Service,
    private readonly authorizationService: AuthorizationService,
    private readonly appConfigService: AppConfigService,
    private readonly userNotifierService: UserNotifierService,
    private readonly overtakeProtocolNotificationService: OvertakeProtocolNotificationService,
    private readonly politicallyExposedPersonFormNotificationService: PoliticallyExposedPersonFormNotificationService,
    private readonly settlementSellerNotificationService: SettlementSellerNotificationService,
    private readonly settlementBuyerNotificationService: SettlementBuyerNotificationService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Department.name) private departmentModel: Model<DepartmentDocument>,
    @InjectModel(Employee.name) private employeeModel: Model<EmployeeDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
    @InjectModel(Proxy.name) private proxyModel: Model<ProxyDocument>,
    @InjectModel(Contact.name) private contactModel: Model<ContactDocument>,
  ) {
    if (!this.appConfigService.getDisableSqs()) {
      void this.subscribeConsumer(this.appConfigService.getSqsUrl()).then(() =>
        this.logger.log('SQS Consumer registered'),
      );
    }
  }

  private readonly producer = this.appConfigService.getDisableSqs()
    ? null
    : Producer.create({
        queueUrl: this.appConfigService.getSqsUrl(),
        accessKeyId: this.appConfigService.getS3AccessKeyId(),
        secretAccessKey: this.appConfigService.getS3SecretAccessKey(),
      } as any); //supporting all opts as per the API docs, but not in type, so as any

  @Cron(CronExpression.EVERY_4_HOURS, {
    name: UpdateEstatesJob.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(UpdateEstatesJob.name);
    cronJob.stop();

    this.logger.log('Scheduled estate update triggered');

    const job = new UpdateEstatesJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`Scheduled estate update finished with result: ${result}`);
    cronJob.start();
  }

  private handleSQSMessage = async (sqsMessage: SQS.Message) => {
    const estateId = sqsMessage.Body;
    const endpoint = (sqsMessage.MessageAttributes?.endpoint?.StringValue || EndpointType.All) as EndpointType;

    if (!estateId) {
      this.logger.error('Received queue entry does not have a corresponding estate ID');
      return;
    }

    const vitecEventDate = sqsMessage.MessageAttributes?.changeDate?.StringValue;
    if (!vitecEventDate) {
      this.logger.warn(
        `Did not receive vitec event date for estate ${estateId}, race condition detection for sync messages may be flawed.`,
      );
    }

    const _timeLogUniqueId = v4();
    console.time(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`);

    const syncReceiveDate = vitecEventDate ? new Date(vitecEventDate) : new Date();

    const oldEstate = await this.estateModel.findOne({
      estateId,
    });
    if (
      !!oldEstate?.lastSuccessfulSyncStartDate &&
      oldEstate.lastSuccessfulSyncStartDate > syncReceiveDate &&
      (endpoint === EndpointType.All || endpoint === EndpointType.Estates)
    ) {
      this.logger.warn(
        `Skipping estate ${
          oldEstate.estateId
        } because its last successful sync start date (${oldEstate.lastSuccessfulSyncStartDate.toISOString()}) is greater than the sync receive date (${syncReceiveDate.toISOString()})`,
      );
      return;
    }

    const estate = await this.vitecService.getEstate(estateId).catch(() => null);
    if (!estate) {
      this.logger.error(`Estate not found: ${estateId}`);
      return;
    }
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, 'After getting estate from Vitec');

    await this.upsertEstate({ estate, syncReceiveDate, existingOldEstate: oldEstate, endpoint, _timeLogUniqueId });
    await this.updateOffsetOnEVEstate(estate, oldEstate, endpoint, _timeLogUniqueId);
    console.timeEnd(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`);
  };

  private async subscribeConsumer(queueUrl: string) {
    const app = Consumer.create({
      queueUrl,
      messageAttributeNames: ['endpoint', 'changeDate'],
      attributeNames: ['All'],
      handleMessage: this.handleSQSMessage,
      handleMessageBatch: async (sqsBatchMessage) => {
        const deduplicationFunction = (m: SQS.Message) =>
          `${m.Body}-${m.MessageAttributes?.endpoint?.StringValue || 'All'}`;
        const bodies = sqsBatchMessage.map(deduplicationFunction).join(', ');
        this.logger.log(`SQS message batch received (${sqsBatchMessage.length}): ${bodies}`);
        const uniqMessages = uniqBy(sqsBatchMessage, deduplicationFunction);

        await chainByKey(
          uniqMessages,
          (m) => m.Body,
          (m) => this.handleSQSMessage(m),
        );
      },
      batchSize: 8,
      sqs: new SQS({
        httpOptions: {
          agent: new https.Agent({
            keepAlive: true,
          }),
        },
      }),
    });
    app.on('error', (err) => {
      this.logger.error(`SQS consumer error: ${err.message}, stack: ${err.stack}`);
    });
    app.on('processing_error', (err) => {
      this.logger.error(`SQS consumer error: ${err.message}, stack: ${err.stack}`);
    });
    app.on('timeout_error', (err) => {
      this.logger.error(`SQS consumer error: ${err.message}, stack: ${err.stack}`);
    });
    app.on('message_received', (msg) => {
      this.logger.log(`SQS message received: ${msg.Body}`);
    });
    app.on('message_processed', (msg) => {
      this.logger.log(`SQS message processed: ${msg.Body}`);
    });
    app.on('empty', () => {
      this.logger.log(`SQS is empty`);
    });
    app.start();
  }

  async updateOffsetOnEVEstate(
    vitecEstate: EstateDto,
    oldEstate: EstateDocument,
    endpointType: EndpointType,
    _timeLogUniqueId: string,
  ): Promise<void> {
    this.logger.log(`Updating offset on vitec estate: ${vitecEstate.estateId}`);
    console.timeLog(`TimeHandleSQS ${vitecEstate.estateId} ${_timeLogUniqueId}`, 'Before updating estate offset');
    const noPriceChangeInVitec = vitecEstate.estatePrice.priceSuggestion === oldEstate?.estatePrice.priceSuggestion;
    if (noPriceChangeInVitec || !vitecEstate.estatePrice.priceSuggestion) {
      this.logger.log(
        `Skipping offset update on estate: (${vitecEstate.estateId}): noPriceChange: ${noPriceChangeInVitec}, priceSuggestion: old: ${oldEstate?.estatePrice.priceSuggestion} / new: ${vitecEstate.estatePrice.priceSuggestion}, endpointType: ${endpointType}`,
      );
      return;
    }
    try {
      if (!vitecEstate.matrikkel.length) {
        this.logger.log(`No landIdMatrix on vitecEstate: ${vitecEstate.estateId}`);
        return;
      }

      const landIdentificationMatrix = EstateService.getEVIdentificationMatrixFromVitec(vitecEstate.matrikkel);
      const evEstate = await this.estateService.findByLandIdentificationMatrix(
        landIdentificationMatrix,
        vitecEstate.partOwnership.partNumber,
      );
      if (!evEstate) {
        this.logger.log(`EV estate not found with landIdMatrix: ${landIdentificationMatrix}`);
        return;
      }
      console.timeLog(`TimeHandleSQS ${vitecEstate.estateId} ${_timeLogUniqueId}`, 'After getting EV estate');

      const estimationOffset = await this.calculateEstimationOffset(vitecEstate.estatePrice.priceSuggestion, evEstate);
      console.timeLog(
        `TimeHandleSQS ${vitecEstate.estateId} ${_timeLogUniqueId}`,
        'After estimation offset calculation',
      );

      if (!estimationOffset) {
        this.logger.error(`No valuation for EV estate with landIdMatrix: ${landIdentificationMatrix}`);
        return;
      }
      this.logger.log(`Updating estimationOffset on EV Estate: ${evEstate.id} with ${estimationOffset}`);
      await this.estateService.update(evEstate.id, {
        estimationOffset,
      });
      console.timeLog(`TimeHandleSQS ${vitecEstate.estateId} ${_timeLogUniqueId}`, 'After updated estimation offset');
    } catch (error) {
      this.logger.log(`Estimation offet update failed with the follwoing error: ${error}`);
    }
  }

  private async calculateEstimationOffset(priceSuggestion: number, estate: EstateEV): Promise<number | null> {
    const evEstateEvaluation = await this.evService.getEstateEvaluation(estate.landIdentificationMatrix, {
      organizationNumber: estate.OrganizationNumber,
      shareNumber: estate.ShareNumber,
    });
    if (!evEstateEvaluation || evEstateEvaluation.valuation) {
      return null;
    }
    return priceSuggestion - evEstateEvaluation.valuation;
  }

  private getMainEmployeeId(estate: EstateDto): string | null {
    const brokersWithAppropriateRole = estate.brokersIdWithRoles.find((broker) => {
      return broker.brokerRole === BrokerRole.MAIN_BROKER;
    });

    if (!brokersWithAppropriateRole) {
      return null;
    }

    return brokersWithAppropriateRole.employeeId;
  }

  private async getBrokersIdWithRoles(
    estate: EstateDto,
    _timeLogUniqueId: string,
  ): Promise<EstateBrokerIdWithRolesNested[]> {
    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[getBrokersIdWithRoles] Start');

    const brokersIdWithRoles = await Promise.all(
      estate.brokersIdWithRoles
        .filter((broker) => broker.brokerRole !== BrokerRole.RESPONSIBLE_SETTLEMENT)
        .map(async (broker) => {
          const brokerEmployee = await this.employeeModel.findOne({
            employeeId: broker.employeeId,
          });

          if (!brokerEmployee) {
            this.logger.warn(
              `Broker with employee id ${broker.employeeId} is not found in our database for estate ${estate.estateId}`,
            );

            return null;
          }

          return {
            employeeId: brokerEmployee.employeeId,
            brokerRole: broker.brokerRole,
            employee: {
              image: brokerEmployee.image,
              slug: brokerEmployee.slug,
              name: brokerEmployee.name,
              title: brokerEmployee.title,
              mobilePhone: brokerEmployee.mobilePhone,
              workPhone: brokerEmployee.workPhone,
              email: brokerEmployee.email,
            },
          };
        }),
    );

    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[getBrokersIdWithRoles] End');

    return brokersIdWithRoles.filter((employee) => employee !== null);
  }

  private async updateEstatePrice(newEstate: EstateDto, oldEstate: EstateDocument, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${newEstate.estateId} ${_timeLogUniqueId}`, '[updateEstatePrice] Start');

    if (!compareEstatePrice(newEstate.estatePrice, oldEstate?.estatePrice)) {
      await this.mongoService.upsert(
        this.estateModel,
        { estateId: newEstate.estateId },
        {
          estatePrice: newEstate.estatePrice
            ? {
                ...newEstate.estatePrice,
                changedDate: new Date(),
              }
            : null,
        },
      );
    }
    console.timeLog(`TimeHandleSQS ${newEstate.estateId} ${_timeLogUniqueId}`, '[updateEstatePrice] End');
  }

  private async updateEstateStatus(newEstate: EstateDto, oldEstate: EstateDocument, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${newEstate.estateId} ${_timeLogUniqueId}`, '[updateEstateStatus] Start');

    if (oldEstate?.status !== newEstate.status) {
      await this.mongoService.upsert(
        this.estateModel,
        { estateId: newEstate.estateId },
        {
          status: newEstate.status,
          $push: {
            statusChanges: {
              from: oldEstate?.status,
              to: newEstate.status,
              date: new Date(),
            },
          },
        },
      );
    }
    console.timeLog(`TimeHandleSQS ${newEstate.estateId} ${_timeLogUniqueId}`, '[updateEstateStatus] End');
  }

  private async deleteRemovedContacts(
    model: Model<SellerDocument> | Model<BuyerDocument> | Model<ProxyDocument>,
    estate: EstateDocument,
    validContactIds: string[],
  ): Promise<UpdateWriteOpResult | null> {
    return model.updateMany(
      {
        estates: estate,
        contactId: { $nin: validContactIds },
      },
      {
        $pull: { estates: estate._id },
      },
    );
  }

  private async updateSellersBuyersProxiesCollections(estate: EstateDocument, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[updateContactInformation] Start');

    const contactInformation = await this.vitecService.getContactInformation(estate.estateId);

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateContactInformation] After got contact info from vitec',
    );

    if (!contactInformation) {
      return;
    }

    const sellers = await Promise.all([
      ...contactInformation.sellers.map(async (seller) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { socialSecurity, ...sellerWithoutSocialSecurity } = seller;

        const mobilePhone = seller.mobilePhone ? seller.mobilePhone.replace(/ /g, '') : null;

        await this.mongoService.upsert(
          this.sellerModel,
          {
            contactId: seller.contactId,
          },
          {
            ...sellerWithoutSocialSecurity,
            mobilePhone,
            $addToSet: {
              estates: estate,
            },
          },
        );

        return {
          contactId: seller.contactId,
          contactType: seller.contactType,
          companyName: seller.companyName,
          firstName: seller.firstName,
          lastName: seller.lastName,
          mobilePhone,
        };
      }),
    ]);

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateContactInformation] After updated sellers',
    );

    const buyers = await Promise.all(
      contactInformation.buyers.map(async (buyer) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { socialSecurity, ...buyerWithoutSocialSecurity } = buyer;

        const mobilePhone = buyer.mobilePhone ? buyer.mobilePhone.replace(/ /g, '') : null;

        await this.mongoService.upsert(
          this.buyerModel,
          {
            contactId: buyer.contactId,
          },
          {
            ...buyerWithoutSocialSecurity,
            mobilePhone,
            $addToSet: {
              estates: estate,
            },
          },
        );

        return {
          contactId: buyer.contactId,
          contactType: buyer.contactType,
          companyName: buyer.companyName,
          firstName: buyer.firstName,
          lastName: buyer.lastName,
          mobilePhone,
        };
      }),
    );

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateContactInformation] After updated buyers',
    );

    const proxies = await Promise.all(
      contactInformation.proxies.map(async (proxyDto) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { contactId: proxyOf, proxy } = proxyDto;
        const mobilePhone = proxy.mobilePhone ? proxy.mobilePhone.replace(/ /g, '') : null;

        await this.mongoService.upsert(
          this.proxyModel,
          {
            contactId: proxy.contactId,
          },
          {
            ...omit(proxy, 'socialSecurity'),
            mobilePhone,
            $addToSet: {
              proxyOf,
              estates: estate,
            },
          },
        );

        return {
          proxyOf,
          contactId: proxy.contactId,
          contactType: proxy.contactType,
          companyName: proxy.companyName,
          firstName: proxy.firstName,
          lastName: proxy.lastName,
          mobilePhone,
        };
      }),
    );

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateContactInformation] After updated proxies',
    );

    const [sellerDeleteResult, buyerDeleteResult, proxyDeleteResult] = await Promise.all([
      this.deleteRemovedContacts(
        this.sellerModel,
        estate,
        contactInformation.sellers.map((seller) => seller.contactId),
      ),
      this.deleteRemovedContacts(
        this.buyerModel,
        estate,
        contactInformation.buyers.map((buyer) => buyer.contactId),
      ),
      this.deleteRemovedContacts(
        this.proxyModel,
        estate,
        contactInformation.proxies.map((proxy) => proxy.contactId),
      ),
    ]);

    this.logger.log(
      `Removed estate references for ${estate.estateId}: ${sellerDeleteResult?.modifiedCount || 0} sellers, ${
        buyerDeleteResult?.modifiedCount || 0
      } buyers, ${proxyDeleteResult?.modifiedCount || 0} proxies`,
    );

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateContactInformation] After removed outdated estate references from buyers/sellers/proxies',
    );

    await this.mongoService.upsert(
      this.estateModel,
      { estateId: estate.estateId },
      {
        sellers,
        buyers,
        proxies,
      },
    );

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateContactInformation] After updated estate',
    );
    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[updateContactInformation] End');
  }

  private async updateActivities(estateId: string, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateActivities] Start');

    const activities = await this.vitecService.getActivities(estateId);

    console.timeLog(
      `TimeHandleSQS ${estateId} ${_timeLogUniqueId}`,
      '[updateActivities] After got activities from vitec',
    );

    await this.mongoService.upsert(
      this.estateModel,
      { estateId },
      {
        activities,
      },
    );
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateActivities] End');
  }

  public async updateCheckList(estateId: string, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateCheckList] Start');

    const checkList = await this.vitecService.getCheckList(estateId);

    console.timeLog(
      `TimeHandleSQS ${estateId} ${_timeLogUniqueId}`,
      '[updateCheckList] After got checklist from vitec.',
    );

    await this.mongoService.upsert(
      this.estateModel,
      { estateId },
      {
        checkList,
      },
    );
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateCheckList] End');
  }

  private async updateBids(estateId: string, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateBids] Start');

    const bids = await this.vitecService.getBids(estateId);

    await this.mongoService.upsert(
      this.estateModel,
      { estateId },
      {
        bids,
      },
    );
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateBids] End');
  }

  private async updateDocuments(estateId: string, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateDocuments] Start');

    const documents = await this.vitecService.getDocuments(estateId);

    await this.mongoService.upsert(
      this.estateModel,
      { estateId },
      {
        documents,
      },
    );
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateDocuments] End');
  }

  private async updateContactRelations(estateId: string, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateContactRelations] Start');

    const contactRelations = await this.vitecService.getContactRelations(estateId);

    const allContacts = flatten(
      contactRelations.contacts.map((entry) => entry.contacts.map((c) => ({ ...c, relationType: entry.type }))),
    );

    await PromiseBB.map(
      allContacts,
      async (entry) => {
        const contact = await this.vitecService.getContact(entry.contactId);

        await this.mongoService.upsert(this.contactModel, { contactId: contact.contactId }, contact);
      },
      { concurrency: 5 },
    );

    await this.mongoService.upsert(
      this.estateModel,
      { estateId: estateId },
      {
        contacts: allContacts,
      },
    );
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateContactRelations] End');
  }

  private async updateAds(estateId: string, _timeLogUniqueId: string) {
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateAds] Start');

    const ads = await this.vitecService.getEstateAds(estateId);

    await this.mongoService.upsert(
      this.estateModel,
      { estateId },
      {
        ads,
      },
    );
    console.timeLog(`TimeHandleSQS ${estateId} ${_timeLogUniqueId}`, '[updateAds] End');
  }

  private async sendDocumentsAvailableNotificationIfApplicable({
    oldEstate,
    finalNewEstate,
  }: {
    oldEstate: EstateDocument;
    finalNewEstate: EstateDocument;
  }) {
    if (oldEstate?.documents?.length === 0 && finalNewEstate.documents?.length > 0) {
      this.logger.log(
        `oldEstate doc length: ${oldEstate?.documents?.length}, newEstate doc length: ${finalNewEstate.documents?.length}`,
      );
      const buyerIds = finalNewEstate.buyers.map((s) => s.contactId);
      const buyers = await this.buyerModel.find({ contactId: { $in: buyerIds } });
      const sellerIds = finalNewEstate.sellers.map((s) => s.contactId);
      const sellers = await this.sellerModel.find({ contactId: { $in: sellerIds } });
      const proxyIds = finalNewEstate.proxies.map((p) => p.contactId);
      const proxies = await this.proxyModel.find({ contactId: { $in: proxyIds } });

      await this.userNotifierService.notifyContacts({
        contacts: [...buyers, ...sellers, ...proxies],
        feedFeatureFlag: FeatureFlag.SendFirstDocumentFeed,
        iconName: 'script',
        isFeatureEnabled: this.authorizationService.isFeatureEnabled,
        message: 'Et nytt dokument er tilgjengelig for din bolig',
        pushFeatureFlag: FeatureFlag.SendFirstDocumentPush,
        redirectUrl: `/customer/estate/${finalNewEstate.estateId}/documents`,
      });
    }
  }

  private async sendPepSellerNotificationIfApplicable({
    oldEstate,
    finalNewEstate,
  }: {
    oldEstate: EstateDocument;
    finalNewEstate: EstateDocument;
  }) {
    if (
      this.politicallyExposedPersonFormNotificationService.isPepSellerFormCreationEnabled(finalNewEstate) &&
      !this.politicallyExposedPersonFormNotificationService.isPepSellerFormCreationEnabled(oldEstate)
    ) {
      this.logger.debug(`Sending PEP seller notification to estate: ${finalNewEstate.estateId}`);

      try {
        await this.politicallyExposedPersonFormNotificationService.createFormAndSendSellerNotificationsIfNotSent(
          finalNewEstate,
        );
      } catch (e) {
        this.logger.error(`Error sending PEP seller notifications to estate: ${finalNewEstate.estateId}, error ${e}`);
      }
    } else {
      this.logger.debug(
        `Skipping sending PEP seller notification to estate: ${finalNewEstate.estateId}, assignmentTypeGroup: ${
          finalNewEstate.assignmentTypeGroup
        }, newEstate: ${this.politicallyExposedPersonFormNotificationService.isPepSellerFormCreationEnabled(
          finalNewEstate,
        )}, oldEstate: ${this.politicallyExposedPersonFormNotificationService.isPepSellerFormCreationEnabled(
          oldEstate,
        )}, newChecklist: ${JSON.stringify(finalNewEstate.checkList)}, oldChecklist: ${JSON.stringify(
          oldEstate?.checkList,
        )}`,
      );
    }
  }

  private async sendSettlementSellerNotificationIfApplicable({
    oldEstate,
    finalNewEstate,
  }: {
    oldEstate: EstateDocument;
    finalNewEstate: EstateDocument;
  }) {
    if (
      this.settlementSellerNotificationService.isSettlementSellerFormCreationEnabled(finalNewEstate)
      // TODO reintroduce this part after some time, because if the brokers set a critical condition before the feature was rolled out, we would not send out notis: && !this.settlementSellerNotificationService.isSettlementSellerFormCreationEnabled(oldEstate)
    ) {
      this.logger.debug(`Sending Settlement seller notification to estate: ${finalNewEstate.estateId}`);

      try {
        await this.settlementSellerNotificationService.createFormAndSendNotificationsIfNotSent(finalNewEstate);
      } catch (e) {
        this.logger.error(
          `Error sending Settlement seller notifications to estate: ${finalNewEstate.estateId}, error ${e}`,
        );
      }
    } else {
      this.logger.debug(
        `Skipping sending Settlement seller notification to estate: ${finalNewEstate.estateId}, assignmentTypeGroup: ${
          finalNewEstate.assignmentTypeGroup
        }, newEstate: ${this.settlementSellerNotificationService.isSettlementSellerFormCreationEnabled(
          finalNewEstate,
        )}, oldEstate: ${this.settlementSellerNotificationService.isSettlementSellerFormCreationEnabled(
          oldEstate,
        )}, newChecklist: ${JSON.stringify(finalNewEstate.checkList)}, oldChecklist: ${JSON.stringify(
          oldEstate?.checkList,
        )}`,
      );
    }
  }

  private async sendSettlementBuyerNotificationIfApplicable({
    oldEstate,
    finalNewEstate,
  }: {
    oldEstate: EstateDocument;
    finalNewEstate: EstateDocument;
  }) {
    if (
      this.settlementBuyerNotificationService.isSettlementBuyerFormCreationEnabled(finalNewEstate)
      // TODO reintroduce this part after some time, because if the brokers set a critical condition before the feature was rolled out, we would not send out notis: && !this.settlementBuyerNotificationService.isSettlementBuyerFormCreationEnabled(oldEstate)
    ) {
      this.logger.debug(`Sending Settlement buyer notification to estate: ${finalNewEstate.estateId}`);

      try {
        await this.settlementBuyerNotificationService.createFormAndSendNotificationsIfNotSent(finalNewEstate);
      } catch (e) {
        this.logger.error(
          `Error sending Settlement buyer notifications to estate: ${finalNewEstate.estateId}, error ${e}`,
        );
      }
    } else {
      this.logger.debug(
        `Skipping sending Settlement buyer notification to estate: ${finalNewEstate.estateId}, assignmentTypeGroup: ${
          finalNewEstate.assignmentTypeGroup
        }, newEstate: ${this.settlementBuyerNotificationService.isSettlementBuyerFormCreationEnabled(
          finalNewEstate,
        )}, oldEstate: ${this.settlementBuyerNotificationService.isSettlementBuyerFormCreationEnabled(
          oldEstate,
        )}, newChecklist: ${JSON.stringify(finalNewEstate.checkList)}, oldChecklist: ${JSON.stringify(
          oldEstate?.checkList,
        )}`,
      );
    }
  }

  private async sendSettlementBuyerProjectNotificationIfApplicable({
    oldEstate,
    finalNewEstate,
  }: {
    oldEstate: EstateDocument;
    finalNewEstate: EstateDocument;
  }) {
    if (
      this.settlementBuyerNotificationService.isSettlementBuyerProjectFormCreationEnabled(finalNewEstate)
      // TODO reintroduce this part after some time, because if the brokers set a critical condition before the feature was rolled out, we would not send out notis: && !this.settlementBuyerNotificationService.isSettlementBuyerProjectFormCreationEnabled(oldEstate)
    ) {
      this.logger.debug(`Sending Settlement buyer project notification to estate: ${finalNewEstate.estateId}`);

      try {
        await this.settlementBuyerNotificationService.createFormAndSendNotificationsIfNotSent(finalNewEstate, true);
      } catch (e) {
        this.logger.error(
          `Error sending Settlement buyer project notifications to estate: ${finalNewEstate.estateId}, error ${e}`,
        );
      }
    } else {
      this.logger.debug(
        `Skipping sending Settlement buyer project notification to estate: ${
          finalNewEstate.estateId
        }, assignmentTypeGroup: ${
          finalNewEstate.assignmentTypeGroup
        }, newEstate: ${this.settlementBuyerNotificationService.isSettlementBuyerProjectFormCreationEnabled(
          finalNewEstate,
        )}, oldEstate: ${this.settlementBuyerNotificationService.isSettlementBuyerProjectFormCreationEnabled(
          oldEstate,
        )}, newChecklist: ${JSON.stringify(finalNewEstate.checkList)}, oldChecklist: ${JSON.stringify(
          oldEstate?.checkList,
        )}`,
      );
    }
  }

  private async sendOtpNotificationIfApplicable({
    oldEstate,
    finalNewEstate,
  }: {
    oldEstate: EstateDocument;
    finalNewEstate: EstateDocument;
  }) {
    if (
      isTakeOverDateTodayOrTomorrow(finalNewEstate) &&
      this.overtakeProtocolNotificationService.shouldCreateOtpForEstate(finalNewEstate) &&
      (oldEstate.takeOverDate?.getTime() !== finalNewEstate.takeOverDate?.getTime() || // It was changed with good reason by Broker, rather send out duplicate noti than don't send out because it's before 7:55 AM
        !isTakeOverDateTodayOrTomorrow(oldEstate) || // We didn't recently sent out notification about the takeover
        !this.overtakeProtocolNotificationService.shouldCreateOtpForEstate(oldEstate)) // The checklists were in a bad state, so we have not sent out notifications before
    ) {
      try {
        await this.overtakeProtocolNotificationService.sendNotificationsIfFeatureFlagEnabled(finalNewEstate);
      } catch (e) {
        this.logger.error(`Error sending OTP notifications to estate: ${finalNewEstate.estateId}, error ${e}`);
      }
    } else {
      this.logger.debug(
        `Skipping sending OTP notification to estate: ${finalNewEstate.estateId}, newEstate: { takeOverDate: ${
          finalNewEstate.takeOverDate
        }, checkList: ${this.overtakeProtocolNotificationService.shouldCreateOtpForEstate(
          finalNewEstate,
        )} }, oldEstate: { takeOverDate: ${
          oldEstate?.takeOverDate
        }, checkList: ${this.overtakeProtocolNotificationService.shouldCreateOtpForEstate(oldEstate)} }`,
      );
    }
  }

  private async updateEstateBase({
    estate,
    oldEstate,
    _timeLogUniqueId,
  }: {
    estate: EstateDto;
    oldEstate: EstateDocument;
    _timeLogUniqueId: string;
  }) {
    const mainEmployeeId = this.getMainEmployeeId(estate);

    if (!mainEmployeeId) {
      this.logger.warn(`There is no main broker set for estate ${estate.estateId}`);
    }

    const mainEmployee = mainEmployeeId ? await this.employeeModel.findOne({ employeeId: mainEmployeeId }) : null;

    if (!mainEmployee) {
      this.logger.warn(
        `Main broker with employee id ${mainEmployeeId} is not found in our database for estate ${estate.estateId}`,
      );
    }
    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[updateEstateBase] Updating estate base');

    const brokersIdWithRoles = await this.getBrokersIdWithRoles(estate, _timeLogUniqueId);

    const department = await this.departmentModel.findOne({
      departmentId: estate.departmentId,
    });

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateEstateBase] After getting department',
    );

    if (!department) {
      this.logger.warn(`There is no department set for estate ${estate.estateId}`);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { estatePrice, status, ...estateWithoutSpecialProperties } = estate;

    const newEstate = await this.mongoService.upsert(
      this.estateModel,
      { estateId: estate.estateId },
      {
        ...estateWithoutSpecialProperties,
        url: urljoin('/boliger/', estate.estateId),
        location: { type: 'Point', coordinates: [estate.longitude || 0, estate.latitude || 0] },
        brokersIdWithRoles,
        employeeId: mainEmployeeId,
        employee: mainEmployee,
        department,

        // TODO: declare proper types on schema
        textFields: estate.textFields as any,
        facilities: estate.facilities as any,
        partOwnership: estate.partOwnership as any,
      },
    );

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateEstateBase] After upserting new Estate',
    );

    await this.updateEstatePrice(estate, oldEstate, _timeLogUniqueId);
    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateEstateBase] After updated estate price',
    );

    await this.updateEstateStatus(estate, oldEstate, _timeLogUniqueId);
    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateEstateBase] After updated estate status',
    );

    await this.updateDocuments(estate.estateId, _timeLogUniqueId);
    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateEstateBase] After updated estate documents',
    );

    await this.updateAds(estate.estateId, _timeLogUniqueId);
    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[updateEstateBase] After updated estate ads',
    );

    return newEstate;
  }

  private async updateMongoCollectionsBasedOnEndpointType({
    oldEstate,
    estate,
    endpoint,
    start,
    _timeLogUniqueId,
  }: {
    oldEstate: EstateDocument;
    estate: EstateDto;
    endpoint: EndpointType;
    start: Date;
    _timeLogUniqueId: string;
  }) {
    // if old estate doesnt exist, update everything
    const updateType = oldEstate ? endpoint : EndpointType.All;
    switch (updateType) {
      case EndpointType.Activities: {
        await this.updateActivities(estate.estateId, _timeLogUniqueId);
        break;
      }
      case EndpointType.CheckList: {
        await this.updateCheckList(estate.estateId, _timeLogUniqueId);
        break;
      }
      case EndpointType.Bids: {
        await this.updateBids(estate.estateId, _timeLogUniqueId);
        break;
      }
      case EndpointType.ContactInformation: {
        await this.updateSellersBuyersProxiesCollections(oldEstate, _timeLogUniqueId);
        break;
      }
      case EndpointType.ContactRelations: {
        if (oldEstate && oldEstate.estateBaseType !== EstateBaseType.PROJECT) {
          await this.updateContactRelations(estate.estateId, _timeLogUniqueId);
        }
        break;
      }
      case EndpointType.Estates: {
        const newEstate = await this.updateEstateBase({ estate, oldEstate, _timeLogUniqueId });
        const isAdditionalEnsurityContactSyncNeeded = newEstate.status === 3;
        if (isAdditionalEnsurityContactSyncNeeded) {
          await this.updateSellersBuyersProxiesCollections(newEstate, _timeLogUniqueId);
        }
        await this.mongoService.upsert(
          this.estateModel,
          { estateId: newEstate.estateId },
          {
            lastSuccessfulSyncStartDate: start,
          },
        );
        break;
      }
      default: {
        const newEstate = await this.updateEstateBase({ estate, oldEstate, _timeLogUniqueId });
        await this.updateSellersBuyersProxiesCollections(newEstate, _timeLogUniqueId);
        await this.updateActivities(estate.estateId, _timeLogUniqueId);
        await this.updateCheckList(estate.estateId, _timeLogUniqueId);
        await this.updateBids(estate.estateId, _timeLogUniqueId);
        if (oldEstate && oldEstate.estateBaseType !== EstateBaseType.PROJECT) {
          await this.updateContactRelations(estate.estateId, _timeLogUniqueId);
        }
        await this.mongoService.upsert(
          this.estateModel,
          { estateId: estate.estateId },
          {
            lastSuccessfulSyncStartDate: start,
          },
        );
      }
    }
  }

  async upsertEstate({
    estate,
    syncReceiveDate,
    existingOldEstate,
    endpoint,
    _timeLogUniqueId,
  }: {
    estate: EstateDto;
    syncReceiveDate: Date;
    existingOldEstate?: EstateDocument;
    endpoint?: EndpointType;
    _timeLogUniqueId: string;
  }): Promise<void> {
    const start = syncReceiveDate || new Date();

    this.logger.debug(`Begin upserting estate ${estate.estateId} at ${start.toISOString()} type: ${endpoint}`);
    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[upsertEstate] Start');

    const oldEstate =
      existingOldEstate ||
      (await this.estateModel.findOne({
        estateId: estate.estateId,
      }));

    await this.updateMongoCollectionsBasedOnEndpointType({ oldEstate, estate, endpoint, start, _timeLogUniqueId });

    const finalNewEstate = await this.estateModel.findOne({ estateId: estate.estateId });

    // notification hooks

    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[upsertEstate] Notifications start');

    await this.sendDocumentsAvailableNotificationIfApplicable({ oldEstate, finalNewEstate });

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[upsertEstate] Notifications after notifyContacts',
    );

    this.logger.debug(`Begin sending notifications for estate ${estate.estateId}`);

    await this.sendOtpNotificationIfApplicable({ oldEstate, finalNewEstate });

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[upsertEstate] PEP seller notification after OTP notification',
    );

    await this.sendPepSellerNotificationIfApplicable({ oldEstate, finalNewEstate });

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[upsertEstate] Settlement seller notifications after PEP notification',
    );

    await this.sendSettlementSellerNotificationIfApplicable({ oldEstate, finalNewEstate });

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[upsertEstate] Settlement buyer notifications after settlement seller notification',
    );

    await this.sendSettlementBuyerNotificationIfApplicable({ oldEstate, finalNewEstate });

    console.timeLog(
      `TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`,
      '[upsertEstate] Settlement buyer project notifications after settlement buyer notification',
    );

    await this.sendSettlementBuyerProjectNotificationIfApplicable({ oldEstate, finalNewEstate });

    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[upsertEstate] Notifications end');
    console.timeLog(`TimeHandleSQS ${estate.estateId} ${_timeLogUniqueId}`, '[upsertEstate] End');
  }

  async updateEstates(syncState: SyncState | null): Promise<void> {
    this.logger.log('Updating estates');

    const departments = await this.departmentModel.find();

    const estateMetadata = flatten(
      await Promise.all(
        departments.map(async (department) => {
          return this.vitecService.getEstateMetadata(department.departmentId);
        }),
      ),
    );

    this.logger.debug(`There are ${estateMetadata.length} estates in total`);

    const changedEstatesSinceLastSync = syncState
      ? estateMetadata.filter((estate) => estate.changedDate >= syncState.lastSuccessfulSyncDate)
      : estateMetadata;

    this.logger.debug(`There are ${changedEstatesSinceLastSync.length} changed estates since the last successful sync`);

    if (changedEstatesSinceLastSync.length > 0) {
      const estates = changedEstatesSinceLastSync;
      this.logger.debug(`Pushing estates: ${JSON.stringify(estates.map((e) => e.estateId))} to sqs`);
      if (!this.appConfigService.getDisableSqs()) {
        await this.producer.send(
          estates.map((e) => ({
            body: e.estateId,
            groupId: 'push',
            deduplicationId: v4(),
            id: e.estateId,
            messageAttributes: {
              endpoint: { DataType: 'String', StringValue: EndpointType.Estates },
              changeDate: { DataType: 'String', StringValue: e.changedDate.toISOString() },
            },
          })),
        );
      }
    } else {
      this.logger.debug(`No estate should be changed`);
    }
  }

  async updateAllContactInformation(): Promise<void> {
    this.logger.log('Updating contact informations');

    const departments = await this.departmentModel.find();

    const estateMetadata = flatten(
      await Promise.all(
        departments.map(async (department) => {
          return this.vitecService.getEstateMetadata(department.departmentId);
        }),
      ),
    );

    this.logger.debug(`There are ${estateMetadata.length} estates in total`);

    const nonArchivedEstates = estateMetadata.filter((estate) => estate.status > -1);

    this.logger.debug(`There are ${nonArchivedEstates.length} non-archived estates`);

    if (nonArchivedEstates.length > 0) {
      const estates = nonArchivedEstates;
      this.logger.debug(`Pushing estates: ${JSON.stringify(estates.length)} to sqs`);
      if (!this.appConfigService.getDisableSqs()) {
        await this.producer.send(
          estates.map((e) => ({
            body: e.estateId,
            groupId: 'push',
            deduplicationId: v4(),
            id: e.estateId,
            messageAttributes: {
              endpoint: { DataType: 'String', StringValue: EndpointType.ContactInformation },
              changeDate: { DataType: 'String', StringValue: new Date().toISOString() },
            },
          })),
        );
      }
    } else {
      this.logger.debug(`No estate should be changed`);
    }
  }
}
