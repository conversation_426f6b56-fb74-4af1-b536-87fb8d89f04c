import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { flatten } from 'ramda';
import { MongoService } from '../../mongo/mongo.service';
import { EstateService } from '../../pg/estate/estate.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { VitecService } from '../../vitec/vitec.service';
import UpdateArchivedEstatesJob from '../jobs/update-archived-estates.job';
import { Department, DepartmentDocument } from '../schema/department.schema';
import { SyncService } from '../sync.service';

@Injectable()
export class ArchivedEstateSyncService {
  private readonly logger = new ConsoleLogger(ArchivedEstateSyncService.name);

  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly vitecService: VitecService,
    private readonly mongoService: MongoService,
    private readonly syncService: SyncService,
    private readonly estateService: EstateService,
    @InjectModel(Department.name) private departmentModel: Model<DepartmentDocument>,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_1AM, {
    name: UpdateArchivedEstatesJob.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(UpdateArchivedEstatesJob.name);
    cronJob.stop();

    this.logger.log('Scheduled archived estate update triggered');

    const job = new UpdateArchivedEstatesJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`Scheduled archived estate update finished with result: ${result}`);
    cronJob.start();
  }

  async archiveEstates(): Promise<void> {
    this.logger.log('Archiving estates');

    const departments = await this.departmentModel.find();

    const estateMetadata = flatten(
      await Promise.all(
        departments.map(async (department) => {
          return this.vitecService.getEstateMetadata(department.departmentId);
        }),
      ),
    );

    if (!estateMetadata.length) {
      this.logger.warn('There are no estates in Vitec');
      return;
    }

    this.logger.debug(`There are ${estateMetadata.length} total estates`);

    const unarchivedEstateIdsInVitec = estateMetadata.map((estate) => estate.estateId);

    await this.mongoService.runInTransaction(async () => {
      const estatesToBeArchived = await this.estateModel.find({
        estateId: { $nin: unarchivedEstateIdsInVitec },
        status: { $ne: VitecEstateStatus.OWN_ARCHIVED },
      });

      if (estatesToBeArchived.length > 0) {
        this.logger.debug(
          `Archiving estates and updating "estimationOffset" by IDs: ${JSON.stringify(
            estatesToBeArchived.map((estate) => estate.estateId),
          )}`,
        );

        await Promise.all(
          estatesToBeArchived.map(async (estate) => {
            await this.mongoService.upsert(
              this.estateModel,
              { estateId: estate.estateId },
              {
                status: VitecEstateStatus.OWN_ARCHIVED,
                statusChanges: [
                  ...estate.statusChanges,
                  { from: estate.status, to: VitecEstateStatus.OWN_ARCHIVED, date: new Date() },
                ],
              },
            );
            const evLandIdMatrix = EstateService.getEVIdentificationMatrixFromVitec(estate.matrikkel);
            const evEstate = await this.estateService.findByLandIdentificationMatrix(evLandIdMatrix);
            if (evEstate) {
              await this.estateService.update(evEstate.id, { estimationOffset: null });
            }
          }),
        );
      } else {
        this.logger.debug(`No estate should be archived`);
      }
    });
  }
}
