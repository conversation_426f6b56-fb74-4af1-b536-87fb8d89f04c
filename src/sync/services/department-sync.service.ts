import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { MongoService } from '../../mongo/mongo.service';
import { generateSlug } from '../../utils/slug.utils';
import DepartmentDto from '../../vitec/dto/department.dto';
import { VitecService } from '../../vitec/vitec.service';
import UpdateDepartmentsJob from '../jobs/update-departments.job';
import { Department, DepartmentDocument } from '../schema/department.schema';
import { Estate, EstateDocument } from '../schema/estate.schema';
import { SyncService } from '../sync.service';

@Injectable()
export class DepartmentSyncService {
  private readonly logger = new ConsoleLogger(DepartmentSyncService.name);

  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly vitecService: VitecService,
    private readonly mongoService: MongoService,
    private readonly syncService: SyncService,
    @InjectModel(Department.name) private departmentModel: Model<DepartmentDocument>,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_2AM, {
    name: UpdateDepartmentsJob.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    const cronJob = this.schedulerRegistry.getCronJob(UpdateDepartmentsJob.name);
    cronJob.stop();

    this.logger.log('Scheduled department update triggered');

    const job = new UpdateDepartmentsJob(this.syncService, this);
    const result = await job.start();

    this.logger.log(`Scheduled department update finished with result: ${result}`);
    cronJob.start();
  }

  async updateDepartment(department: DepartmentDto): Promise<void> {
    this.logger.debug(`Updating department ${department.departmentId}`);

    await this.mongoService.upsert(
      this.departmentModel,
      {
        departmentId: department.departmentId,
      },
      {
        departmentId: department.departmentId,
        name: department.name,
        organisationNumber: department.organisationNumber,
        legalName: department.legalName,
        phone: department.phone,
        email: department.email,
        streetAddress: department.streetAddress,
        postalAddress: department.postalAddress,
        postalCode: department.postalCode,
        marketName: department.marketName,
        city: department.city,
        slug: generateSlug(department.name),
        // we don't use commissions for now (16/09/2024)
        // related task: https://linear.app/nordvik/issue/NOR-921/investigate-misuseunnecessary-request-to-vitec-hub
        commissions: [],
      },
    );
  }

  async updateDepartments(): Promise<void> {
    this.logger.log('Updating departments');

    const departments = await this.vitecService.getDepartments();

    await this.mongoService.runInTransaction(async () => {
      for (const department of departments) {
        await this.updateDepartment(department);
      }
    });
  }
}
