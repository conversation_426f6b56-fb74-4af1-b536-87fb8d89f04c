import SyncJob from '../sync-job';
import { SyncService } from '../sync.service';
import { EiendomsverdiEstateSyncService } from '../services/eiendomsverdi-estate-sync.service';

export default class UpdateEiendomsverdiEstatesJob extends SyncJob {
  constructor(
    updaterService: SyncService,
    private readonly eiendomsverdiEstateSyncService: EiendomsverdiEstateSyncService,
  ) {
    super(UpdateEiendomsverdiEstatesJob.name, updaterService);
  }

  async run(): Promise<void> {
    await this.eiendomsverdiEstateSyncService.deleteSoldEstates();
    await this.eiendomsverdiEstateSyncService.createBoughtEstates();
    await this.eiendomsverdiEstateSyncService.updateExistingEstates();
  }
}
