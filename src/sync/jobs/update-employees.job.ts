import SyncJob from '../sync-job';
import { SyncService } from '../sync.service';
import { EmployeeSyncService } from '../services/employee-sync.service';

export default class UpdateEmployees<PERSON>ob extends SyncJob {
  constructor(updaterService: SyncService, private readonly employeeSyncService: EmployeeSyncService) {
    super(UpdateEmployeesJob.name, updaterService);
  }

  async run(syncState): Promise<void> {
    await this.employeeSyncService.updateEmployees(syncState);
  }
}
