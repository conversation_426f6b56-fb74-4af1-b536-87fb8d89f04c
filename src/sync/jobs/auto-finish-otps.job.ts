import { SyncService } from '../sync.service';
import Sync<PERSON><PERSON> from '../sync-job';
import { OtpAutoFinalizationService } from '../services/auto-finalization/otp-auto-finalization.service';

export default class AutoFinishOtpsJob extends Sync<PERSON>ob {
  constructor(syncService: SyncService, private readonly otpAutoFinalizationService: OtpAutoFinalizationService) {
    super(AutoFinishOtpsJob.name, syncService);
  }

  async run(): Promise<void> {
    await this.otpAutoFinalizationService.autoFinalizeOtps();
  }
}
