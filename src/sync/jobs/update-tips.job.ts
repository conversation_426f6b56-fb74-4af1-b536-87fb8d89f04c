import SyncJob from '../sync-job';
import { SyncService } from '../sync.service';
import { SyncState } from '../schema/sync-state';
import { TipSyncService } from '../services/tip-sync.service';

export default class UpdateTipsJob extends SyncJob {
  constructor(updaterService: SyncService, private readonly tipSyncService: TipSyncService) {
    super(UpdateTipsJob.name, updaterService);
  }

  async run(syncState: SyncState | null): Promise<void> {
    await this.tipSyncService.updateTips(syncState);
  }
}
