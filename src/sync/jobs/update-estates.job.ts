import SyncJob from '../sync-job';
import { SyncService } from '../sync.service';
import { SyncState } from '../schema/sync-state';
import { EstateSyncService } from '../services/estate-sync.service';

export default class UpdateEstatesJob extends SyncJob {
  constructor(updaterService: SyncService, private readonly estateSyncService: EstateSyncService) {
    super(UpdateEstatesJob.name, updaterService);
  }

  async run(syncState: SyncState | null): Promise<void> {
    await this.estateSyncService.updateEstates(syncState);
  }
}
