import SyncJob from '../sync-job';
import { SyncService } from '../sync.service';
import { DepartmentSyncService } from '../services/department-sync.service';

export default class UpdateDepartmentsJob extends SyncJob {
  constructor(updaterService: SyncService, private readonly departmentSyncService: DepartmentSyncService) {
    super(UpdateDepartmentsJob.name, updaterService);
  }

  async run(): Promise<void> {
    await this.departmentSyncService.updateDepartments();
  }
}
