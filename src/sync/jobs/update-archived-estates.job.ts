import { SyncService } from '../sync.service';
import { ArchivedEstateSyncService } from '../services/archived-estate-sync.service';
import SyncJob from '../sync-job';

export default class UpdateArchivedEstatesJob extends SyncJob {
  constructor(syncService: SyncService, private readonly archivedEstateSyncService: ArchivedEstateSyncService) {
    super(UpdateArchivedEstatesJob.name, syncService);
  }

  async run(): Promise<void> {
    await this.archivedEstateSyncService.archiveEstates();
  }
}
