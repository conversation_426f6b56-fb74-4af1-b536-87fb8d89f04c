import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import AppConfigModule from '../app-config/app-config.module';
import AuthorizationModule from '../authorization/authorization.module';
import EiendomsverdiModule from '../eiendomsverdi/eiendomsverdi.module';
import MapboxModule from '../mapbox/mapbox.module';
import MongoModule from '../mongo/mongo.module';
import NordvikboligApiModule from '../norvikbolig-api/nordvikbolig-api.module';
import NotificationModule from '../notification/notification.module';
import EstatePriceHistoriesModule from '../pg/estate-price-histories/estate-price-histories.module';
import EstatesModule from '../pg/estate/estate.module';
import OvertakeProtocolModule from '../pg/overtake-protocol/overtake-protocol.module';
import PEPFormModule from '../pg/pep-form/pep-form.module';
import SettlementBuyerModule from '../pg/settlement-buyer/settlement-buyer.module';
import SettlementSellerModule from '../pg/settlement-seller/settlement-seller.module';
import UsersModule from '../pg/user/user.module';
import S3Module from '../s3/s3.module';
import SlackModule from '../slack/slack.module';
import TwilioModule from '../twilio/twilio.module';
import VitecModule from '../vitec/vitec.module';
import { Buyer, BuyerSchema } from './schema/buyer.schema';
import { Contact, ContactSchema } from './schema/contact.schema';
import { Department, DepartmentSchema } from './schema/department.schema';
import { Employee, EmployeeSchema } from './schema/employee.schema';
import { Estate, EstateSchema } from './schema/estate.schema';
import { Proxy, ProxySchema } from './schema/proxy.schema';
import { Seller, SellerSchema } from './schema/seller.schema';
import { SyncState, SyncStateSchema } from './schema/sync-state';
import { Tip, TipSchema } from './schema/tip.schema';
import { ArchivedEstateSyncService } from './services/archived-estate-sync.service';
import { OtpAutoFinalizationService } from './services/auto-finalization/otp-auto-finalization.service';
import { PEPFormAutoFinalizationService } from './services/auto-finalization/pep-form-auto-finalization.service';
import { SettlementBuyerFormAutoFinalizationService } from './services/auto-finalization/settlement-buyer-form-auto-finalization.service';
import { SettlementSellerFormAutoFinalizationService } from './services/auto-finalization/settlement-seller-form-auto-finalization.service';
import { DepartmentSyncService } from './services/department-sync.service';
import { EiendomsverdiEstateSyncService } from './services/eiendomsverdi-estate-sync.service';
import { EmployeeSyncService } from './services/employee-sync.service';
import { UpdateEstatePriceHistoriesService } from './services/estate-price-histories/update-estate-price-histories.service';
import { EstateSyncService } from './services/estate-sync.service';
import { TipSyncService } from './services/tip-sync.service';
import { SyncController } from './sync.controller';
import { SyncService } from './sync.service';

@Module({
  imports: [
    AppConfigModule,
    AuthorizationModule,
    VitecModule,
    MongoModule,
    S3Module,
    TwilioModule,
    NotificationModule,
    UsersModule,
    EstatesModule,
    EiendomsverdiModule,
    OvertakeProtocolModule,
    SettlementBuyerModule,
    SettlementSellerModule,
    PEPFormModule,
    SlackModule,
    MapboxModule,
    EstatePriceHistoriesModule,
    NordvikboligApiModule,
    MongooseModule.forFeature([
      { name: SyncState.name, schema: SyncStateSchema },
      { name: Estate.name, schema: EstateSchema },
      { name: Department.name, schema: DepartmentSchema },
      { name: Employee.name, schema: EmployeeSchema },
      { name: Seller.name, schema: SellerSchema },
      { name: Buyer.name, schema: BuyerSchema },
      { name: Proxy.name, schema: ProxySchema },
      { name: Contact.name, schema: ContactSchema },
      { name: Tip.name, schema: TipSchema },
    ]),
    ScheduleModule.forRoot(),
  ],
  providers: [
    SyncService,
    ArchivedEstateSyncService,
    DepartmentSyncService,
    EmployeeSyncService,
    EstateSyncService,
    TipSyncService,
    EiendomsverdiEstateSyncService,
    OtpAutoFinalizationService,
    SettlementBuyerFormAutoFinalizationService,
    SettlementSellerFormAutoFinalizationService,
    PEPFormAutoFinalizationService,
    UpdateEstatePriceHistoriesService,
    {
      provide: ConsoleLogger,
      useClass: ConsoleLogger,
      scope: Scope.TRANSIENT,
    },
  ],
  controllers: [SyncController],
  exports: [SyncService],
})
export default class SyncModule {}
