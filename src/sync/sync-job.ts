import { ConsoleLogger } from '@nestjs/common';
import { SyncState } from './schema/sync-state';
import { SyncService } from './sync.service';

export enum JobResult {
  Success,
  Error,
}

export default abstract class SyncJob {
  private readonly jobLogger = new ConsoleLogger(SyncJob.name);

  protected constructor(private readonly id: string, private readonly syncService: SyncService) {}

  abstract run(syncState: SyncState | null): Promise<void>;

  async start(): Promise<JobResult> {
    const jobStartDate = new Date();
    const syncState = await this.syncService.getSyncState(this.id);

    try {
      await this.run(syncState);

      await this.syncService.setSyncState(this.id, {
        lastSuccessfulSyncDate: jobStartDate,
      });

      return JobResult.Success;
    } catch (error) {
      this.jobLogger.error(`Unhandled error: ${error}`);
      return JobResult.Error;
    }
  }
}
