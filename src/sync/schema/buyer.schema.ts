import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import * as mongoose from 'mongoose';
import { Estate } from './estate.schema';
import { ESTATE_MODEL_NAME } from './model-names';

export type BuyerDocument = Buyer & Document;

@Schema({ collection: 'buyers', timestamps: true })
export class Buyer {
  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: ESTATE_MODEL_NAME }],
  })
  estates: Estate[];

  @Prop()
  contactId: string;

  @Prop()
  contactType: number;

  @Prop()
  organisationNumber: string;

  @Prop()
  mobilePhone: string;

  @Prop()
  email: string;

  @Prop()
  smsNotificationSent: boolean;

  @Prop()
  smsNotificationSentDate: Date;

  @Prop()
  changedDate: Date;

  @Prop()
  lastName: string;

  @Prop()
  firstName: string;

  @Prop()
  companyName: string;
}

export const BuyerSchema = SchemaFactory.createForClass(Buyer);
