import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';
import { Document } from 'mongoose';
import { EstateAssignmentTypeGroup } from '../../vitec/dto/estate.dto';
import { Contact } from './contact.schema';
import { Department } from './department.schema';
import { Employee } from './employee.schema';
import { DEPARTMENT_MODEL_NAME, EMPLOYEE_MODEL_NAME } from './model-names';

export type EstateDocument = Estate & Document;

export class LandIdentificationMatrix {
  knr: number;
  gnr: number;
  bnr: number;
  fnr: number;
  snr: number;
}

export class EstateAddressNested {
  apartmentNumber: string;
  streetAdress: string;
  zipCode: string;
  city: string;
}

export class EstateImageNested {
  large: string;
  medium: string;
  small: string;
}

export class EstatePlotNested {
  owned: boolean;
  size: number;
  description: string;
}

export class EstateValuationTaxNested {
  primaryValue: number;
  primaryYear: number;
  secondaryValue: number;
  secondaryYear: number;
  comment: string;
  propertyTaxAmount: number;
  propertyTaxYear: number;
  valuationDate: Date;
  valuationType: string;
}

export class EstatePriceNested {
  priceSuggestion: number;
  soldPrice: number;
  estimatedValue: number;
  communityTax: number;
  communityTaxYear: number;
  salesCostDescription: string;
  changedDate: Date;
  totalPrice: number;
}

export class EstateSizeNested {
  primaryRoomArea: number;
  primaryRoomAreaDescription: string;
  grossArea: number;
  usableArea: number;
}

export class EstateBuildingNested {
  name: string;
  buildingArea: EstateBuildingAreaNested[];
}

export class EstateBuildingAreaNested {
  areaType: AreaType;
  areaInformation: EstateAreaInformationNested[];
}

export class EstateAreaInformationNested {
  floorNumber: number;
  areaSize: number;
  areaDescription: string;
}

export enum AreaType {
  UsageArea = 1,
  LivingArea = 2,
  GrossArea = 3,
  GrossAreaBta = 4,
  UsageAreaI = 10,
  UsageAreaE = 11,
  UsageAreaB = 12,
  OpenArea = 20,
}

export class EstateLocationNested {
  type: string;
  coordinates: number[];
}

export class EstateProjectUnitNested {
  estateId: string;
  priceEstimate: number;
  usableArea: number;
  primaryRoomArea: number;
  descriptionItemAmountOfRooms: number;
  floor: number;
  estateType: string;
  status: number;
  purchaseCostsAmount: number;
  rentPrMonth: number;
  collectiveDebt: number;
  soldPrice: number;
  soldDate: Date;
  apartmentNumber: string;
}

export class EstateDocumentNested {
  documentId: string;
  head: string;
  extension: string;
  docType: number;
  lastChanged: Date; // ISO date
}

export enum BrokerRole {
  MAIN_BROKER = 1,
  RESPONSIBLE_BROKER = 2,
  ASSISTANT = 3,
  RESPONSIBLE_SETTLEMENT = 4,
}

export class EstateBrokerIdWithRolesNested {
  employeeId: string;
  brokerRole: BrokerRole;
  employee: {
    image: {
      large: string;
      medium: string;
      small: string;
    };
    slug: string;
    name: string;
    email: string;
    title: string;
    mobilePhone: string;
    workPhone: string;
  };
}

export class EstateBidNested {
  bidId: number;
  type: number;
  time: Date;
  amount: number;
  partyid: number;
  expires: Date;
  reservations: boolean;
  accepted: boolean;
  changedDate: Date;
  rejectedDate: Date | null;
}

export type EstateAd = {
  id: number;
  preview: boolean;
  channel: number;
  finnAdType: number;
  adStatus: number;
  publishStart: Date;
  publishEnd: Date;
  lastChanged: Date;
};

export type EstateSeller = {
  contactId: string;
  contactType: number;
  companyName: string;
  firstName: string;
  lastName: string;
  mobilePhone: string;
};

export enum CheckListValue {
  NO = 0,
  YES = 1,
  NOT_RELEVANT = 2,
}

export enum CheckListTag {
  ACTIVATE_OTP_FOR_PROJECT = 'AKTIVERPROTOKOLL_PROSJEKT',
  DEACTIVATE_OTP_FOR_SALE = 'DEAKTIVERPROTOKOLL_SALG',
  DEACTIVATE_OTP_FOR_SETTLEMENT = 'DEAKTIVERPROTOKOLL_OPPGJOPPDRAG',

  ACTIVATE_PEP_SELLER_FOR_SALE = 'AMLINFOSELGER_SALG',
  ACTIVATE_PEP_SELLER_FOR_SETTLEMENT = 'AMLINFOSELGER_OPPGJORSOPPDRAG',
  ACTIVATE_PEP_SELLER_FOR_FORECLOSURE = 'AMLINFOSELGER_TVANG',
  ACTIVATE_PEP_SELLER_FOR_PROJECT = 'AMLINFOSELGER_KONTRAKT',
  ACTIVATE_PEP_SELLER_FOR_EVALUATION = 'AMLINFOSELGER_VV',

  ACTIVATE_SETTLEMENT_SELLER_FOR_SALE = 'OPPGJOR_SELGER_SALG',
  ACTIVATE_SETTLEMENT_SELLER_FOR_SETTLEMENT = 'OPPGJOR_SELGER_OPPGJORSOPPDRAG',
  ACTIVATE_SETTLEMENT_SELLER_FOR_FORECLOSURE = 'OPPGJOR_SELGER_TVANG',
  ACTIVATE_SETTLEMENT_SELLER_FOR_PROJECT = 'OPPGJOR_SELGER_KONTRAKT',

  ACTIVATE_SETTLEMENT_BUYER_FOR_SALE = 'OPPGJOR_KJOPER_SALG',
  ACTIVATE_SETTLEMENT_BUYER_FOR_SETTLEMENT = 'OPPGJOR_KJOPER_OPPGJORSOPPDRAG',
  ACTIVATE_SETTLEMENT_BUYER_FOR_FORECLOSURE = 'OPPGJOR_KJOPER_TVANG',
  ACTIVATE_SETTLEMENT_BUYER_FOR_PROJECT = 'OPPGJOR_KJOPER_PROSJEKT',
  ACTIVATE_SETTLEMENT_BUYER_FOR_PROJECT_ADVANCE = 'OPPGJOR_KJOPER_FORSKUDD',

  FOR_SALE_CONFIRMED = 'BEKREFTET_TILSALGS',

  OFF_MARKET = 'OFFMARKET',
}

export enum EstateBaseType {
  UNKNOWN = -1,
  NOT_SET = 0,
  DETACHED = 1,
  LEISURE = 2,
  BUSINESS = 3,
  PROJECT = 4,
  PLOT = 5,
}

export type CheckListItem = {
  tags: string[];
  value: CheckListValue;
  changedBy: string;
  changedDate: Date;
};

export class CheckList {
  checkListItems: CheckListItem[];
  lastChanged: Date;
}

@Schema({ collection: 'estates', timestamps: true })
export class Estate {
  @Prop()
  estateId: string;

  @Prop()
  assignmentNum: string;

  @Prop()
  assignmentType: number;

  @Prop()
  assignmentTypeGroup: EstateAssignmentTypeGroup;

  @Prop()
  systemId: string;

  @Prop()
  finnCode: string;

  @Prop()
  finnPublishDate: Date;

  @Prop()
  finnExpireDate: Date;

  @Prop()
  takeOverDate: Date;

  @Prop()
  contractMeetingDate: Date;

  @Prop()
  changedDate: Date;

  @Prop()
  address: EstateAddressNested;

  @Prop()
  ownership: number;

  @Prop()
  employeeId: string;

  @Prop()
  brokersIdWithRoles: EstateBrokerIdWithRolesNested[];

  @Prop()
  departmentId: number;

  @Prop()
  status: number;

  @Prop()
  defaultImageId: string;

  @Prop()
  defaultImage: EstateImageNested;

  @Prop()
  heading: string;

  @Prop()
  showings: { start: string; end: string; showingId: string }[];

  @Prop()
  showingNote: string;

  @Prop()
  noOfRooms: number;

  @Prop()
  noOfBedRooms: number;

  @Prop()
  textFields: mongoose.Schema.Types.Mixed;

  @Prop()
  floor: number;

  @Prop()
  facilities: [];

  @Prop()
  estatePreferences: [];

  @Prop()
  constructionYear: number;

  @Prop()
  energyLetter: number;

  @Prop()
  energyColorCode: number;

  @Prop()
  plot: EstatePlotNested;

  @Prop()
  partOwnership: mongoose.Schema.Types.Mixed;

  @Prop()
  estatePrice: EstatePriceNested;

  @Prop()
  estateSize: EstateSizeNested;

  @Prop()
  buildings: EstateBuildingNested[];

  @Prop()
  valuationTax: EstateValuationTaxNested;

  @Prop()
  links: string[];

  @Prop()
  commissionAcceptedDate: Date;

  @Prop()
  soldDate: Date;

  @Prop()
  estateTypeId: string;

  @Prop()
  estateType: string;

  @Prop()
  estateBaseType: EstateBaseType;

  @Prop()
  estateTypeExternal: number;

  @Prop()
  createdDate: Date;

  @Prop()
  location: EstateLocationNested;

  @Prop()
  matrikkel: LandIdentificationMatrix[];

  @Prop()
  municipality: string;

  @Prop()
  municipalityId: string;

  @Prop()
  takeoverComment: string;

  @Prop()
  appraiserContactId: string;

  @Prop()
  appraiserContact: Contact;

  @Prop()
  businessManagerContact: Contact;

  @Prop()
  tag: string;

  @Prop()
  projectId: string;

  @Prop()
  projectRelation: number;

  @Prop()
  projectUnits: EstateProjectUnitNested[];

  @Prop()
  projectTextFields: mongoose.Schema.Types.Mixed;

  @Prop()
  projectName: string;

  @Prop()
  activities: { start: Date; end: Date; type: number; name: string }[];

  @Prop()
  checkList: CheckList;

  @Prop()
  statusChanges: { from: number; to: number; date: Date }[];

  @Prop()
  url: string;

  @Prop()
  sellers: EstateSeller[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: EMPLOYEE_MODEL_NAME })
  employee: Employee;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: DEPARTMENT_MODEL_NAME })
  department: Department;

  @Prop()
  areaId: string;

  @Prop()
  bids: EstateBidNested[];

  @Prop()
  documents: EstateDocumentNested[];

  @Prop()
  contacts: {
    contactId: string;
    comments: {
      comment: string;
      changedDate: Date;
    }[];
    proxyId: string;
    relationType: number;
  }[];

  @Prop()
  buyers: {
    contactId: string;
    contactType: number;
    companyName: string;
    firstName: string;
    lastName: string;
    mobilePhone: string;
  }[];

  @Prop()
  proxies: {
    proxyOf: string[];
    contactId: string;
    contactType: number;
    companyName: string;
    firstName: string;
    lastName: string;
    mobilePhone: string;
  }[];

  @Prop()
  lastImageChangeDate: Date | null;

  @Prop()
  expireDate: Date | null;

  @Prop()
  lastSuccessfulSyncStartDate: Date;

  @Prop()
  ads?: EstateAd[];
}

export const EstateSchema = SchemaFactory.createForClass(Estate);

export function isCheckListItemChecked(checkList: CheckList, tag: string): boolean {
  return Boolean(
    checkList?.checkListItems?.find((item) => item.value === CheckListValue.YES && item.tags?.includes(tag)),
  );
}

export function getBuildingAreaSize(building: EstateBuildingNested, areaTypes: AreaType[]): number {
  return building.buildingArea.reduce((acc, area) => {
    if (areaTypes.includes(area.areaType)) {
      return acc + area.areaInformation.reduce((acc, info) => acc + info.areaSize, 0);
    }
    return acc;
  }, 0);
}

export function getEstateArea(buildings: EstateBuildingNested[], areaTypes: AreaType[]): number {
  return buildings.reduce((acc, building) => acc + getBuildingAreaSize(building, areaTypes), 0);
}

export function getBRA_i(buildings: EstateBuildingNested[]): number {
  // https://linear.app/nordvik/issue/NOR-8/changes-for-new-area-units-on-estates
  return getEstateArea(buildings, [AreaType.UsageAreaI]);
}
