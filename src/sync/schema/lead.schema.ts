import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type LeadDocument = Lead & Document;

@Schema({ collection: 'leads', timestamps: true })
export class Lead {
  @Prop()
  leadId: string;

  @Prop()
  date: Date;

  @Prop()
  source: string;

  @Prop()
  officeId: string;

  @Prop()
  employeeEmail: string;

  @Prop()
  tipId: string;
}

export const LeadSchema = SchemaFactory.createForClass(Lead);
