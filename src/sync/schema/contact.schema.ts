import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ContactDocument = Contact & Document;

export enum ContactType {
  INTERESTED,
  VIEWING,
}

@Schema({ collection: 'contacts', timestamps: true })
export class Contact {
  @Prop()
  contactId: string;

  @Prop()
  address: string;

  @Prop()
  city: string;

  @Prop()
  changedDate: Date;

  @Prop()
  companyName: string;

  @Prop()
  consents: unknown[];

  @Prop()
  contactType: ContactType;

  @Prop()
  customerReview: string;

  @Prop()
  departmentId: number;

  @Prop()
  email: string;

  @Prop()
  firstName: string;

  @Prop()
  lastName: string;

  @Prop()
  mobilePhone: string;

  @Prop()
  organisationNumber: string;

  @Prop()
  postalAddress: string;

  @Prop()
  postalCode: string;

  @Prop()
  privatePhone: string;

  @Prop()
  workPhone: string;
}

export const ContactSchema = SchemaFactory.createForClass(Contact);
