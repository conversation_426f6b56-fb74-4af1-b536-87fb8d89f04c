import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoose from 'mongoose';
import { Estate } from './estate.schema';
import { ESTATE_MODEL_NAME } from './model-names';

export type SellerDocument = Seller & Document;

@Schema({ collection: 'sellers', timestamps: true })
export class Seller {
  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: ESTATE_MODEL_NAME }],
  })
  estates: Estate[];

  @Prop()
  smsNotificationSent: boolean;

  @Prop()
  smsNotificationSentDate: Date;

  @Prop()
  contactId: string;

  @Prop()
  contactType: number;

  @Prop()
  organisationNumber: string;

  @Prop()
  lastName: string;

  @Prop()
  firstName: string;

  @Prop()
  companyName: string;

  @Prop()
  mobilePhone: string;

  @Prop()
  email: string;

  @Prop()
  changedDate: Date;
}

export const SellerSchema = SchemaFactory.createForClass(Seller);
