import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TipDocument = Tip & Document;

@Schema({ collection: 'tips', timestamps: true })
export class Tip {
  @Prop()
  tipId: string;

  @Prop()
  created: Date;

  @Prop()
  modified: Date;

  @Prop()
  status: number;

  @Prop()
  userId: string;

  @Prop()
  departmentId: string;

  @Prop()
  contactId: string;

  @Prop()
  firstName: string;

  @Prop()
  lastName: string;

  @Prop()
  mobilePhone: string;

  @Prop()
  email: string;

  @Prop()
  estateId: string;

  @Prop()
  postalCode: string;

  @Prop()
  streetAdress: string;

  @Prop()
  comment: string;

  @Prop()
  source: string;

  @Prop()
  productId: string;

  @Prop()
  recipientId: string;

  @Prop()
  originationType: number;
}

export const TipSchema = SchemaFactory.createForClass(Tip);
