import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import * as mongoose from 'mongoose';
import { Department } from './department.schema';
import { DEPARTMENT_MODEL_NAME } from './model-names';

export type EmployeeDocument = Employee & Document;

export class EmployeeImageNested {
  large: string;
  medium: string;
  small: string;
}

@Schema({ collection: 'employees', timestamps: true })
export class Employee {
  @Prop()
  image: EmployeeImageNested;

  @Prop()
  departmentId: number[];

  @Prop()
  estates: string[];

  @Prop()
  employeeId: string;

  @Prop()
  title: string;

  @Prop()
  name: string;

  @Prop()
  mobilePhone: string;

  @Prop()
  workPhone: string;

  @Prop()
  email: string;

  @Prop()
  employeeActive: boolean;

  @Prop()
  changedDate: Date;

  @Prop()
  slug: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: DEPARTMENT_MODEL_NAME })
  department: Department;

  @Prop()
  aboutMe: string;

  @Prop()
  student: boolean;

  @Prop()
  webPublish: boolean;

  @Prop()
  imageTimestamp: Date;
}

export const EmployeeSchema = SchemaFactory.createForClass(Employee);
