import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoose from 'mongoose';
import { Estate } from './estate.schema';
import { ESTATE_MODEL_NAME } from './model-names';

export type DepartmentDocument = Department & Document;

export type Commission = {
  employeeId: string;
  departmentId: number;
  period: string;
  articleSummaries: ArticleSummary[];
};

export type ArticleSummary = {
  articleId: string;
  amount: number;
};

@Schema({ collection: 'departments', timestamps: true })
export class Department {
  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: ESTATE_MODEL_NAME }],
  })
  estates: Estate[];

  @Prop()
  commissions: Commission[];

  @Prop()
  departmentId: number;

  @Prop()
  name: string;

  @Prop()
  organisationNumber: string;

  @Prop()
  legalName: string;

  @Prop()
  phone: string;

  @Prop()
  email: string;

  @Prop()
  streetAddress: string;

  @Prop()
  postalAddress: string;

  @Prop()
  postalCode: string;

  @Prop()
  city: string;

  @Prop()
  slug: string;

  @Prop()
  marketName: string;
}

export const DepartmentSchema = SchemaFactory.createForClass(Department);
