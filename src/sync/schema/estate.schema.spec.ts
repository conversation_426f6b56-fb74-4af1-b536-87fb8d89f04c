import { CheckList, CheckListValue, isCheckListItemChecked } from './estate.schema';

describe('Estate Schema', () => {
  describe('hasCheckListItemChecked', () => {
    it('should not find the tag if checklist is not there', () => {
      const checkList: CheckList = undefined;

      const result = isCheckListItemChecked(checkList, 'AAA');
      expect(result).toBeFalsy();
    });

    it('should not find the tag if no items in checklist', () => {
      const checkList: CheckList = {
        checkListItems: [],
        lastChanged: undefined,
      };

      const result = isCheckListItemChecked(checkList, 'AAA');
      expect(result).toBeFalsy();
    });

    const complexCheckList: CheckList = {
      checkListItems: [
        {
          value: CheckListValue.NO,
          tags: ['AAA', 'BBB'],
          changedBy: '',
          changedDate: new Date(),
        },
        {
          value: CheckListValue.YES,
          tags: ['BBB', 'CCC'],
          changedBy: '',
          changedDate: new Date(),
        },
      ],
      lastChanged: undefined,
    };

    it('should not find the checklist item if its not checked', () => {
      const result = isCheckListItemChecked(complexCheckList, 'AAA');
      expect(result).toBeFalsy();
    });

    it('should find the checklist item if its checked in one item and unchecked on an other', () => {
      const result = isCheckListItemChecked(complexCheckList, 'BBB');
      expect(result).toBeTruthy();
    });

    it('should find the checklist item if its checked', () => {
      const result = isCheckListItemChecked(complexCheckList, 'CCC');
      expect(result).toBeTruthy();
    });
  });
});
