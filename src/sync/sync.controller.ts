import { Body, ConsoleLogger, Controller, NotFoundException, Param, Post } from '@nestjs/common';
import { indexOf } from 'lodash';
import { v4 } from 'uuid';
import Protected from '../authorization/protected.decorator';
import { VitecService } from '../vitec/vitec.service';
import { ArchivedEstateSyncService } from './services/archived-estate-sync.service';
import { OtpAutoFinalizationService } from './services/auto-finalization/otp-auto-finalization.service';
import { PEPFormAutoFinalizationService } from './services/auto-finalization/pep-form-auto-finalization.service';
import { SettlementBuyerFormAutoFinalizationService } from './services/auto-finalization/settlement-buyer-form-auto-finalization.service';
import { SettlementSellerFormAutoFinalizationService } from './services/auto-finalization/settlement-seller-form-auto-finalization.service';
import { DepartmentSyncService } from './services/department-sync.service';
import { EmployeeSyncService } from './services/employee-sync.service';
import { UpdateEstatePriceHistoriesService } from './services/estate-price-histories/update-estate-price-histories.service';
import { EstateSyncService } from './services/estate-sync.service';
import { TipSyncService } from './services/tip-sync.service';

@Controller('sync')
export class SyncController {
  private readonly logger = new ConsoleLogger(SyncController.name);

  constructor(
    private readonly vitecService: VitecService,
    private readonly archivedEstateSyncService: ArchivedEstateSyncService,
    private readonly departmentSyncService: DepartmentSyncService,
    private readonly employeeSyncService: EmployeeSyncService,
    private readonly estateSyncService: EstateSyncService,
    private readonly tipSyncService: TipSyncService,
    private readonly otpAutoFinalizationService: OtpAutoFinalizationService,
    private readonly settlementBuyerFormAutoFinalizationService: SettlementBuyerFormAutoFinalizationService,
    private readonly settlementSellerFormAutoFinalizationService: SettlementSellerFormAutoFinalizationService,
    private readonly pepFormAutoFinalizationService: PEPFormAutoFinalizationService,
    private readonly updateEstatePriceHistoriesService: UpdateEstatePriceHistoriesService,
  ) {}

  @Post('/trigger/archive-estates')
  @Protected()
  async triggerArchiveEstateSync() {
    this.logger.log('Triggering estate archive manually');
    return this.archivedEstateSyncService.archiveEstates();
  }

  @Post('/trigger/departments')
  @Protected()
  async triggerDepartmentsSync() {
    this.logger.log('Triggering departments update manually');
    return this.departmentSyncService.updateDepartments();
  }

  @Post('/trigger/departments/:departmentId')
  @Protected()
  async triggerDepartmentSync(@Param('departmentId') departmentId: number) {
    this.logger.log(`Triggering department update for department ${departmentId} manually`);

    const departments = await this.vitecService.getDepartments();
    const department = departments.find((department) => department.departmentId === departmentId);

    if (!department) {
      throw new NotFoundException('No such department');
    }

    return this.departmentSyncService.updateDepartment(department);
  }

  @Post('/trigger/employees')
  @Protected()
  async triggerEmployeesSync() {
    this.logger.log('Triggering employees update manually');
    return this.employeeSyncService.updateEmployees(null);
  }

  @Post('/trigger/employees/:employeeId')
  @Protected()
  async triggerEmployeeSync(@Param('employeeId') employeeId: string) {
    this.logger.log(`Triggering employee update for employee ${employeeId} manually`);

    const employees = await this.vitecService.getEmployees();
    const employee = employees.find((employee) => employee.employeeId === employeeId);

    if (!employee) {
      throw new NotFoundException('No such employee');
    }

    return this.employeeSyncService.updateEmployee(employee, null);
  }

  @Post('/trigger/estates')
  @Protected()
  async triggerEstatesSync() {
    this.logger.log('Triggering estates update manually');
    return this.estateSyncService.updateEstates(null);
  }

  @Post('/trigger/estates/:estateId')
  @Protected()
  async triggerEstateSync(@Param('estateId') estateId: string) {
    this.logger.log(`Triggering estate update for estate ${estateId} manually`);

    const estate = await this.vitecService.getEstate(estateId);

    if (!estate) {
      throw new NotFoundException('No such estate');
    }

    return this.estateSyncService.upsertEstate({ estate, syncReceiveDate: new Date(), _timeLogUniqueId: v4() });
  }

  @Post('/trigger/tips')
  @Protected()
  async triggerTipsSync() {
    this.logger.log('Triggering tips update manually');
    return this.tipSyncService.updateTips(null);
  }

  @Post('/trigger/otp-auto-finalization')
  @Protected()
  async triggerArchiveDocuments() {
    this.logger.log('Triggering OTP auto-finalization manually');
    await this.otpAutoFinalizationService.autoFinalizeOtps();
    this.logger.log('OTP auto-finalization finished');
  }

  @Post('/trigger/checklist')
  @Protected()
  async triggerChecklistSync(@Body() body: { estateIds: string[] }) {
    this.logger.log('Triggering checklist sync manually');
    for (const estateId of body.estateIds) {
      const current = indexOf(body.estateIds, estateId) + 1;
      const all = body.estateIds.length;
      this.logger.log(`CheckList syncing in progress for estate: ${estateId} ${current}/${all}`);
      await this.estateSyncService.updateCheckList(estateId, v4());
    }
    this.logger.log(`CheckList syncing finished`);
  }

  @Post('/trigger/settlement-buyer-form-auto-finalization')
  @Protected()
  async triggerSettlementBuyerFormAutoFinalization() {
    this.logger.log('Triggering SettlementBuyerForm auto-finalization manually');
    await this.settlementBuyerFormAutoFinalizationService.trigger();
    this.logger.log('SettlementBuyerForm auto-finalization finished');
  }

  @Post('/trigger/settlement-seller-form-auto-finalization')
  @Protected()
  async triggerSettlementSellerFormAutoFinalization() {
    this.logger.log('Triggering SettlementSellerForm auto-finalization manually');
    await this.settlementSellerFormAutoFinalizationService.trigger();
    this.logger.log('SettlementSellerForm auto-finalization finished');
  }

  @Post('/trigger/pep-form-auto-finalization')
  @Protected()
  async triggerPEPFormAutoFinalization() {
    this.logger.log('Triggering PEP Form auto-finalization manually');
    await this.pepFormAutoFinalizationService.trigger();
    this.logger.log('PEP Form auto-finalization finished');
  }

  @Post('/trigger/all-contact-information')
  @Protected()
  async triggerContactInformationSync() {
    this.logger.log('Triggering contact information updates by adding all non-archived estates to the queue');
    return this.estateSyncService.updateAllContactInformation();
  }

  @Post('/trigger/update-estate-price-histories/:id')
  @Protected()
  async triggerUpdateEstatePriceHistories(@Param('id') id: string) {
    this.logger.log(`Triggering UpdateEstatePriceHistories manually for id: ${id}`);
    await this.updateEstatePriceHistoriesService.triggerForOneEstate(id);
    this.logger.log(`UpdateEstatePriceHistories finished for id: ${id}`);
  }
  @Post('/trigger/update-estate-price-histories')
  @Protected()
  async triggerUpdateAllEstatePriceHistories() {
    this.logger.log(`Triggering UpdateEstatePriceHistories manually for every estate, async`);
    void this.updateEstatePriceHistoriesService.trigger();
    this.logger.log(
      `UpdateEstatePriceHistories finished for every estate, async, process is still running in the background`,
    );
  }

  /*
  @Post('/trigger/eiendomsverdi/estates')
  @Protected()
  async triggerEiendomsverdiEstatesSync() {
    this.logger.log('Triggering eiendomsverdi estates update manually');
    //await this.eiendomsverdiEstateSyncService.deleteSoldEstates();
    await this.eiendomsverdiEstateSyncService.createBoughtEstates();
    //await this.eiendomsverdiEstateSyncService.updateExistingEstates();
  }
   */
}
