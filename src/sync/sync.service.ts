import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MongoService } from '../mongo/mongo.service';
import { SyncState, SyncStateDocument } from './schema/sync-state';

@Injectable()
export class SyncService {
  constructor(
    private readonly mongoService: MongoService,
    @InjectModel(SyncState.name) private syncStateModel: Model<SyncStateDocument>,
  ) {}

  async getSyncState(jobId: string): Promise<SyncState | null> {
    const syncState = await this.syncStateModel.findOne({ jobId });

    if (!syncState) {
      return null;
    }

    return syncState;
  }

  async setSyncState(jobId: string, syncState: Partial<SyncState>): Promise<void> {
    await this.mongoService.upsert(this.syncStateModel, { jobId }, syncState);
  }
}
