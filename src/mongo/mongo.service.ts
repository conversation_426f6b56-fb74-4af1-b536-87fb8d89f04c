import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import _ from 'lodash';
import { Connection, Document, FilterQuery, Model, UpdateQuery } from 'mongoose';

@Injectable()
export class MongoService {
  private readonly logger = new ConsoleLogger(MongoService.name);
  private readonly journal = new ConsoleLogger('MongoJournal');

  constructor(@InjectConnection() private connection: Connection) {}

  async runInTransaction(callback: () => void) {
    try {
      this.logger.debug('Starting transaction');
      const session = await this.connection.startSession();
      session.startTransaction();

      try {
        await callback();
        await session.commitTransaction();
        this.logger.debug('Transaction committed');
      } catch (error) {
        await session.abortTransaction();
        this.logger.debug('Transaction aborted');
        this.logger.error(`Error while executing transaction: ${error}`);
        throw error;
      } finally {
        await session.endSession();
      }
    } catch (error) {
      throw error;
    }
  }

  async logUpsert<T extends Document>(
    model: Model<T>,
    oldDocument: T | null,
    filter?: FilterQuery<T>,
    update?: UpdateQuery<T>,
  ) {
    const entry = {
      collection: model.collection.name,
      type: oldDocument === null ? 'insert' : 'update',
      where: filter,
      changes: Object.getOwnPropertyNames(update).reduce(
        (acc: { property: string; oldValue: any; newValue: any }[], property: string) => {
          if (oldDocument === null) {
            return [...acc, { property, oldValue: undefined, newValue: update[property] }];
          } else if (property in oldDocument) {
            const oldValue = oldDocument[property];
            const newValue =
              typeof update[property] === 'object' && update[property] !== null && '_id' in update[property]
                ? update[property]['_id']
                : update[property];

            if (!_.isEqual(oldValue, newValue)) {
              return [...acc, { property, oldValue, newValue }];
            }
          }

          return acc;
        },
        [],
      ),
    };

    if (entry.changes.length > 0) {
      this.journal.log(JSON.stringify(entry));
    }
  }

  async upsert<T extends Document>(model: Model<T>, filter?: FilterQuery<T>, update?: UpdateQuery<T>): Promise<T> {
    const newValue = await model.findOneAndUpdate(filter, update, {
      useFindAndModify: false,
      upsert: true,
      new: true,
    });

    //await this.logUpsert(model, oldValue, filter, update);

    return newValue;
  }
}
