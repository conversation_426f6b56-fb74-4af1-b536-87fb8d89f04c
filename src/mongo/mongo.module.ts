import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import AppConfigService from '../app-config/app-config.service';
import AppConfigModule from '../app-config/app-config.module';
import { MongoService } from './mongo.service';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [AppConfigModule],
      useFactory: async (appConfigService: AppConfigService) => ({
        uri: appConfigService.getMongoDbUri(),
      }),
      inject: [AppConfigService],
    }),
  ],
  providers: [MongoService],
  exports: [MongoService],
})
export default class MongoModule {}
