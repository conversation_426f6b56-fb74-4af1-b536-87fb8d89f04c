import { ConsoleLogger, Injectable } from '@nestjs/common';
import sgMail from '@sendgrid/mail';

import { readFile } from 'fs/promises';
import Mustache from 'mustache';
import AppConfigService from '../app-config/app-config.service';
import { MailAuditType } from '../pg/mail-audit/mail-audit.model';
import { MailAuditService } from '../pg/mail-audit/mail-audit.service';

@Injectable()
export class MailService {
  private readonly logger = new ConsoleLogger(MailService.name);

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly mailAuditService: MailAuditService,
  ) {
    sgMail.setApiKey(this.appConfigService.getSendgridApikey());
  }

  async sendMail(mailData: {
    to: string;
    html: string;
    subject: string;
    from?: string;
    mailAuditType?: MailAuditType;
  }): Promise<boolean> {
    const from = mailData.from || '<EMAIL>';
    this.logger.log(`Triggering email notification for ${mailData.to}`);
    await this.mailAuditService.create(mailData.mailAuditType || MailAuditType.SEND_MAIL, from, mailData.to, {
      subject: mailData.subject,
      html: mailData.html,
    });

    await sgMail.send({
      ...mailData,
      html: await this.getGenericTemplate(mailData.html),
      from,
    });

    return true;
  }

  async getGenericTemplate(text: string): Promise<string> {
    const templateBuffer = await this.getEmailTemplateBuffer('generic.template.html');
    return Mustache.render(templateBuffer.toString(), {
      text,
      envUrl: this.appConfigService.getAppUrl(),
    });
  }

  private async getEmailTemplateBuffer(fileName: string): Promise<string> {
    return readFile(`src/mail/templates/${fileName}`, { encoding: 'utf-8' });
  }
}
