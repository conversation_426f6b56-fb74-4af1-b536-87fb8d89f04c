import { Module, ValidationPipe } from '@nestjs/common';
import { APP_FILTER, APP_PIPE } from '@nestjs/core';
import AppConfigModule from './app-config/app-config.module';
import AuthenticationModule from './authentication/authentication.module';
import AuthorizationModule from './authorization/authorization.module';
import { HealthController } from './endpoints/status';
import AllExceptionsFilter from './exceptions/all-exceptions.filter';
import NotificationModule from './notification/notification.module';
import AreasModule from './pg/area/area.module';
import AverageSalePriceModule from './pg/average-sale-price/average-sale-price.module';
import EstatesModule from './pg/estate/estate.module';
import FavoritesModule from './pg/favorite/favorite.module';
import FeedsModule from './pg/feed/feed.module';
import LipscoreAuditModule from './pg/lipscore-audit/lipscore-audit.module';
import LipscoreBrokerRatingModule from './pg/lipscore-broker-rating/lipscore-broker-rating.module';
import PostgresModule from './pg/pg.module';
import UserFirebaseTokenModule from './pg/user-firebase-token/user-firebase-token.module';
import UsersModule from './pg/user/user.module';
import UserAreaModule from './pg/userarea/userarea.module';
import S3Module from './s3/s3.module';
import SyncModule from './sync/sync.module';
import VitecModule from './vitec/vitec.module';

@Module({
  imports: [
    AppConfigModule,
    AuthenticationModule,
    AuthorizationModule,
    VitecModule,
    SyncModule,
    S3Module,
    PostgresModule,
    UsersModule,
    NotificationModule,
    FeedsModule,
    UserFirebaseTokenModule,
    EstatesModule,
    FavoritesModule,
    UserAreaModule,
    AreasModule,
    LipscoreAuditModule,
    LipscoreBrokerRatingModule,
    AverageSalePriceModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        transform: true,
        whitelist: true,
      }),
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
  controllers: [HealthController],
})
export class AppModule {}
