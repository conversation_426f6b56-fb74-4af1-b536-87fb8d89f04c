import { ArgumentsHost, Catch, Console<PERSON>ogger, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import ExceptionDto from './dto/exception.dto';

@Catch()
export default class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new ConsoleLogger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    let json: ExceptionDto = {
      timestamp: new Date(),
      endpoint: request.url,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    };

    if (exception instanceof HttpException) {
      json = {
        ...json,
        statusCode: exception.getStatus(),
      };

      if (typeof exception.getResponse() === 'string') {
        json = {
          ...json,
          message: exception.getResponse() as string,
        };
      } else {
        json = {
          ...json,
          ...(exception.getResponse() as Record<string, any>),
        };
      }
    }

    this.logger.error(`Endpoint ${request.url} threw error ${exception} and returned ${JSON.stringify(json)}`);

    response.status(json.statusCode).json(json);
  }
}
