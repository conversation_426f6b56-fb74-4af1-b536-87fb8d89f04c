import { ConsoleLogger, HttpStatus, Injectable } from '@nestjs/common';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import rateLimit from 'axios-rate-limit';
import axiosRetry, { isNetworkOrIdempotentRequestError } from 'axios-retry';
import { plainToClass } from 'class-transformer';
import * as fs from 'fs';
import urljoin from 'url-join';
import AppConfigService from '../app-config/app-config.service';
import ActivityDto from './dto/activity.dto';
import AdsDto from './dto/ads.dto';
import BaseCommissionDto from './dto/base-commission.dto';
import BidDto from './dto/bid.dto';
import CheckListItemDto from './dto/check-list-item.dto';
import CheckListDto from './dto/check-list.dto';
import ContactInformationDto from './dto/contact-information.dto';
import ContactRelationsDto from './dto/contact-relations.dto';
import ContactDto from './dto/contact.dto';
import DepartmentDto from './dto/department.dto';
import DocumentDto from './dto/document.dto';
import EmployeeDto from './dto/employee.dto';
import EstateMetadataDto from './dto/estate-metadata.dto';
import EstateDto from './dto/estate.dto';
import ProjectDto from './dto/project.dto';
import TipsDto from './dto/tips.dto';

export type EstateGallery = {
  data: {
    estate: {
      estateId: string;
      images: [
        {
          url: {
            small: string;
            medium: string;
            large: string;
          };
        },
      ];
    };
  };
};

@Injectable()
export class VitecService {
  private readonly logger = new ConsoleLogger(VitecService.name);
  private readonly httpClient: AxiosInstance;

  constructor(private readonly appConfigService: AppConfigService) {
    this.httpClient = rateLimit(axios.create(), {
      maxRequests: this.appConfigService.getVitecRateLimitMax(),
      perMilliseconds: this.appConfigService.getVitecRateLimitTimeWindow(),
    });

    axiosRetry(this.httpClient as unknown as AxiosInstance, {
      retries: this.appConfigService.getVitecRequestMaxRetry(),
      retryDelay: () => {
        return this.appConfigService.getVitecRequestRetryAfter();
      },
      retryCondition: (error) => {
        return isNetworkOrIdempotentRequestError(error) || error.response.status === HttpStatus.TOO_MANY_REQUESTS;
      },
    });
  }

  async getTips(changedAfter: Date = new Date()): Promise<TipsDto> {
    try {
      const response = await this.getRequest(['Tips'], { changedAfter: changedAfter.toISOString() });

      return plainToClass(TipsDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch tips due to ${error}`);
      return null;
    }
  }

  async getEstateMetadata(departmentId: number): Promise<EstateMetadataDto[]> {
    const response = await this.getRequest(['Estates'], { departmentId });

    const estateMetadata = response.data.map((entry) =>
      plainToClass(EstateMetadataDto, entry, { excludeExtraneousValues: true }),
    );
    this.logger.debug(`Fetched ${estateMetadata.length} estate metadata for department ${departmentId}`);

    return estateMetadata;
  }

  async getEstate(estateId: string): Promise<EstateDto | null> {
    try {
      const response = await this.getRequest(['Estates', estateId]);

      return plainToClass(EstateDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch estate ${estateId} due to ${error}`);
      return null;
    }
  }

  async getProject(estateId: string): Promise<ProjectDto | null> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'Project']);

      return plainToClass(ProjectDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch project for estate ${estateId} due to ${error}`);
      return null;
    }
  }

  async getContact(contactId: string): Promise<ContactDto | null> {
    try {
      const response = await this.getRequest(['Contacts', contactId]);

      return plainToClass(ContactDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch contact with id ${contactId} due to ${error}`);
      return null;
    }
  }

  async getContactInformation(estateId: string): Promise<ContactInformationDto | null> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'ContactInformation']);

      return plainToClass(ContactInformationDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch contact information for estate ${estateId} due to ${error}`);
      return null;
    }
  }

  async getContactRelations(estateId: string): Promise<ContactRelationsDto | null> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'ContactRelations']);

      return plainToClass(ContactRelationsDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch contact relations for estate ${estateId} due to ${error}`);
      return null;
    }
  }

  async getActivities(estateId: string): Promise<ActivityDto[]> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'Activities']);

      const activities = response.data.map((entry) =>
        plainToClass(ActivityDto, entry, { excludeExtraneousValues: true }),
      );
      this.logger.debug(`Fetched ${activities.length} activities`);

      return activities;
    } catch (error) {
      this.logger.warn(`Could not fetch activities for estate ${estateId} due to ${error}`);
      return [];
    }
  }

  async getCheckList(estateId: string): Promise<CheckListDto | null> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'CheckList']);

      const checkListItems = response.data.checkListItems.map((item) =>
        plainToClass(CheckListItemDto, item, { excludeExtraneousValues: true }),
      );
      return plainToClass(CheckListDto, { ...response.data, checkListItems }, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(`Could not fetch checklist for estate ${estateId} due to ${error}`);
      return null;
    }
  }

  async getBids(estateId: string): Promise<BidDto[]> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'Bids']);

      const bids = response.data.map((entry) => plainToClass(BidDto, entry, { excludeExtraneousValues: true }));
      this.logger.debug(`Fetched ${bids.length} bids`);

      return bids;
    } catch (error) {
      this.logger.warn(`Could not fetch bids for estate ${estateId} due to ${error}`);
      return [];
    }
  }

  async getDocuments(estateId: string): Promise<DocumentDto[]> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'Documents']);

      const documents = response.data.map((entry) =>
        plainToClass(DocumentDto, entry, { excludeExtraneousValues: true }),
      );
      this.logger.debug(`Fetched ${documents.length} documents`);

      return documents;
    } catch (error) {
      this.logger.warn(`Could not fetch documents for estate ${estateId} due to ${error}`);
      return [];
    }
  }

  async getDepartments(): Promise<DepartmentDto[]> {
    try {
      const response = await this.getRequest(['Departments']);

      const departments = response.data.map((entry) =>
        plainToClass(DepartmentDto, entry, { excludeExtraneousValues: true }),
      );
      this.logger.debug(`Fetched ${departments.length} departments`);

      return departments;
    } catch (error) {
      this.logger.warn(`Could not fetch departments due to ${error}`);
      return [];
    }
  }

  async getBaseCommission(departmentId: number, period: string): Promise<BaseCommissionDto | null> {
    try {
      const response = await this.getRequest(['Employees', 'BaseCommission'], { departmentId, period });

      return plainToClass(BaseCommissionDto, response.data, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.warn(
        `Could not fetch base commission for department ID ${departmentId} and period ${period} due to ${error}`,
      );
      return null;
    }
  }

  async getEmployees(): Promise<EmployeeDto[]> {
    try {
      const response = await this.getRequest(['Employees']);

      const employees = response.data.map((entry) =>
        plainToClass(EmployeeDto, entry, { excludeExtraneousValues: true }),
      );
      this.logger.debug(`Fetched ${employees.length} employees`);
      return employees;
    } catch (error) {
      this.logger.warn(`Could not employees due to ${error}`);
      return [];
    }
  }

  async downloadEmployeeImage(employeeId: string, destinationPath: string): Promise<void> {
    await this.downloadImage(['Employees', employeeId, 'Picture'], destinationPath);
  }

  async downloadEstateImage(estateId: string, imageId: string, destinationPath: string): Promise<void> {
    await this.downloadImage(['Estates', estateId, 'Images', imageId], destinationPath);
  }

  async getEstateAds(estateId: string): Promise<AdsDto[]> {
    try {
      const response = await this.getRequest(['Estates', estateId, 'Ads']);

      const ads = response.data.ads.map((entry) => plainToClass(AdsDto, entry, { excludeExtraneousValues: true }));
      this.logger.debug(`Fetched ${ads.length} ads`);

      return ads;
    } catch (error) {
      this.logger.warn(`Could not fetch ads for estate ${estateId} due to ${error}`);
      return [];
    }
  }

  async fetchGalleryNordvikbolig(estateVitecId: string): Promise<EstateGallery> {
    const query = encodeURIComponent(`{ estate(id:"${estateVitecId}") { estateId, images { url } } }`);
    const response = await this.httpClient.get(`https://api.nordvikbolig.no/graphql?query=${query}`);
    return response.data;
  }

  async sendSMSViaVitec({
    contactId,
    departmentId,
    estateId,
    fromEmployeeId,
    message,
    to,
  }: {
    to: string;
    message: string; // If multiline message only use \n as breakline, no multiline string expression
    fromEmployeeId: string; // like ANSO
    departmentId: number;
    estateId: string;
    contactId: string;
  }): Promise<AxiosResponse<string>> {
    return this.postRequestWithQueryParamEncoding({
      endpointPathPiecesAfterInstallationId: ['Next', 'SendSMS'],
      queryParams: {
        fromEmployeeId: fromEmployeeId ?? '',
        useSenderFallback: true,
        recipientNumber: to,
        text: message,
        departmentId,
        contactId,
        estateId,
        source: 'Nordvik_Eiendomsmegling',
      },
    });
  }

  async postActivity({
    employeeId,
    estateId,
  }: {
    estateId: string;
    employeeId: string;
  }): Promise<AxiosResponse<string>> {
    const nowDate = new Date().toISOString();
    return this.postRequestWithQueryParamEncoding({
      endpointPathPiecesAfterInstallationId: ['Activities'],
      body: {
        typeName: 'Kundetilfredshet',
        employeeId: employeeId,
        start: nowDate,
        end: nowDate,
        title: 'Forespørsel om tilbakemelding er sendt',
        note: 'Det ble i dag sendt ut en forespørsel til boligselger om å vurdere deg. Hvis boligselger ikke svarer, så vil de motta en ny forespørsel om fem dager.',
        estateId: estateId,
        doneDate: nowDate,
      },
    });
  }

  private async getRequest(
    endpointPathPiecesAfterInstallationId: string[],
    queryParams?: Record<string, any>,
  ): Promise<AxiosResponse> {
    const url = this.formatUrl(endpointPathPiecesAfterInstallationId);
    this.logger.debug(`GET ${url} ${queryParams ? JSON.stringify(queryParams) : ''}`);

    return this.httpClient.get(url, {
      params: queryParams,
      auth: {
        username: this.appConfigService.getVitecUsername(),
        password: this.appConfigService.getVitecPassword(),
      },
    });
  }

  private async postRequestWithQueryParamEncoding({
    endpointPathPiecesAfterInstallationId,
    queryParams,
    body,
  }: {
    endpointPathPiecesAfterInstallationId: string[];
    queryParams?: Record<string, number | string | boolean>;
    body?: any;
  }): Promise<AxiosResponse> {
    const url = this.formatUrl(endpointPathPiecesAfterInstallationId);
    this.logger.debug(`POST ${url} ${queryParams ? JSON.stringify(queryParams) : ''}`);

    // Axios does normalization of queryParams by default
    return this.httpClient.post(url, body, {
      params: queryParams,
      auth: {
        username: this.appConfigService.getVitecUsername(),
        password: this.appConfigService.getVitecPassword(),
      },
    });
  }

  private async downloadImage(endpoint: string[], destinationPath: string): Promise<AxiosResponse> {
    const url = this.formatUrl(endpoint);
    this.logger.debug(`GET ${url}`);

    const response = await this.httpClient.get(url, {
      responseType: 'stream',
      auth: {
        username: this.appConfigService.getVitecUsername(),
        password: this.appConfigService.getVitecPassword(),
      },
    });

    const writer = fs.createWriteStream(destinationPath);

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  }

  formatUrl(endpoint: string[]): string {
    return urljoin(
      this.appConfigService.getVitecBaseUrl(),
      this.appConfigService.getVitecInstallationId(),
      ...endpoint,
    );
  }
}
