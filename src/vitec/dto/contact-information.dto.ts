import { Expose } from 'class-transformer';

export class ContactInformationEntryDto {
  contactId: string;
  departmentId: number;
  contactType: number;
  companyName: string;
  organisationNumber: string;
  firstName: string;
  lastName: string;
  mobilePhone: string;
  privatePhone: string;
  workPhone: string;
  email: string;
  socialSecurity: string;
  address: string;
  postalAddress: string;
  postalCode: string;
  city: string;
  consents: {
    type: number;
    value: boolean;
    changedDate: Date;
    changedBy: string;
  }[];
  customerReview: string;
  changedDate: Date;
}

export class ProxyDto {
  contactId: string;
  proxy: ContactInformationEntryDto;
}

export default class ContactInformationDto {
  @Expose()
  sellers: ContactInformationEntryDto[];

  @Expose()
  buyers: ContactInformationEntryDto[];

  @Expose()
  proxies: ProxyDto[];
}
