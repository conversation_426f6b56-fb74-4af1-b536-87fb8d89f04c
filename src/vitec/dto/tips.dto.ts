import { Expose } from 'class-transformer';

export type ApiTip = {
  originType: number;
  created: Date;
  modified: Date;
  status: number;
  userId: string;
  departmentId: string;
  contactId: string;
  firstName: string;
  lastName: string;
  mobilePhone: string;
  email: string;
  estateId: string;
  postalCode: string;
  streetAdress: string;
  comment: string;
  source: string;
  productId: string;
  recipientId: string;
  tipId: string;
};

export default class TipsDto {
  @Expose()
  tips: {
    originType: number;
    tips: ApiTip[];
  }[];
}
