import { Expose } from 'class-transformer';

export default class ProjectDto {
  @Expose()
  createdDate: Date;

  @Expose()
  estateId: string;

  @Expose()
  sellerId: string;

  @Expose()
  projectName: string;

  @Expose()
  assignmentNum: string;

  @Expose()
  status: number;

  @Expose()
  departmentId: number;

  @Expose()
  employeeId: string;

  @Expose()
  p12Garanti: true;

  @Expose()
  p47Garanti: true;

  @Expose()
  p12Due: Date;

  @Expose()
  p47Due: Date;

  @Expose()
  p12Belop: number;

  @Expose()
  p47Belop: number;

  @Expose()
  textFields: { [key: string]: string };

  @Expose()
  projectUnits: {
    estateId: string;
    priceEstimate: number;
    usableArea: number;
    primaryRoomArea: number;
    descriptionItemAmountOfRooms: number;
    floor: number;
    estateType: string;
    status: number;
    purchaseCostsAmount: number;
    rentPrMonth: number;
    collectiveDebt: number;
    soldPrice: number;
    soldDate: Date;
    apartmentNumber: string;
  }[];

  @Expose()
  changedDate: Date;

  @Expose()
  parkingUnits: unknown[];
}
