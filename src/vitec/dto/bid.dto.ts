import { Expose, Type } from 'class-transformer';

export default class BidDto {
  @Expose()
  bidId: number;

  @Expose()
  type: number;

  @Expose()
  @Type(() => Date)
  time: Date;

  @Expose()
  amount: number;

  @Expose()
  partyid: number;

  @Expose()
  @Type(() => Date)
  expires: Date;

  @Expose()
  reservations: boolean;

  @Expose()
  accepted: boolean;

  @Expose()
  @Type(() => Date)
  changedDate: Date;

  @Expose()
  @Type(() => Date)
  rejectedDate: Date;
}
