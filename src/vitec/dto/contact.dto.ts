import { Expose } from 'class-transformer';

export default class ContactDto {
  @Expose()
  contactId: string;

  @Expose()
  departmentId: number;

  @Expose()
  contactType: number;

  @Expose()
  companyName: string;

  @Expose()
  organisationNumber: string;

  @Expose()
  firstName: string;

  @Expose()
  lastName: string;

  @Expose()
  mobilePhone: string;

  @Expose()
  privatePhone: string;

  @Expose()
  workPhone: string;

  @Expose()
  email: string;

  @Expose()
  address: string;

  @Expose()
  postalAddress: string;

  @Expose()
  postalCode: string;

  @Expose()
  city: string;

  @Expose()
  consents: {
    type: number;
    value: boolean;
    changedDate: Date;
    changedBy: string;
  }[];

  @Expose()
  changedDate: Date;

  @Expose()
  customerReview: string;
}
