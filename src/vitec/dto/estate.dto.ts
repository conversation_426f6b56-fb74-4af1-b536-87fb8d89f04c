import { Expose, Type } from 'class-transformer';
import { EstateBrokerIdWithRolesNested, EstateBuildingNested } from '../../sync/schema/estate.schema';

export enum EstateAssignmentTypeGroup {
  NOT_SET = 0,
  SALE = 1,
  RENT = 2,
  VALUATION = 3,
  SETTLEMENT = 4,
  FORECLOSURE = 5,
  COMMERCIAL_AREA = 6,
  PROJECT_SALE = 7,
  COMMERCIAL_SALE = 8,
  COMMERCIAL_RENT = 9,
  ADMINISTRATION = 10,
}

export enum VitecEstateStatus {
  OWN_ARCHIVED = -1, // does no exists on vitec
  REQUEST = 0,
  PREPARATION = 1,
  FOR_SALE = 2,
  OVERSOLD = 3,
  RESERVED = 4,
  ARCHIVED = 5,
  EXPIRED = 6,
  TERMINATED = 7,
}

export default class EstateDto {
  @Expose()
  estateId: string;

  @Expose()
  status: VitecEstateStatus;

  @Expose()
  changedDate: Date;

  @Expose()
  brokersIdWithRoles: EstateBrokerIdWithRolesNested[];

  @Expose()
  createdDate: Date;

  @Expose()
  @Type(() => Date)
  expireDate: Date;

  @Expose()
  heading: string;

  @Expose()
  assignmentNum: string;

  @Expose()
  systemId: string;

  @Expose()
  departmentId: number;

  @Expose()
  settleDepartmentId: number;

  @Expose()
  assignmentType: number;

  @Expose()
  assignmentTypeGroup: EstateAssignmentTypeGroup;

  @Expose()
  ownership: number;

  @Expose()
  estateTypeExternal: number;

  @Expose()
  estateBaseType: number;

  @Expose()
  estatePreferences: [];

  @Expose()
  showings: [];

  @Expose()
  showingNote: string;

  @Expose()
  noOfRooms: number;

  @Expose()
  noOfBedRooms: number;

  @Expose()
  floor: number;

  @Expose()
  constructionYear: number;

  @Expose()
  energyLetter: number;

  @Expose()
  energyColorCode: number;

  @Expose()
  rentalCost: number;

  @Expose()
  rentalTimeSpan: unknown;

  @Expose()
  availableDate: string;

  @Expose()
  plot: {
    owned: true;
    size: number;
    description: string;
  };

  @Expose()
  partOwnership: {
    partOrgNumber: string;
    partNumber: number;
  };

  @Expose()
  estatePrice: {
    changedDate: Date;
    priceSuggestion: number;
    soldPrice: number;
    estimatedValue: number;
    collectiveDebt: number;
    collectiveAssets: number;
    loanFare: number;
    communityTax: number;
    communityTaxYear: number;
    salesCostDescription: string;
    rent: {
      rentPrMonth: number;
      rentIncludes: unknown;
    };
    purchaseCostsAmount: number;
    totalPrice: number;
    totalPriceExclusiveCostsAndDebt: number;
    waterRate: number;
    waterRateDescription: number;
    waterRateYear: number;
    yearlySocietyTax: number;
    yearlyLeaseFee: number;
    leasingPartyTransportFee: number;
    originalAgreementPrice: number;
    additionalAgreementOptions: number;
    originalExpensesPrice: number;
    transportAgreementCosts: number;
  };

  @Expose()
  estateSize: {
    primaryRoomArea: number;
    primaryRoomAreaDescription: string;
    grossArea: number;
    usableArea: number;
  };

  @Expose()
  links: [];

  @Expose()
  address: {
    apartmentNumber: string;
    streetAdress: string;
    zipCode: string;
    city: string;
  };

  @Expose()
  matrikkel: [
    {
      knr: number;
      gnr: number;
      bnr: number;
      fnr: number;
      snr: number;
      ownPart: string;
    },
  ];

  @Expose()
  textFields: { [key: string]: string };

  @Expose()
  soldDate: Date;

  @Expose()
  commissionAcceptedDate: Date;

  @Expose()
  takeOverDate: Date;

  @Expose()
  contractMeetingDate: Date;

  @Expose()
  finnCode: string;

  @Expose()
  finnPublishDate: Date;

  @Expose()
  finnExpireDate: Date;

  @Expose()
  leasingContractDate: string;

  @Expose()
  valuationTax: {
    primaryValue: number;
    primaryYear: number;
    secondaryValue: number;
    secondaryYear: number;
    comment: string;
    propertyTaxAmount: number;
    propertyTaxYear: number;
    valuationDate: Date;
    valuationType: string;
  };

  @Expose()
  municipality: string;

  @Expose()
  municipalityId: string;

  @Expose()
  projectId: string;

  @Expose()
  projectRelation: number;

  @Expose()
  publicApartmentNumber: string;

  @Expose()
  customerPortal: false;

  @Expose()
  estateTypeId: string;

  @Expose()
  estateType: string;

  @Expose()
  longitude: number;

  @Expose()
  latitude: number;

  @Expose()
  takeoverComment: string;

  @Expose()
  appraiserContactId: string;

  @Expose()
  areaId: string;

  @Expose()
  completionCertificateDate: string;

  @Expose()
  facilities: number[];

  @Expose()
  landOwnerEstateDocumentDate: string;

  @Expose()
  liveAndManagementDuty: boolean;

  @Expose()
  odel: false;

  @Expose()
  ownAssignmentType: string;

  @Expose()
  requiresConcession: false;

  @Expose()
  tag: string;

  @Expose()
  buildings: EstateBuildingNested[];

  @Expose()
  lastDocumentChangeDate: Date;

  @Expose()
  lastImageChangeDate: Date;
}
