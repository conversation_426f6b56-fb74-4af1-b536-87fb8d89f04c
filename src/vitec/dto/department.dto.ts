import { Expose } from 'class-transformer';

export default class DepartmentDto {
  @Expose()
  departmentId: number;

  @Expose()
  name: string;

  @Expose()
  organisationNumber: string;

  @Expose()
  legalName: string;

  @Expose()
  departmentNumber: number;

  @Expose()
  phone: string;

  @Expose()
  fax: string;

  @Expose()
  email: string;

  @Expose()
  streetAddress: string;

  @Expose()
  postalAddress: string;

  @Expose()
  postalCode: string;

  @Expose()
  city: string;

  @Expose()
  isRegion: boolean;

  @Expose()
  subDepartments: unknown[];

  @Expose()
  visitPostalCode: string;

  @Expose()
  visitCity: string;

  @Expose()
  marketName: string;

  @Expose()
  webPublish: boolean;
}
