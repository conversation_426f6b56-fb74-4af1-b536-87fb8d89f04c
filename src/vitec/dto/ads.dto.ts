import { Expose, Type } from 'class-transformer';

export default class AdsDto {
  @Expose()
  id: number;

  @Expose()
  preview: boolean;

  @Expose()
  channel: number;

  @Expose()
  finnAdType: number;

  @Expose()
  adStatus: number;

  @Expose()
  @Type(() => Date)
  publishStart: Date;

  @Expose()
  @Type(() => Date)
  publishEnd: Date;

  @Expose()
  @Type(() => Date)
  lastChanged: Date;
}
