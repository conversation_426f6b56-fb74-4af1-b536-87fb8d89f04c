import { Expose, Type } from 'class-transformer';

export default class EmployeeDto {
  @Expose()
  employeeId: string;

  @Expose()
  title: string;

  @Expose()
  name: string;

  @Expose()
  departmentId: number[];

  @Expose()
  mobilePhone: string;

  @Expose()
  workPhone: string;

  @Expose()
  email: string;

  @Expose()
  employeeActive: boolean;

  @Expose()
  student: boolean;

  @Expose()
  webPublish: boolean;

  @Expose()
  @Type(() => Date)
  imageTimestamp: Date;

  @Expose()
  @Type(() => Date)
  changedDate: Date;

  @Expose()
  @Type(() => Date)
  createdDate: Date;

  @Expose()
  aboutMe: string;
}
