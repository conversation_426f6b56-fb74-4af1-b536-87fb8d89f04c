import { ConsoleLogger, Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';

@Injectable()
export class NordvikboligApiService {
  private readonly logger = new ConsoleLogger(NordvikboligApiService.name);
  private readonly httpClient: AxiosInstance;

  constructor() {
    this.httpClient = axios.create();
  }

  async getByRawGraphql<T>(query: string): Promise<T | null> {
    this.logger.debug(`Sending query: ${query}`);

    try {
      const response = await this.httpClient.get<{ data: T }>(
        `https://api.nordvikbolig.no/graphql?query=${encodeURIComponent(query)}`,
      );
      return response.data?.data;
    } catch (e) {
      this.logger.error(`Error while requesting ${query} : ${e}`);
      return null;
    }
  }
}
