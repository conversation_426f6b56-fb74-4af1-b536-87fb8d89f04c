import { ConsoleLogger, Injectable } from '@nestjs/common';
import axios from 'axios';
import urljoin from 'url-join';
import AppConfigService from '../app-config/app-config.service';

type Message = {
  text: string;
  attachments?: Record<string, unknown>[];
};

const errorColor = '#ff0000';

const getError = (feature: string, error: string): Record<string, unknown>[] => [
  {
    color: errorColor,
    blocks: [
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*${feature}`,
          },
          {
            type: 'mrkdwn',
            text: `*Info:*\n ${error}`,
          },
        ],
      },
    ],
  },
];

@Injectable()
export class SlackService {
  private readonly logger = new ConsoleLogger(SlackService.name);

  constructor(private readonly appConfigService: AppConfigService) {}

  private async slackRequest(webhookUri: string, message: Message): Promise<void> {
    try {
      await axios.post(webhookUri, message);
    } catch (error) {
      console.log(error);
    }
  }

  async sendDocumentError(errorMsg: string): Promise<void> {
    const uri = urljoin(
      this.appConfigService.getSlackApiBaseUrl(),
      this.appConfigService.getSlackDocumentErrorPostfix(),
    );
    await this.slackRequest(uri, {
      text: 'Document archive error',
      attachments: getError('OTP Document Archive', errorMsg),
    });
  }

  async sendSmsDuplicationError(errorMsg: string): Promise<void> {
    const uri = urljoin(
      this.appConfigService.getSlackApiBaseUrl(),
      this.appConfigService.getSlackSmsDuplicationError(),
    );
    await this.slackRequest(uri, {
      text: 'Duplicated SMS were sent out',
      attachments: getError('Duplications', errorMsg),
    });
  }
}
