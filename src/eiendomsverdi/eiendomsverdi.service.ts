import { ConsoleLogger, Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { isEmpty, isNil } from 'ramda';
import xmljs from 'xml-js';
import AppConfigService from '../app-config/app-config.service';
import { Estate, EstateType, LandIdentificationMatrix, UserPreference } from '../pg/estate/estate.model';
import { User } from '../pg/user/user.model';

type ParsedXmlTag = { elements?: ParsedXmlTag[]; name: string; text?: string };

export enum ServiceName {
  PublicInformationRealtime = 'PublicInformationRealtime',
  Service = 'Service',
}

export type ValuationObject = {
  valuation: number | null;
  valuationIndex: number | null;
};

export type HousingCooperativeShareIdentification = {
  organizationNumber: string | null;
  shareNumber: string | null;
};

@Injectable()
export class EiendomsverdiService {
  private readonly logger = new ConsoleLogger(EiendomsverdiService.name);
  private readonly httpClient: AxiosInstance;
  private retryEVApiSeconds = 1;
  private readonly maximumRetryEVApiSeconds = 1024;

  constructor(private readonly appConfigService: AppConfigService) {
    this.httpClient = axios.create();
  }

  async getEstate(
    user: User,
    addressId: string,
    estateId: string,
    organizationNumber: string,
    shareNumber: string,
  ): Promise<Partial<Estate>> {
    // query fields should be in the exact order:
    //  1) addressID
    //  2) estateID
    const query = [`<addressID>${addressId}</addressID>`, `<estateID>${estateId}</estateID>`].join('');
    if (organizationNumber && shareNumber) {
      // query fields should be in the exact order:
      //  1) organizationNumber
      //  2) shareNumber
      const queryShared = [
        `<organizationNumber>${organizationNumber}</organizationNumber>`,
        `<shareNumber>${shareNumber}</shareNumber>`,
      ].join('');
      const result = await this.request(ServiceName.Service, 'GetHousingCooperativeShareInformation', queryShared);
      return result
        ? EiendomsverdiService.toEstate(user, estateId, addressId, organizationNumber, shareNumber, result)
        : null;
    } else {
      const result = await this.request(ServiceName.Service, 'GetExtendedEstateInfo', query);
      return result
        ? EiendomsverdiService.toEstate(user, estateId, addressId, organizationNumber, shareNumber, result)
        : null;
    }
  }
  async getEstateEvaluation(
    baseLandIdentificationMatrix: LandIdentificationMatrix,
    housingCooperativeShareIdentification: HousingCooperativeShareIdentification,
  ): Promise<ValuationObject | null> {
    if (isEmpty(baseLandIdentificationMatrix) || isNil(baseLandIdentificationMatrix)) {
      return null;
    }
    if (housingCooperativeShareIdentification.organizationNumber && housingCooperativeShareIdentification.shareNumber) {
      // query fields should be in the exact order:
      //  1) identification
      //   a) BNr
      //   b) FNr
      //   c) GNr
      //   d) KNr
      //   e) SNr
      //  2) housingCooperativeShareIdentification
      //   a) OrganizationNumber
      //   b) ShareNumber
      const dataPayload = [
        `<tem:estateIdentification>`,
        `<ev:Identification>`,
        `<ev:BNr>${baseLandIdentificationMatrix?.bnr}</ev:BNr>`,
        `<ev:FNr>${baseLandIdentificationMatrix?.fnr}</ev:FNr>`,
        `<ev:GNr>${baseLandIdentificationMatrix?.gnr}</ev:GNr>`,
        `<ev:KNr>${baseLandIdentificationMatrix?.knr}</ev:KNr>`,
        `<ev:SNr>${baseLandIdentificationMatrix?.snr}</ev:SNr>`,
        `</ev:Identification>`,
        `</tem:estateIdentification>`,
        `<tem:housingCooperativeShareIdentification>`,
        `<ev:OrganizationNumber>${housingCooperativeShareIdentification.organizationNumber}</ev:OrganizationNumber>`,
        `<ev:ShareNumber>${housingCooperativeShareIdentification.shareNumber}</ev:ShareNumber>`,
        `</tem:housingCooperativeShareIdentification>`,
      ].join('');
      const result = await this.request(ServiceName.Service, 'GetMarketEstimate', dataPayload);
      return result ? EiendomsverdiService.toEvaluation(result) : null;
    }
    // query fields should be in the exact order:
    //  1) BNr
    //  2) FNr
    //  3) GNr
    //  4) KNr
    //  5) SNr
    const requestPayload = [
      `<tem:estateIdentification>`,
      `<ev:Identification>`,
      `<ev:BNr>${baseLandIdentificationMatrix?.bnr}</ev:BNr>`,
      `<ev:FNr>${baseLandIdentificationMatrix?.fnr}</ev:FNr>`,
      `<ev:GNr>${baseLandIdentificationMatrix?.gnr}</ev:GNr>`,
      `<ev:KNr>${baseLandIdentificationMatrix?.knr}</ev:KNr>`,
      `<ev:SNr>${baseLandIdentificationMatrix?.snr}</ev:SNr>`,
      `</ev:Identification>`,
      `</tem:estateIdentification>`,
    ].join('');
    const result = await this.request(ServiceName.Service, 'GetMarketEstimate', requestPayload);
    return result ? EiendomsverdiService.toEvaluation(result) : null;
  }

  private async waitBeforeCallingApiAgain(): Promise<void> {
    this.logger.log(`Exponential backoff strategy for EV Api, ${this.retryEVApiSeconds}`);
    await new Promise((resolve) => setTimeout(resolve, this.retryEVApiSeconds * 1000));
    this.retryEVApiSeconds = this.retryEVApiSeconds * 2;
  }

  private static toEvaluation(getEstatesResult: ParsedXmlTag): ValuationObject | null {
    const marketEstimateTag = getEstatesResult.elements?.find(EiendomsverdiService.findTagByName('b:MarketEstimate'));
    const estimate = EiendomsverdiService.getFirstText(
      marketEstimateTag?.elements?.find(EiendomsverdiService.findTagByName('b:Estimate')),
    );

    if (estimate && parseInt(estimate) >= 0) {
      return { valuation: parseInt(estimate), valuationIndex: null };
    }

    return null;
  }

  private static toEstate(
    user: User,
    estateId: string,
    addressId: string,
    OrganizationNumber: string | null,
    ShareNumber: string | null,
    getEstatesResult: ParsedXmlTag,
  ): Partial<Estate> {
    const addressTag = getEstatesResult.elements?.find(EiendomsverdiService.findTagByName('b:Address'));
    const propertiesTag = getEstatesResult.elements?.find(EiendomsverdiService.findTagByName('b:Properties'));
    const address = EiendomsverdiService.getFirstText(
      addressTag?.elements?.find(EiendomsverdiService.findTagByName('b:FullAddress')),
    );
    const municipal = EiendomsverdiService.getFirstText(
      addressTag?.elements?.find(EiendomsverdiService.findTagByName('b:Municipality')),
    );
    const estateType = EiendomsverdiService.getFirstText(
      propertiesTag?.elements?.find(EiendomsverdiService.findTagByName('b:EstateType')),
    );
    const numBedrooms = EiendomsverdiService.getFirstText(
      propertiesTag?.elements?.find(EiendomsverdiService.findTagByName('b:NumBedrooms')),
    );
    const livingArea = EiendomsverdiService.getFirstText(
      propertiesTag?.elements?.find(EiendomsverdiService.findTagByName('b:LivingArea')),
    );
    const buildYear = EiendomsverdiService.getFirstText(
      propertiesTag?.elements?.find(EiendomsverdiService.findTagByName('b:BuildYear')),
    );
    const floor = EiendomsverdiService.getFirstText(
      propertiesTag?.elements?.find(EiendomsverdiService.findTagByName('b:Floor')),
    );
    const ownership = EiendomsverdiService.getFirstText(
      propertiesTag?.elements?.find(EiendomsverdiService.findTagByName('b:Ownership')),
    );
    const cadastreTag = getEstatesResult.elements?.find(EiendomsverdiService.findTagByName('b:Identification'));

    const dto = {
      address,
      municipal,
      addressId,
      estateId,
      estateType,
      numBedrooms,
      livingArea,
      buildYear,
      ownership,
      floor,
      location: cadastreTag?.elements?.reduce(
        (acc, tag): { [key: string]: string } => ({
          ...acc,
          [tag.name.slice(2).toLowerCase()]: parseInt(EiendomsverdiService.getFirstText(tag), 10),
        }),
        {},
      ),
      OrganizationNumber,
      ShareNumber,
    };
    return EiendomsverdiService.createEstate(user, dto as any);
  }

  async getOwnedEstatesByNameAndBirthDate(
    name: string,
    birthDate: string,
  ): Promise<{ estateId: string; addressId: string; organizationNumber: string | null; shareNumber: string | null }[]> {
    const query = [`<dateOfBirth>${birthDate.substr(0, 10)}</dateOfBirth>`, `<name>${name}</name>`].join('');
    const result = await this.request(ServiceName.Service, 'GetOwnerEstateInformation', query);
    return EiendomsverdiService.toEstateIds(result?.elements || []);
  }

  private static findTagByName(nameIdentifier: string) {
    return ({ name }: { name: string }) => name === nameIdentifier;
  }

  private static getFirstText(tag?: ParsedXmlTag): string {
    return tag?.elements?.find(Boolean)?.text || '';
  }

  private static createEstate(
    user: User,
    dto: {
      address: string;
      municipal: string;
      addressId: string;
      estateId: string;
      estateType: string;
      numBedrooms: string;
      livingArea: string;
      buildYear: string;
      floor: string;
      ownership: string;
      location: LandIdentificationMatrix;
      OrganizationNumber: string;
      ShareNumber: string;
    },
  ): Partial<Estate> {
    const numberOfBedrooms = parseInt(dto.numBedrooms, 10);
    const livingArea = parseInt(dto.livingArea, 10);
    const buildYear = parseInt(dto.buildYear, 10);
    const floor = parseInt(dto.floor, 10);
    const landIdentificationMatrix = dto.location;
    return {
      connectToBroker: false,
      imageUrl: null,
      interestRate: null,
      loanAmount: null,
      mortgageYears: null,
      originalMortgage: null,
      sellPreference: UserPreference.DONT,
      type: EstateType.OWNED,
      userID: user.id,
      address: dto.address,
      landIdentificationMatrix,
      propertyType: dto.estateType,
      numberOfBedrooms: isNaN(numberOfBedrooms) ? null : numberOfBedrooms,
      livingArea: isNaN(livingArea) ? null : livingArea,
      buildYear: isNaN(buildYear) ? null : buildYear,
      floor: isNaN(floor) ? null : floor,
      ownership: dto.ownership,
      EVEstateID: dto.estateId,
      EVAddressID: dto.addressId,
      OrganizationNumber: dto.OrganizationNumber,
      ShareNumber: dto.ShareNumber,
    };
  }

  private async request(serviceName: ServiceName, action: string, body: string): Promise<ParsedXmlTag | null> {
    const headers = { 'Content-Type': 'application/soap+xml' };
    const data = this.makeXmlRequest(serviceName, action, body);

    try {
      const result = await axios({ url: this.serviceNameToUrl(serviceName), method: 'POST', headers, data });
      const actionResult = EiendomsverdiService.unwrapActionResult(xmljs.xml2js(result.data) as ParsedXmlTag);
      this.retryEVApiSeconds = 1;
      if (!actionResult || !actionResult.elements || !actionResult.elements[0]) {
        this.logger.warn(`Error calling EV API, it responded with unexpected format: ${JSON.stringify(actionResult)}`);
        return null;
      }

      return actionResult;
    } catch (error) {
      const isRateLimitError = error.errno === 'ECONNRESET';
      if (isRateLimitError) {
        await this.waitBeforeCallingApiAgain();
        if (this.retryEVApiSeconds > this.maximumRetryEVApiSeconds) {
          this.logger.error(`Reached maximum retry timeout for EV Api: ${this.retryEVApiSeconds}`);
          this.retryEVApiSeconds = 1;
          return null;
        }
        return await this.request(serviceName, action, body);
      } else {
        this.logger.error(
          `Error calling EV api with serviceName: ${serviceName}, ${action}: ${error.response?.status} ${error.response?.data}`,
        );
        return null;
      }
    }
  }

  private makeXmlRequest(serviceName: ServiceName, action: string, body: string): string {
    return `<?xml version="1.0"?>
    <s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:a="http://www.w3.org/2005/08/addressing" xmlns:u="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:tem="http://tempuri.org/" xmlns:ev="http://schemas.datacontract.org/2004/07/EV.InformationServices.ExternalApi.DTO" xmlns:ev1="http://schemas.datacontract.org/2004/07/EV.InformationServices.ExternalApi.DTO.ObjectWrappers">
      <s:Header>
        <a:Action>http://tempuri.org/I${serviceName}/${action}</a:Action>
        <a:To s:mustUnderstand="1">https://test-api.eiendomsverdi.no/${serviceName}.svc</a:To>
        ${this.makeSecurityHeader()}
      </s:Header>
      <s:Body><${action} xmlns="http://tempuri.org/">${body}</${action}></s:Body>
    </s:Envelope>`;
  }

  private makeSecurityHeader(): string {
    return `<o:Security xmlns:o="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
      <o:UsernameToken>
        <o:Username>${this.appConfigService.getEiendomsverdiUser()}</o:Username>
        <o:Password>${this.appConfigService.getEiendomsverdiPassword()}</o:Password>
      </o:UsernameToken>
    </o:Security>`;
  }

  private static unwrapEnvelopeAndBody = (soapResponse: ParsedXmlTag): ParsedXmlTag => {
    if (!soapResponse.elements) {
      throw new Error('no elements in soapResponse');
    }
    const [envelope] = soapResponse.elements;
    const body = envelope.elements?.find(EiendomsverdiService.findTagByName('s:Body'));

    if (!body || !body.elements) {
      throw new Error('no body or elements in soap response');
    }
    return body.elements[0];
  };

  private static unwrapActionResult = (soapResponse: ParsedXmlTag): ParsedXmlTag | null => {
    const response = EiendomsverdiService.unwrapEnvelopeAndBody(soapResponse);
    if (!response || !response.elements || !response.elements[0]) {
      return null;
    }
    return response.elements[0];
  };

  private serviceNameToUrl(serviceName: ServiceName): string {
    switch (serviceName) {
      case ServiceName.PublicInformationRealtime:
        return this.appConfigService.getEiendomsverdiPublicInformationRealtimeUrl();
      case ServiceName.Service:
        return this.appConfigService.getEiendomsverdiServiceUrl();
    }
  }

  private static toEstateIds(
    getEstatesResult: ParsedXmlTag[],
  ): { estateId: string; addressId: string; organizationNumber: string | null; shareNumber: string | null }[] {
    return (
      getEstatesResult.find(EiendomsverdiService.findTagByName('b:Estates'))?.elements?.map((estateTag) => {
        const addressId = EiendomsverdiService.getFirstText(
          estateTag.elements?.find(EiendomsverdiService.findTagByName('b:EV_AddressID')),
        );
        const estateId = EiendomsverdiService.getFirstText(
          estateTag.elements?.find(EiendomsverdiService.findTagByName('b:EV_EstateID')),
        );
        const shareIdentification = estateTag.elements?.find(
          EiendomsverdiService.findTagByName('b:HousingCooperatativeShareIdentification'),
        );
        const organizationNumber = EiendomsverdiService.getFirstText(
          shareIdentification?.elements?.find(EiendomsverdiService.findTagByName('b:OrganizationNumber')),
        );
        const shareNumber = EiendomsverdiService.getFirstText(
          shareIdentification?.elements?.find(EiendomsverdiService.findTagByName('b:ShareNumber')),
        );
        return {
          addressId: addressId === '0' ? '-2147483648' : addressId,
          estateId,
          organizationNumber: organizationNumber.length > 0 ? organizationNumber : null,
          shareNumber: shareNumber.length > 0 ? shareNumber : null,
        };
      }) || []
    );
  }
}
