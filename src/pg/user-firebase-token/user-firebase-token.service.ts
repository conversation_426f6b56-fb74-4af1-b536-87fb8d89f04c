import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserFirebaseToken } from './user-firebase-token.model';

@Injectable()
export class UserFirebaseTokenService {
  constructor(
    @InjectModel(UserFirebaseToken)
    private UserFirebaseTokenModel: typeof UserFirebaseToken,
  ) {}

  async findAllByUserIds(userIDs: string[]): Promise<UserFirebaseToken[]> {
    return this.UserFirebaseTokenModel.findAll({
      where: {
        userID: userIDs,
      },
    });
  }
}
