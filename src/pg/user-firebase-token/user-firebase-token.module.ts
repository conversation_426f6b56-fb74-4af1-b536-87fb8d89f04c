import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { UserFirebaseToken } from './user-firebase-token.model';
import { UserFirebaseTokenService } from './user-firebase-token.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([UserFirebaseToken])],
  providers: [UserFirebaseTokenService],
  exports: [UserFirebaseTokenService],
})
export default class UserFirebaseTokenModule {}
