import { Column, Model, Table } from 'sequelize-typescript';

@Table({
  modelName: 'PEPForm',
})
export class PEPForm extends Model {
  @Column({ primaryKey: true }) id: string;

  @Column estateVitecId!: string;
  @Column isNotificationSent: boolean | null;

  @Column type: string | null;

  // Sale Information
  @Column saleInfoTransactionReason: string | null;
  @Column saleInfoInMyNameOrProxy: string | null;
  @Column saleInfoUseOrInvestment: string | null;

  // Property Information
  @Column propertyInfoOwnedForTime: string | null;
  @Column propertyInfoIsRenovatedByOwner: string | null;
  @Column propertyInfoRenovator: string | null;
  @Column propertyInfoRenovationFinance: string | null;
  @Column propertyInfoRenovationDocumentation: string | null;

  // Equity Information
  @Column equityInfoPercent: string | null;
  @Column equityInfoSource: string | null;

  // Signing
  @Column idfyDocumentId: string | null;
  @Column signingStarted: Date | null;
  @Column signingFinished: Date | null;
}
