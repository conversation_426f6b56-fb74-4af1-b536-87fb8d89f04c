import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { PEPFormService } from './pep-form.service';
import { PEPForm } from './pep-form.model';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([PEPForm])],
  providers: [PEPFormService],
  exports: [PEPFormService],
})
export default class PEPFormModule {}
