import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { PEPForm } from './pep-form.model';

@Injectable()
export class PEPFormService {
  constructor(@InjectModel(PEPForm) private PEPFormModel: typeof PEPForm) {}

  async getById(estateVitecId: string, type: 'SELLER' | 'BUYER' = 'SELLER'): Promise<PEPForm | null> {
    return this.PEPFormModel.findOne({ where: { estateVitecId, type } });
  }

  async updateIsNotificationSent(
    estateVitecId: string,
    isSent: boolean,
    type: 'SELLER' | 'BUYER' = 'SELLER',
  ): Promise<void> {
    await this.PEPFormModel.update({ isNotificationSent: isSent }, { where: { estateVitecId, type } });
  }

  async findAllUnfinishedBetweenSignStartDates(fromDate: Date, toDate: Date): Promise<PEPForm[]> {
    return this.PEPFormModel.findAll({
      where: {
        signingStarted: { [Op.gte]: fromDate, [Op.lte]: toDate },
        signingFinished: { [Op.is]: null },
      },
    });
  }
}
