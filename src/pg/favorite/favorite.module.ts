import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Favorite } from './favorite';
import { FavoriteService } from './favorite.service';
import PostgresModule from '../pg.module';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([Favorite])],
  providers: [FavoriteService],
  exports: [FavoriteService],
})
export default class FavoritesModule {}
