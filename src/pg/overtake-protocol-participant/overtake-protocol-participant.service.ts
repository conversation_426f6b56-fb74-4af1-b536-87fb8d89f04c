import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { OvertakeProtocolParticipant } from './overtake-protocol-participant.model';

@Injectable()
export class OvertakeProtocolParticipantService {
  constructor(
    @InjectModel(OvertakeProtocolParticipant)
    private OvertakeProtocolParticipantModel: typeof OvertakeProtocolParticipant,
  ) {}

  async findAllForOtp(otpId): Promise<OvertakeProtocolParticipant[] | null> {
    return this.OvertakeProtocolParticipantModel.findAll({
      where: {
        overtakeProtocolId: otpId,
      },
    });
  }
}
