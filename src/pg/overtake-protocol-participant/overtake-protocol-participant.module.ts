import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { OvertakeProtocolParticipant } from './overtake-protocol-participant.model';
import { OvertakeProtocolParticipantService } from './overtake-protocol-participant.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([OvertakeProtocolParticipant])],
  providers: [OvertakeProtocolParticipantService],
  exports: [OvertakeProtocolParticipantService],
})
export default class OvertakeProtocolParticipantModule {}
