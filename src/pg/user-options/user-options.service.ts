import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserOptions } from './user-options.model';

@Injectable()
export class UserOptionService {
  constructor(
    @InjectModel(UserOptions)
    private UserOptionsModel: typeof UserOptions,
  ) {}

  async findAll(): Promise<UserOptions[]> {
    return this.UserOptionsModel.findAll({});
  }

  async findOne(id: string): Promise<UserOptions | null> {
    return this.UserOptionsModel.findOne({
      where: {
        id,
      },
    });
  }
}
