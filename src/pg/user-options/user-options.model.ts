import { ARRAY, J<PERSON>N<PERSON>, STRING } from 'sequelize';
import { Column, Model, Table } from 'sequelize-typescript';

type UserType = {
  seller: boolean;
  buyer: boolean;
};

enum DomesticType {
  FAMILY = 'family',
  SINGLE = 'single',
  COUPLE = 'couple',
  GROUP = 'group',
  NO_ANSWER = 'no-answer',
}

enum MortgageOption {
  YES = 'yes',
  NO = 'no',
  PRE_APPROVED = 'preApproved',
}

enum ContactHoursOption {
  WEEKDAY_WORK_HOURS = 'weekdayWorkHours',
  WEEKDAY_EVENING = 'weekdayEvening',
  WEEKEND_DAYTIME = 'weekendDaytime',
  WEEKEND_EVENINGS = 'weekendEvenings',
}

enum HouseCondition {
  NEW = 'new',
  USED = 'used',
}

enum PropertyType {
  HOUSE = 'house',
  APARTMENT = 'apartment',
  DETACHED_HOUSE = 'detached house',
  TOWNHOUSE = 'townhouse',
  SEMI_DETACHED_HOUSE = 'semi-detached house',
  PLOT = 'plot',
  HOLIDAY_HOME = 'holiday home',
}

@Table({
  modelName: 'UserOptions',
})
export class UserOptions extends Model<UserOptions> {
  @Column userID: string;
  @Column(JSONB) type: UserType;
  @Column livingArea: number;
  @Column(JSONB) area: {
    from: number;
    to: number;
  };
  @Column(JSONB) price: {
    from: number;
    to: number;
  };
  @Column(JSONB) profile: {
    domesticType: DomesticType;
    numberOfPeople: number;
    estateTypePreferences: string[];
    houseCondition: HouseCondition[];
    type: PropertyType;
    rooms: {
      from: number;
      to: number;
    };
  };
  @Column(ARRAY(STRING)) values: string[];
  @Column mortgage: MortgageOption;
  @Column(ARRAY(STRING)) contactPreferences: string[];
  @Column(ARRAY(STRING)) contactHours: ContactHoursOption[];
}
