import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserOptions } from './user-options.model';
import { UserOptionService } from './user-options.service';
import PostgresModule from '../pg.module';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([UserOptions])],
  providers: [UserOptionService],
  exports: [UserOptionService],
})
export default class UserOptionsModule {}
