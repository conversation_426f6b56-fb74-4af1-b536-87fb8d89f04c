import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, QueryTypes } from 'sequelize';
import { SmsAudit, SmsAuditType } from './sms-audit.model';

export type SmsDuplicate = {
  count: string;
  sentDate: Date;
  phoneNumber: string;
  data: string;
};

@Injectable()
export class SmsAuditService {
  constructor(
    @InjectModel(SmsAudit)
    private SmsAuditModel: typeof SmsAudit,
  ) {}

  async findByTypeToPhoneNumber(phoneNumber: string, type: SmsAuditType, estateId: string): Promise<SmsAudit> {
    return this.SmsAuditModel.findOne({
      where: {
        phoneNumber,
        type,
        data: { estateId },
        [Op.or]: [{ response: { [Op.is]: null } }, { response: { response: { [Op.not]: null } } }],
      },
    });
  }

  async findByType(phoneNumber: string, type: SmsAuditType): Promise<SmsAudit | null> {
    return this.SmsAuditModel.findOne({
      where: {
        phoneNumber,
        type,
        [Op.or]: [{ response: { [Op.is]: null } }, { response: { response: { [Op.not]: null } } }],
      },
      limit: 1,
      order: [['createdAt', 'DESC']],
    });
  }

  async create(input: {
    phoneNumber: string;
    text: string;
    type: SmsAuditType;
    variant?: string;
    estateId: string;
    response: Record<string, unknown>;
  }): Promise<SmsAudit> {
    return this.SmsAuditModel.create({
      phoneNumber: input.phoneNumber,
      type: input.type,
      data: { text: input.text, variant: input.variant, estateId: input.estateId },
      response: input.response,
    });
  }

  async findOne(id: string): Promise<SmsAudit | null> {
    return this.SmsAuditModel.findOne({
      where: {
        id,
      },
    });
  }

  async findDuplicatesAtDate(date: Date): Promise<SmsDuplicate[]> {
    return this.SmsAuditModel.sequelize.query(
      `
      SELECT   Count(*) as Count,
         Date_trunc('day',"createdAt") AS sentDate,
         "phoneNumber",
         "data"
          FROM     PUBLIC."SmsAudit"
          WHERE    Date_trunc('day',"createdAt") = Date_trunc('day',timestamp ?)
          GROUP BY "data",
                   "phoneNumber",
                   date_trunc('day',"createdAt")
          HAVING   count(*) > 1
          ORDER BY sentDate DESC
    `,
      {
        replacements: [date.toISOString()],
        type: QueryTypes.SELECT,
      },
    ) as Promise<SmsDuplicate[]>;
  }
}
