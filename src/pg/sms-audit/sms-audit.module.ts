import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { SmsAudit } from './sms-audit.model';
import { SmsAuditService } from './sms-audit.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([SmsAudit])],
  providers: [SmsAuditService],
  exports: [SmsAuditService],
})
export default class SmsAuditModule {}
