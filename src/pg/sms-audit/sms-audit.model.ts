import { Column, DataType, Model, Table } from 'sequelize-typescript';

export enum SmsAuditType {
  SEND_CODE = 'sendCode',
  VERIFY_CODE = 'verifyCode',
  SEND_SMS = 'sendSms',
  AFTER_SIGNING_SMS = 'afterSigningSms',
  AFTER_BIDDING_SMS = 'afterBiddingSms',
  AFTER_LAUNCH_SMS = 'afterLaunchSms',
  AFTER_SALE_SMS = 'afterBidAcceptBuyer',
  OTP_SMS = 'otpSms',
  BROKER_OTP_SMS = 'brokerOtpSms',
  SETTLEMENT_SELLER_SMS = 'settlementSellerSms',
  SETTLEMENT_BUYER_SMS = 'settlementBuyerSms',
  OTP_SIGNING_REMINDER = 'otpSigningReminder',
  SETTLEMENT_SIGNING_REMINDER = 'settlementSigningReminder',
  PEP_BUYER_SMS = 'pepBuyerSms',
  PEP_SELLER_SMS = 'pepSellerSms',
}

export type AcquisitionSmsData = {
  text: string;
  estateId: string;
  variant: string;
};

@Table({
  modelName: 'SmsAudit',
})
export class SmsAudit extends Model {
  @Column phoneNumber: string;
  @Column type: SmsAuditType;
  @Column(DataType.JSONB) data: AcquisitionSmsData;
  @Column(DataType.JSONB) response: Record<string, unknown>;
}
