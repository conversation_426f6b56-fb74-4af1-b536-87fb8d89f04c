import { getModelToken } from '@nestjs/sequelize';
import { Test, TestingModule } from '@nestjs/testing';
import AppConfigService from '../../app-config/app-config.service';
import { mocAppConfigServiceFactory } from '../../mock-classes/mock-app-config-service';
import { LipscoreBrokerRating } from './lipscore-broker-rating.model';
import { LipscoreBrokerRatingService } from './lipscore-broker-rating.service';

describe('LipscoreBrokerRatingService', () => {
  let service: LipscoreBrokerRatingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LipscoreBrokerRatingService,
        {
          provide: AppConfigService,
          useValue: mocAppConfigServiceFactory,
        },
        {
          provide: getModelToken(LipscoreBrokerRating),
          useValue: {
            findOne: jest.fn().mockResolvedValue(null),
            create: jest.fn((input) => input),
          },
        },
      ],
    }).compile();

    service = module.get<LipscoreBrokerRatingService>(LipscoreBrokerRatingService);
    jest.clearAllMocks();
  });

  it('should remove HTML space entity in review texts', async () => {
    service.getBrokerReviewsRecursive = jest.fn().mockResolvedValue([
      {
        id: 12345,
        lang: '',
        rating: 5,
        text: 'test&nbsp;text',
        created_at: '',
        user: {
          short_name: '',
        },
        attributes: [
          {
            caption: '',
            value: '',
          },
          {
            caption: '',
            value: '',
          },
        ],
      },
    ]);
    service.getBrokerRatingsRecursive = jest.fn().mockResolvedValue([]);

    const result = await service.saveBrokerStatisticsToDB({
      lipscoreId: 'test-id',
    } as unknown as Pick<LipscoreBrokerRating, 'averageRating' | 'lipscoreId' | 'ratingCount' | 'reviewCount' | 'vitecBrokerEmployeeId'>);

    expect(result.reviews[0].text).toBe('test text');
  });
});
