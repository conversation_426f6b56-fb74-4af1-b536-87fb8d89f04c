import { Controller, Post } from '@nestjs/common';
import Protected from '../../authorization/protected.decorator';
import { LipscoreBrokerRatingService } from './lipscore-broker-rating.service';

@Controller('lipscore-broker-rating')
export class LipscoreBrokerRatingController {
  constructor(private readonly lipscoreBrokerRatingService: LipscoreBrokerRatingService) {}

  @Post('/trigger/sync')
  @Protected()
  async triggerLipscoreBrokerRatingSync() {
    return this.lipscoreBrokerRatingService.trigger();
  }
}
