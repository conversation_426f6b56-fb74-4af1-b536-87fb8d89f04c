import { Column, DataType, Model, Table } from 'sequelize-typescript';

type LipscoreBrokerRatingReview = {
  id: number;
  createdAt: Date;
  lang: string;
  rating: number;
  reviewerShortName: string;
  text: string;
  recommendation: number;
  agentSatisfaction: number;
};

type LipscoreBrokerRatingType = {
  id: number;
  createdAt: Date;
  lang: string;
  rating: number;
  reviewerShortName: string;
};

@Table({ modelName: 'LipscoreBrokerRating' })
export class LipscoreBrokerRating extends Model {
  @Column averageRating: number;
  @Column lipscoreId: number;
  @Column ratingCount: number;
  @Column reviewCount: number;
  @Column(DataType.JSONB) reviews: LipscoreBrokerRatingReview[];
  @Column vitecBrokerEmployeeId: string;
  @Column(DataType.JSONB) ratings: LipscoreBrokerRatingType[];
}
