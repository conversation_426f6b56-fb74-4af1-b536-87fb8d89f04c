import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import AppConfigModule from '../../app-config/app-config.module';
import PostgresModule from '../pg.module';
import { LipscoreBrokerRatingController } from './lipscore-broker-rating.controller';
import { LipscoreBrokerRating } from './lipscore-broker-rating.model';
import { LipscoreBrokerRatingService } from './lipscore-broker-rating.service';

@Module({
  imports: [PostgresModule, AppConfigModule, SequelizeModule.forFeature([LipscoreBrokerRating])],
  providers: [LipscoreBrokerRatingService],
  exports: [LipscoreBrokerRatingService],
  controllers: [LipscoreBrokerRatingController],
})
export default class LipscoreBrokerRatingModule {}
