import { ConsoleLogger, Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/sequelize';
import axios, { AxiosResponse } from 'axios';
import { isEmpty, pick } from 'lodash';
import AppConfigService from '../../app-config/app-config.service';
import { retryFunction } from '../../utils/retry.utils';
import { LipscoreBrokerRating } from './lipscore-broker-rating.model';

type GetLipscoreBrokerRatingsApiResponse = {
  id: number;
  internal_id: string;
  rating: string;
  review_count: number;
  votes: number;
  reviews: {
    id: number;
    created_at: string;
    text: string;
    rating: number;
    lang: string;
    user: {
      short_name: string;
    };
  }[];
}[];

type LipscoreBrokerReview = {
  id: number;
  created_at: string;
  votes_up: number;
  votes_down: number;
  video: string;
  purchase_date: string;
  imported_at: string | null;
  text: string;
  translated_text: string | null;
  rating: number;
  testimonial: boolean;
  lang: string;
  user: {
    id: number;
    name: string;
    avatar_thumb_url: string | null;
    short_name: string;
  };
  images: {
    id: number;
    thumb_url: string;
    image_url: string;
  }[];
  review_reply: {
    text: string;
    created_at: string;
    member_site: string;
  } | null;
  product: {
    name: string;
  };
  attributes: {
    id: number;
    name: string;
    caption: string;
    labels: string[];
    options_count: number;
    value: number;
  }[];
};

type LipscoreBrokerRatingType = {
  id: number;
  created_at: string;
  updated_at: string;
  status: string;
  lang: string;
  origin: string;
  support_case_created_at: string;
  rating: number;
  user: {
    id: number;
    name: string;
    avatar_thumb_url: string | null;
    short_name: string;
  };
};

@Injectable()
export class LipscoreBrokerRatingService {
  private readonly logger = new ConsoleLogger(LipscoreBrokerRatingService.name);

  constructor(
    private readonly appConfigService: AppConfigService,
    @InjectModel(LipscoreBrokerRating) private LipscoreBrokerRatingModel: typeof LipscoreBrokerRating,
  ) {}

  getBrokerReviewsRecursive = async ({
    lipscoreBrokerId,
    page,
  }: {
    lipscoreBrokerId: number;
    page: number;
  }): Promise<LipscoreBrokerReview[]> => {
    const axiosReviewsApiResult: { response: AxiosResponse<LipscoreBrokerReview[]>; error: any | null } =
      await retryFunction(() =>
        axios.get<LipscoreBrokerReview[]>(
          `${this.appConfigService.getLipscoreApiUrl()}/products/${lipscoreBrokerId}/reviews?api_key=${this.appConfigService.getLipscoreApiKey()}&per_page=100&page=${page}`,
          {
            headers: { 'X-Authorization': this.appConfigService.getLipscoreSecretApiKey() },
          },
        ),
      );
    if (axiosReviewsApiResult.response?.data.length) {
      const nextPageResults = await this.getBrokerReviewsRecursive({ lipscoreBrokerId, page: page + 1 });
      return [...axiosReviewsApiResult.response.data, ...nextPageResults];
    } else {
      return axiosReviewsApiResult.response?.data || [];
    }
  };

  getBrokerRatingsRecursive = async ({
    lipscoreBrokerId,
    page,
  }: {
    lipscoreBrokerId: number;
    page: number;
  }): Promise<LipscoreBrokerRatingType[]> => {
    const axiosRatingsApiResult: { response: AxiosResponse<LipscoreBrokerRatingType[]>; error: any | null } =
      await retryFunction(() =>
        axios.get<LipscoreBrokerRatingType[]>(
          `${this.appConfigService.getLipscoreApiUrl()}/products/${lipscoreBrokerId}/ratings?api_key=${this.appConfigService.getLipscoreApiKey()}&per_page=100&page=${page}`,
          {
            headers: { 'X-Authorization': this.appConfigService.getLipscoreSecretApiKey() },
          },
        ),
      );
    if (axiosRatingsApiResult.response?.data.length) {
      const nextPageResults = await this.getBrokerRatingsRecursive({ lipscoreBrokerId, page: page + 1 });
      return [...axiosRatingsApiResult.response.data, ...nextPageResults];
    } else {
      return axiosRatingsApiResult.response?.data || [];
    }
  };

  saveBrokerStatisticsToDB = async (
    input: Pick<
      LipscoreBrokerRating,
      'averageRating' | 'lipscoreId' | 'ratingCount' | 'reviewCount' | 'vitecBrokerEmployeeId'
    >,
  ): Promise<LipscoreBrokerRating> => {
    const propsToPickForUpdate = [
      'averageRating',
      'lipscoreId',
      'reviewCount',
      'ratingCount',
      'vitecBrokerEmployeeId',
      'reviews',
      'ratings',
    ];
    const reviewsApiResult = await this.getBrokerReviewsRecursive({ lipscoreBrokerId: input.lipscoreId, page: 1 });
    const reviews =
      reviewsApiResult.map((elem) => ({
        ...pick<LipscoreBrokerReview, keyof LipscoreBrokerReview>(elem, ['id', 'lang', 'rating']),
        text: elem.text.replace(/&nbsp;/g, ' '),
        createdAt: elem.created_at,
        reviewerShortName: elem.user.short_name,
        recommendation: elem.attributes.find((attr) => attr.caption === 'Anbefaling')?.value,
        agentSatisfaction: elem.attributes.find((attr) => attr.caption === 'Gjenbruk')?.value,
      })) || [];
    const ratingsApiResult = await this.getBrokerRatingsRecursive({ lipscoreBrokerId: input.lipscoreId, page: 1 });
    const ratings =
      ratingsApiResult.map((elem) => ({
        ...pick<LipscoreBrokerRatingType, keyof LipscoreBrokerRatingType>(elem, ['id', 'lang', 'rating']),
        createdAt: elem.created_at,
        reviewerShortName: elem.user.short_name,
      })) || [];
    const lbrToCreate = pick({ ...input, reviews, ratings }, propsToPickForUpdate);
    const rating = await this.LipscoreBrokerRatingModel.findOne({ where: { lipscoreId: input.lipscoreId } });
    const res = rating ? await rating.update(lbrToCreate) : await this.LipscoreBrokerRatingModel.create(lbrToCreate);
    return res || null;
  };

  syncAllBrokerStatistics = async (page = 1) => {
    this.logger.log(`Lipscore ratings sync started on page: ${page}`);
    const apiResult: { response: AxiosResponse<GetLipscoreBrokerRatingsApiResponse>; error: any | null } =
      await retryFunction(() =>
        axios.get<GetLipscoreBrokerRatingsApiResponse>(
          `${this.appConfigService.getLipscoreApiUrl()}/products?api_key=${this.appConfigService.getLipscoreApiKey()}&fields=rating,votes,review_count&page=${page}}`, // adding reviews field param makes it 10x slower
          {
            headers: { 'X-Authorization': this.appConfigService.getLipscoreSecretApiKey() },
          },
        ),
      );
    if (apiResult.error) {
      this.logger.error(`Lipscore API call error: ${apiResult.error.toString()}`);
      throw Error();
    }

    if (isEmpty(apiResult.response.data)) {
      return;
    }
    this.logger.log(`Lipscore ratings number to save: ${apiResult.response.data.length}, on page: ${page}`);

    await Promise.all(
      apiResult.response.data.map(async (elem) => {
        await this.saveBrokerStatisticsToDB({
          lipscoreId: elem.id,
          averageRating: Number(elem.rating),
          ratingCount: elem.votes,
          reviewCount: elem.review_count,
          vitecBrokerEmployeeId: elem.internal_id,
        });
      }),
    );

    await this.syncAllBrokerStatistics(page + 1);
  };

  @Cron(CronExpression.EVERY_DAY_AT_8AM, {
    name: LipscoreBrokerRatingService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    await this.trigger();
  }

  async trigger(): Promise<void> {
    this.logger.log('Lipscore Broker Rating syncing started');
    await this.syncAllBrokerStatistics();
    this.logger.log('Lipscore Broker Rating syncing finished');
  }
}
