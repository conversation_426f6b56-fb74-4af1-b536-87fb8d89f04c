import { Body, Controller, Post } from '@nestjs/common';
import Protected from '../../authorization/protected.decorator';
import { LipscoreAuditService } from './lipscore-audit.service';

@Controller('lipscore-notification')
export class LipscoreNotificationController {
  constructor(private readonly lipscoreAuditService: LipscoreAuditService) {}

  @Post('/trigger/lipscore-audit')
  @Protected()
  async triggerLipscoreAuditNotification() {
    return this.lipscoreAuditService.trigger();
  }

  @Post('/trigger/lipscore-audit/days-ago')
  @Protected()
  async triggerLipscoreAuditNotificationOld(@Body() body: { daysAgo: number }) {
    return this.lipscoreAuditService.trigger(body.daysAgo);
  }
}
