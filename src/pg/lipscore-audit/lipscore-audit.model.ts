import { Column, DataType, Model, Table } from 'sequelize-typescript';
//naming due to conforming to API naming
export type InvitationData = {
  buyer_email: string;
  buyer_name: string;
  lang: string;
  internal_order_id: string;
  internal_customer_id: string;
};
export type ProductData = {
  internal_id: string;
  url: string;
  name: string;
  brand: string;
  image_url: string;
};
export type LipscoreAuditData = {
  invitation: InvitationData;
  product: ProductData;
};
@Table({
  modelName: 'LipscoreAudit',
})
export class LipscoreAudit extends Model {
  @Column targetEmail: string;
  @Column brokerId: string;
  @Column estateVitecId: string;
  @Column(DataType.JSONB) data: LipscoreAuditData;
  @Column(DataType.JSONB) response: Record<string, unknown>;
  @Column isSuccessful: boolean;
}
