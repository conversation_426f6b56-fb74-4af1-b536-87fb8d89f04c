import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel as InjectModelSequelize } from '@nestjs/sequelize';
import axios, { AxiosResponse } from 'axios';
import { startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { NordvikboligApiService } from '../../norvikbolig-api/nordvikbolig-api.service';
import { Contact, ContactDocument } from '../../sync/schema/contact.schema';
import { Department, DepartmentDocument } from '../../sync/schema/department.schema';
import { Employee, EmployeeDocument } from '../../sync/schema/employee.schema';
import { BrokerRole, Estate, EstateBaseType, EstateDocument, EstateSeller } from '../../sync/schema/estate.schema';
import { ProxyDocument } from '../../sync/schema/proxy.schema';
import { Seller, SellerDocument } from '../../sync/schema/seller.schema';
import { retryFunction } from '../../utils/retry.utils';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { VitecService } from '../../vitec/vitec.service';
import { LipscoreAudit, LipscoreAuditData } from './lipscore-audit.model';

@Injectable()
export class LipscoreAuditService {
  private readonly logger = new ConsoleLogger(LipscoreAuditService.name);

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly vitecService: VitecService,
    private readonly nordvikApiService: NordvikboligApiService,

    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Department.name) private departmentModel: Model<DepartmentDocument>,
    @InjectModel(Employee.name) private employeeModel: Model<EmployeeDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
    @InjectModel(Contact.name) private contactModel: Model<ContactDocument>,
    @InjectModel(Proxy.name) private proxyModel: Model<ProxyDocument>,
    @InjectModelSequelize(LipscoreAudit) private LipscoreAuditModel: typeof LipscoreAudit,
  ) {}

  async create(input: {
    targetEmail: string;
    brokerId: string;
    estateVitecId: string;
    data: LipscoreAuditData;
    response: Record<string, unknown>;
    isSuccessful: boolean;
  }): Promise<LipscoreAudit> {
    return this.LipscoreAuditModel.create({
      targetEmail: input.targetEmail,
      brokerId: input.brokerId,
      estateVitecId: input.estateVitecId,
      data: input.data,
      response: input.response,
      isSuccessful: input.isSuccessful,
    });
  }

  async findOne(id: string): Promise<LipscoreAudit | null> {
    return this.LipscoreAuditModel.findOne({
      where: {
        id,
      },
    });
  }
  async getLipscoreAudit(targetEmail: string, brokerId: string, estateVitecId: string): Promise<LipscoreAudit[]> {
    return this.LipscoreAuditModel.findAll({
      where: {
        targetEmail,
        brokerId,
        estateVitecId,
      },
    });
  }
  async sendLipscoreRatingRequestToSeller(estate: Estate, seller: EstateSeller & { email?: string }): Promise<void> {
    const broker = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0];
    this.logger.debug(`Broker found with employeeId: ${broker.employeeId}`);

    const brokerEmployee = await this.employeeModel.findOne({
      employeeId: broker.employeeId,
    });

    const department = await this.departmentModel.findOne({ departmentId: brokerEmployee.departmentId[0] });

    let sellerDetails = seller;

    // We should have an email address for the seller, but if not, we try to find it in other collections
    if (!sellerDetails.email) {
      // Most likely the email it's a proxy when the email is not present
      sellerDetails = await this.proxyModel.findOne({ contactId: seller.contactId });
      if (!sellerDetails?.email) {
        sellerDetails = await this.contactModel.findOne({ contactId: seller.contactId });
      }
      if (!sellerDetails?.email) {
        sellerDetails = await this.sellerModel.findOne({ contactId: seller.contactId });
      }
      // last resort, get email from Vitec
      if (!sellerDetails?.email) {
        const response = await this.vitecService.getContact(seller.contactId);
        sellerDetails = {
          ...seller,
          email: response.email,
        };
      }
    }

    if (!sellerDetails?.email) {
      this.logger.error(`Contact ${sellerDetails.contactId} has no email address`);
      return;
    }
    const invitation = {
      buyer_email: sellerDetails.email,
      buyer_name:
        `${seller.firstName || ''} ${seller.lastName || ''}`.trim() ||
        seller.companyName ||
        'No name provided in Vitec',
      lang: 'no',
      internal_order_id: estate.estateId,
      internal_customer_id: seller.contactId,
    };
    const product = {
      internal_id: broker.employeeId,
      url: 'https://nordvikbolig.no',
      name: broker.employee.name,
      brand: department.marketName ?? department.legalName,
      image_url: broker.employee.image.large,
    };

    const data = {
      invitation,
      product,
    };
    this.logger.debug(`Data to be sent to API: ${JSON.stringify(data)}`);
    let isResponseSuccessful = false;
    // TODO set fewer days in Lipscore dashboard, currently 14 days must elapse between reviews to same broker from same email
    const alreadySentEmailAudits = await this.getLipscoreAudit(sellerDetails.email, broker.employeeId, estate.estateId);
    const isAuditToBeSent =
      alreadySentEmailAudits.length === 0 || alreadySentEmailAudits.every((audit) => !audit.isSuccessful);

    if (!isAuditToBeSent) {
      this.logger.error(`Lipscore audit already exists for ${sellerDetails.email} ${broker.employeeId}`);
      return;
    }
    const apiResult: { response: AxiosResponse<any>; error: any | null } = await retryFunction<AxiosResponse<any>>(() =>
      axios({
        url: `https://api.lipscore.com/invitations?api_key=${this.appConfigService.getLipscoreApiKey()}`,
        method: 'POST',
        headers: { 'X-Authorization': this.appConfigService.getLipscoreSecretApiKey() },
        data: { invitation: invitation, products: [product] },
      }),
    );
    if (apiResult.error === null) {
      this.logger.debug(
        `Lipscore API call response: ${JSON.stringify({
          status: apiResult.response.status,
          data: apiResult.response.data,
        })}`,
      );
      isResponseSuccessful = true;
      try {
        await this.vitecService.postActivity({ employeeId: broker.employeeId, estateId: estate.estateId });
      } catch (e) {
        this.logger.error(
          'Sending Activity to Vitec failed',
          JSON.stringify({
            status: e.response.status,
            data: e.response.data,
          }),
        );
      }
    } else {
      this.logger.error(`Lipscore API call error(10 times retried): ${apiResult.error.toString()}`);
      isResponseSuccessful = false;
    }
    await this.create({
      targetEmail: sellerDetails.email,
      brokerId: broker.employeeId,
      estateVitecId: estate.estateId,
      data,
      response: apiResult.response?.data || apiResult.error,
      isSuccessful: isResponseSuccessful,
    })
      .then((lipscoreAudit) => {
        this.logger.debug(`Lipscore audit created: ${JSON.stringify(lipscoreAudit)}`);
      })
      .catch((error) => {
        this.logger.error(`Lipscore audit creation error: ${error.toString()}`);
      });
  }
  @Cron(CronExpression.EVERY_DAY_AT_8AM, {
    name: LipscoreAuditService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering Lipscore audit notification report');
    await this.trigger();
    this.logger.log(`Finished Lipscore audit notification report`);
  }

  async trigger(daysAgo?: number): Promise<void> {
    const endDate = sub(startOfDay(new Date()), { hours: 2 });
    const startDate = sub(endDate, { days: daysAgo ?? 1 });

    const estatesWithTakeoverdateYesterday = await this.estateModel.find({
      takeOverDate: { $gte: startDate, $lt: endDate },
      status: VitecEstateStatus.OVERSOLD,
      estateBaseType: { $ne: EstateBaseType.PROJECT },
    });

    this.logger.log(
      `Sending Lipscore lead to ${
        estatesWithTakeoverdateYesterday.length
      } estates: ${estatesWithTakeoverdateYesterday.map((estate) => estate.estateId)}`,
    );

    const promises = [];

    for (const estate of estatesWithTakeoverdateYesterday) {
      try {
        if (this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendLipscoreEmail, estate.estateId)) {
          /* When sending information to Lipscore (rating-system), we currently send the name and e-mail of the seller on the estate, 
           meaning that it is the seller that receives an invitation to rate & review the broker. For estates where the seller has a PoA, 
          this is wrong, and we have to ask Lipscore to send the invitation to the PoA instead. */
          const response = await this.nordvikApiService.getByRawGraphql<{
            estate: { sellersWhenSold: EstateSeller[] };
          }>(`{estate(id: "${estate.estateId}", statuses: [-1,0,1,2,3,4]) { sellersWhenSold } }`);

          let receivers = response?.estate?.sellersWhenSold ?? [];

          this.logger.debug(
            `Sellers on estate when sold ${estate.estateId}: ${receivers.map((seller) => seller.contactId)}`,
          );

          if (estate.proxies.length > 0) {
            receivers = receivers.map((seller) => {
              return estate.proxies.find((proxy) => proxy.proxyOf.includes(seller.contactId)) ?? seller;
            });
          }

          for (const seller of receivers) {
            this.logger.debug(`Sending Lipscore lead to seller (or proxy): ${seller.contactId}`);

            promises.push(
              this.sendLipscoreRatingRequestToSeller(estate, seller).catch((error) => {
                this.logger.error(
                  `Error when sending Lipscore audit notification for estate ${estate.estateId}: ${error}`,
                );
              }),
            );
          }
        }
      } catch (error) {
        this.logger.error(`Error when sending Lipscore audit notification for estate ${estate.estateId}: ${error}`);
      }
    }

    try {
      await Promise.all(promises);
    } catch (error) {
      this.logger.error(`Error when sending Lipscore audit notification ${error}`);
    }
  }
}
