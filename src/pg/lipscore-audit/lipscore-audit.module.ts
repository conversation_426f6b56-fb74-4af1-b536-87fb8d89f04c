import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SequelizeModule } from '@nestjs/sequelize';
import AppConfigModule from '../../app-config/app-config.module';
import AuthorizationModule from '../../authorization/authorization.module';
import NordvikboligApiModule from '../../norvikbolig-api/nordvikbolig-api.module';
import { Contact, ContactSchema } from '../../sync/schema/contact.schema';
import { Department, DepartmentSchema } from '../../sync/schema/department.schema';
import { Employee, EmployeeSchema } from '../../sync/schema/employee.schema';
import { Estate, EstateSchema } from '../../sync/schema/estate.schema';
import { ProxySchema } from '../../sync/schema/proxy.schema';
import { Seller, SellerSchema } from '../../sync/schema/seller.schema';
import VitecModule from '../../vitec/vitec.module';
import OvertakeProtocolModule from '../overtake-protocol/overtake-protocol.module';
import PostgresModule from '../pg.module';
import { LipscoreNotificationController } from './lipscore-audit.controller';
import { LipscoreAudit } from './lipscore-audit.model';
import { LipscoreAuditService } from './lipscore-audit.service';

@Module({
  imports: [
    PostgresModule,
    AppConfigModule,
    OvertakeProtocolModule,
    AuthorizationModule,
    SequelizeModule.forFeature([LipscoreAudit]),
    VitecModule,
    NordvikboligApiModule,
    MongooseModule.forFeature([
      { name: Estate.name, schema: EstateSchema },
      { name: Department.name, schema: DepartmentSchema },
      { name: Employee.name, schema: EmployeeSchema },
      { name: Seller.name, schema: SellerSchema },
      { name: Contact.name, schema: ContactSchema },
      { name: Proxy.name, schema: ProxySchema },
    ]),
  ],
  providers: [LipscoreAuditService],
  exports: [LipscoreAuditService],
  controllers: [LipscoreNotificationController],
})
export default class LipscoreAuditModule {}
