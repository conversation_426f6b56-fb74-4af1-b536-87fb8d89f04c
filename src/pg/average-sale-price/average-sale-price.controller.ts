import { Controller, Post } from '@nestjs/common';
import Protected from '../../authorization/protected.decorator';
import { AverageSalePriceService } from './average-sale-price.service';

@Controller('average-sale-price')
export class AverageSalePriceController {
  constructor(private readonly averageSalePriceService: AverageSalePriceService) {}

  @Post('/trigger')
  @Protected()
  async triggerLipscoreBrokerRatingSync() {
    return this.averageSalePriceService.trigger();
  }
}
