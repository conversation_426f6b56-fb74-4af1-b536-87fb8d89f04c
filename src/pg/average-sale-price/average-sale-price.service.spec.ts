import { sub } from 'date-fns';
import { EstateDocument } from '../../sync/schema/estate.schema';
import {
  calculateAveragePriceAbsolute,
  calculateAveragePricePerSquareMeter,
  calculateAverageTimeForPropertySale,
  getAveragePricesByPostalCode,
} from './average-sale-price.service';

describe('calculateAveragePriceAbsolute', () => {
  it('should calculate the soldPrice mean', () => {
    const estates = [
      { estatePrice: { soldPrice: 1000 } },
      { estatePrice: { soldPrice: 2000 } },
      { estatePrice: { soldPrice: 3000 } },
    ] as EstateDocument[];
    const average = calculateAveragePriceAbsolute(estates);
    expect(average).toEqual({ average: 2000, count: 3 });
  });

  it('should skip the NaNs during calculation', () => {
    const estates = [
      { estatePrice: { soldPrice: 1000 } },
      { estatePrice: { soldPrice: 2000 } },
      { estatePrice: { soldPrice: 3000 } },
      { estatePrice: { soldPrice: null } },
      { estatePrice: { soldPrice: undefined } },
      { estatePrice: { soldPrice: 'string' } },
    ] as EstateDocument[];
    const average = calculateAveragePriceAbsolute(estates);
    expect(average).toEqual({ average: 2000, count: 3 });
  });

  it('should return null if input is an empty array', () => {
    const estates = [] as unknown as EstateDocument[];
    const average = calculateAveragePriceAbsolute(estates);
    expect(average).toEqual({ average: null, count: 0 });
  });

  it('should return null if there are no number values', () => {
    const estates = [
      { estatePrice: { soldPrice: null } },
      { estatePrice: { soldPrice: undefined } },
      { estatePrice: { soldPrice: 'string' } },
    ] as unknown as EstateDocument[];
    const average = calculateAveragePriceAbsolute(estates);
    expect(average).toEqual({ average: null, count: 0 });
  });
});

describe('calculateAveragePricePerSquareMeter', () => {
  it('should calculate the pricePerSquareMeter mean', () => {
    const estates = [
      { estatePrice: { soldPrice: 1000 }, estateSize: { primaryRoomArea: 100 } },
      { estatePrice: { soldPrice: 2000 }, estateSize: { primaryRoomArea: 200 } },
      { estatePrice: { soldPrice: 3000 }, estateSize: { primaryRoomArea: 300 } },
    ] as EstateDocument[];
    const average = calculateAveragePricePerSquareMeter(estates);
    expect(average).toEqual({ average: 10, count: 3 });
  });

  it('should skip the NaNs during calculation', () => {
    const estates = [
      { estatePrice: { soldPrice: 1000 }, estateSize: { primaryRoomArea: 100 } },
      { estatePrice: { soldPrice: 2000 }, estateSize: { primaryRoomArea: 200 } },
      { estatePrice: { soldPrice: 3000 }, estateSize: { primaryRoomArea: 300 } },
      { estatePrice: { soldPrice: null }, estateSize: { primaryRoomArea: 100 } },
      { estatePrice: { soldPrice: undefined }, estateSize: { primaryRoomArea: 200 } },
      { estatePrice: { soldPrice: 'string' }, estateSize: { primaryRoomArea: 300 } },
      { estatePrice: { soldPrice: 1000 }, estateSize: { primaryRoomArea: null } },
      { estatePrice: { soldPrice: 2000 }, estateSize: { primaryRoomArea: undefined } },
      { estatePrice: { soldPrice: 3000 }, estateSize: { primaryRoomArea: 'string' } },
    ] as EstateDocument[];
    const average = calculateAveragePricePerSquareMeter(estates);
    expect(average).toEqual({ average: 10, count: 3 });
  });

  it('should return null if the input is an empty array', () => {
    const estates = [] as unknown as EstateDocument[];
    const average = calculateAveragePricePerSquareMeter(estates);
    expect(average).toEqual({ average: null, count: 0 });
  });

  it('should return null if there are no number values', () => {
    const estates = [
      { estatePrice: { soldPrice: null }, estateSize: { primaryRoomArea: null } },
      { estatePrice: { soldPrice: undefined }, estateSize: { primaryRoomArea: undefined } },
      { estatePrice: { soldPrice: 'string' }, estateSize: { primaryRoomArea: 'string' } },
    ] as unknown as EstateDocument[];
    const average = calculateAveragePricePerSquareMeter(estates);
    expect(average).toEqual({ average: null, count: 0 });
  });
});

describe('calculateAverageTimeForPropertySale', () => {
  it('should calculate the correct time mean', () => {
    const estates = [
      {
        statusChanges: [
          { date: sub(new Date(), { days: 1 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: sub(new Date(), { days: 1 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: sub(new Date(), { days: 4 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
    ] as EstateDocument[];
    const average = calculateAverageTimeForPropertySale(estates);
    expect(average).toEqual({ average: 2, count: 3 });
  });

  it('should skip the NaNs during calculation', () => {
    const estates = [
      {
        statusChanges: [
          { date: sub(new Date(), { days: 2 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: null, from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: undefined, from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: 'string', from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
    ] as EstateDocument[];
    const average = calculateAverageTimeForPropertySale(estates);
    expect(average).toEqual({ average: 2, count: 1 });
  });

  it('should return null if input is an empty array', () => {
    const estates = [] as unknown as EstateDocument[];
    const average = calculateAverageTimeForPropertySale(estates);
    expect(average).toEqual({ average: null, count: 0 });
  });

  it('should return null if there are no number values', () => {
    const estates = [
      {
        statusChanges: [
          { date: null, from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: undefined, from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        statusChanges: [
          { date: 'string', from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
    ] as EstateDocument[];
    const average = calculateAverageTimeForPropertySale(estates);
    expect(average).toEqual({ average: null, count: 0 });
  });
});

describe('getAveragePricesByPostalCode', () => {
  it('should return the correct values', () => {
    const estates = [
      {
        address: { zipCode: '01', apartmentNumber: '1' },
        estatePrice: { soldPrice: 10000 },
        estateSize: { primaryRoomArea: 10 },
        statusChanges: [
          { date: new Date(), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        address: { zipCode: '01', apartmentNumber: '2' },
        estatePrice: { soldPrice: 20000 },
        estateSize: { primaryRoomArea: 20 },
        statusChanges: [
          { date: sub(new Date(), { days: 2 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
      {
        address: { zipCode: '02' },
        estatePrice: { soldPrice: 10000 },
        estateSize: { primaryRoomArea: 100 },
        statusChanges: [
          { date: sub(new Date(), { days: 2 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
    ] as EstateDocument[];
    const res = getAveragePricesByPostalCode(estates);
    expect(res).toEqual(
      expect.arrayContaining([
        {
          postalCode: '01',
          averagePriceAbsolute: 15000,
          averagePriceAbsoluteCount: 2,
          averagePricePerSquareMeter: 1000,
          averagePricePerSquareMeterCount: 2,
          averageTimeForPropertySale: 1,
          averageTimeForPropertySaleCount: 2,
        },
        {
          postalCode: '02',
          averagePriceAbsolute: 10000,
          averagePriceAbsoluteCount: 1,
          averagePricePerSquareMeter: 100,
          averagePricePerSquareMeterCount: 1,
          averageTimeForPropertySale: 2,
          averageTimeForPropertySaleCount: 1,
        },
      ]),
    );
  });

  it('should null out where there are no values', () => {
    const estates = [
      {
        address: { zipCode: '01', apartmentNumber: '1' },
      },
    ] as EstateDocument[];
    const res = getAveragePricesByPostalCode(estates);
    expect(res).toEqual(
      expect.arrayContaining([
        {
          postalCode: '01',
          averagePriceAbsolute: null,
          averagePriceAbsoluteCount: 0,
          averagePricePerSquareMeter: null,
          averagePricePerSquareMeterCount: 0,
          averageTimeForPropertySale: null,
          averageTimeForPropertySaleCount: 0,
        },
      ]),
    );
  });

  it('should only null out the missing values', () => {
    const estates = [
      {
        address: { zipCode: '01', apartmentNumber: '2' },
        estatePrice: { soldPrice: 20000 },
        estateSize: { primaryRoomArea: 100 },
      },
      {
        address: { zipCode: '02' },
        estatePrice: { soldPrice: 10000 },
        statusChanges: [
          { date: sub(new Date(), { days: 2 }), from: 1, to: 2 },
          { date: new Date(), from: 2, to: 3 },
        ],
      },
    ] as unknown as EstateDocument[];
    const res = getAveragePricesByPostalCode(estates);
    expect(res).toEqual(
      expect.arrayContaining([
        {
          postalCode: '01',
          averagePriceAbsolute: 20000,
          averagePriceAbsoluteCount: 1,
          averagePricePerSquareMeter: 200,
          averagePricePerSquareMeterCount: 1,
          averageTimeForPropertySale: null,
          averageTimeForPropertySaleCount: 0,
        },
        {
          postalCode: '02',
          averagePriceAbsolute: 10000,
          averagePriceAbsoluteCount: 1,
          averagePricePerSquareMeter: null,
          averagePricePerSquareMeterCount: 0,
          averageTimeForPropertySale: 2,
          averageTimeForPropertySaleCount: 1,
        },
      ]),
    );
  });
});
