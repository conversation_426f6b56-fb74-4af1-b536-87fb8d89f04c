import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SequelizeModule } from '@nestjs/sequelize';
import { Estate, EstateSchema } from '../../sync/schema/estate.schema';
import PostgresModule from '../pg.module';
import { AverageSalePriceController } from './average-sale-price.controller';
import { AverageSalePrice } from './average-sale-price.model';
import { AverageSalePriceService } from './average-sale-price.service';

@Module({
  imports: [
    PostgresModule,
    SequelizeModule.forFeature([AverageSalePrice]),
    MongooseModule.forFeature([{ name: Estate.name, schema: EstateSchema }]),
  ],
  providers: [AverageSalePriceService],
  exports: [AverageSalePriceService],
  controllers: [AverageSalePriceController],
})
export default class AverageSalePriceModule {}
AverageSalePriceModule;
