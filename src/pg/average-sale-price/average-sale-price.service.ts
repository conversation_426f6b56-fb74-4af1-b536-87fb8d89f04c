import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel as InjectModelSequelize } from '@nestjs/sequelize';
import { differenceInDays } from 'date-fns';
import { groupBy, isDate, isEmpty, isNil, map, mean } from 'lodash';
import { Model } from 'mongoose';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { AverageSalePrice } from './average-sale-price.model';

@Injectable()
export class AverageSalePriceService {
  private readonly logger = new ConsoleLogger(AverageSalePriceService.name);

  constructor(
    @InjectModelSequelize(AverageSalePrice) private averageSalePriceModel: typeof AverageSalePrice,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  create = async (estates: EstateDocument[]): Promise<void> => {
    const averagePricesByPostalCode = getAveragePricesByPostalCode(estates);
    await this.averageSalePriceModel.bulkCreate(averagePricesByPostalCode);
  };

  @Cron(CronExpression.EVERY_DAY_AT_4AM, {
    name: AverageSalePriceService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('AverageSalePrice syncing triggered');
    await this.trigger();
    this.logger.log('AverageSalePrice syncing finished');
  }

  async trigger(): Promise<void> {
    const estates = await this.estateModel.find({
      status: {
        $gte: VitecEstateStatus.OVERSOLD,
      },
    });

    await this.create(estates);
  }
}

export const getAveragePricesByPostalCode = (estates: EstateDocument[]) => {
  const estatesByPostalCode = groupBy(estates, (e) => e.address.zipCode);
  return map(estatesByPostalCode, (estates) => {
    const { average: averagePriceAbsolute, count: averagePriceAbsoluteCount } = calculateAveragePriceAbsolute(estates);
    const { average: averagePricePerSquareMeter, count: averagePricePerSquareMeterCount } =
      calculateAveragePricePerSquareMeter(estates);
    const { average: averageTimeForPropertySale, count: averageTimeForPropertySaleCount } =
      calculateAverageTimeForPropertySale(estates);

    return {
      postalCode: estates[0].address.zipCode,
      averagePriceAbsolute,
      averagePriceAbsoluteCount,
      averagePricePerSquareMeter,
      averagePricePerSquareMeterCount,
      averageTimeForPropertySale,
      averageTimeForPropertySaleCount,
    };
  });
};

export const calculateAveragePriceAbsolute = (estates: EstateDocument[]): { average: number | null; count: number } => {
  const soldPrices = estates
    .map((e) => e.estatePrice?.soldPrice)
    .filter((soldPrice) => !isNil(soldPrice) && !isNaN(soldPrice));

  return {
    average: !isEmpty(soldPrices) ? mean(soldPrices) : null,
    count: soldPrices.length,
  };
};

export const calculateAveragePricePerSquareMeter = (
  estates: EstateDocument[],
): { average: number | null; count: number } => {
  const filteredEstates = estates.filter(
    (e) =>
      !isNil(e.estatePrice?.soldPrice) &&
      !isNaN(e.estatePrice?.soldPrice) &&
      !isNil(e.estateSize?.primaryRoomArea) &&
      !isNaN(e.estateSize?.primaryRoomArea) &&
      e.estateSize?.primaryRoomArea > 0,
  );
  const pricesPerSquareMeter = filteredEstates.map((e) => e.estatePrice.soldPrice / e.estateSize.primaryRoomArea);
  return {
    average: !isEmpty(pricesPerSquareMeter) ? mean(pricesPerSquareMeter) : null,
    count: pricesPerSquareMeter.length,
  };
};

export const calculateAverageTimeForPropertySale = (
  estates: EstateDocument[],
): { average: number | null; count: number } => {
  const filteredEstates = estates.filter(
    (e) =>
      isDate(e.statusChanges?.find((s) => s.to === 2)?.date) && isDate(e.statusChanges?.find((s) => s.to === 3)?.date),
  );
  const timeDifferences = filteredEstates.map((e) => {
    const startDate = e.statusChanges.find((s) => s.to === 2).date;
    const endDate = e.statusChanges.find((s) => s.to === 3).date;
    // this rounds down if not a full day
    return differenceInDays(endDate, startDate);
  });

  return {
    average: !isEmpty(timeDifferences) ? mean(timeDifferences) : null,
    count: timeDifferences.length,
  };
};
