import { Column, Model, Table } from 'sequelize-typescript';

@Table({ modelName: 'AverageSalePrice' })
export class AverageSalePrice extends Model {
  @Column averageTimeForPropertySale: number;
  @Column averageTimeForPropertySaleCount: number;
  @Column averagePricePerSquareMeter: number;
  @Column averagePricePerSquareMeterCount: number;
  @Column averagePriceAbsolute: number;
  @Column averagePriceAbsoluteCount: number;
  @Column postalCode: string;
}
