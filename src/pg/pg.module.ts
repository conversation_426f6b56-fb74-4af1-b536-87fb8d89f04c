import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import AppConfigModule from '../app-config/app-config.module';
import AppConfigService from '../app-config/app-config.service';
import { Area } from './area/area.model';
import { AverageSalePrice } from './average-sale-price/average-sale-price.model';
import { EstatePriceHistories } from './estate-price-histories/estate-price-histories.model';
import { Estate } from './estate/estate.model';
import { Favorite } from './favorite/favorite';
import { Feed } from './feed/feed';
import { LipscoreAudit } from './lipscore-audit/lipscore-audit.model';
import { LipscoreBrokerRating } from './lipscore-broker-rating/lipscore-broker-rating.model';
import { MailAudit } from './mail-audit/mail-audit.model';
import { OvertakeProtocolParticipant } from './overtake-protocol-participant/overtake-protocol-participant.model';
import { OvertakeProtocol } from './overtake-protocol/overtake-protocol.model';
import { PEPForm } from './pep-form/pep-form.model';
import { PriceGuessingEstate } from './price-guessing-estates/price-guessing-estates.model';
import { SettlementBuyer, SettlementBuyerParticipant } from './settlement-buyer/settlement-buyer.model';
import { SettlementSeller, SettlementSellerParticipant } from './settlement-seller/settlement-seller.model';
import { SmsAudit } from './sms-audit/sms-audit.model';
import { UserActivity } from './user-activity/user-activity.model';
import { UserFirebaseToken } from './user-firebase-token/user-firebase-token.model';
import { UserOptions } from './user-options/user-options.model';
import { User } from './user/user.model';
import { UserArea } from './userarea/userarea.model';

@Module({
  imports: [
    SequelizeModule.forRootAsync({
      imports: [AppConfigModule],
      useFactory: async (appConfigService: AppConfigService) => ({
        dialect: 'postgres',
        dialectOptions: {
          ssl: {
            rejectUnauthorized: false,
          },
        },
        host: appConfigService.getPgHost(),
        port: appConfigService.getPgPort(),
        username: appConfigService.getPgUsername(),
        password: appConfigService.getPgPassword(),
        database: appConfigService.getPgDatabase(),
        models: [
          UserArea,
          Feed,
          User,
          UserFirebaseToken,
          Favorite,
          UserOptions,
          Estate,
          Area,
          UserActivity,
          SmsAudit,
          OvertakeProtocol,
          MailAudit,
          OvertakeProtocolParticipant,
          SettlementSeller,
          SettlementBuyer,
          SettlementBuyerParticipant,
          SettlementSellerParticipant,
          LipscoreAudit,
          LipscoreBrokerRating,
          AverageSalePrice,
          EstatePriceHistories,
          PEPForm,
          PriceGuessingEstate,
        ],
        define: { freezeTableName: true },
      }),
      inject: [AppConfigService],
    }),
  ],
  providers: [],
  exports: [],
})
export default class PostgresModule {}
