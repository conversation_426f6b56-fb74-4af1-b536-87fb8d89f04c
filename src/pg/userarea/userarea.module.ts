import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { UserArea } from './userarea.model';
import { UserAreaService } from './userarea.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([UserArea])],
  providers: [UserAreaService],
  exports: [UserAreaService],
})
export default class UserAreaModule {}
