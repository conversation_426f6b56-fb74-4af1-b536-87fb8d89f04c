import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserArea } from './userarea.model';

@Injectable()
export class UserAreaService {
  constructor(
    @InjectModel(UserArea)
    private UserAreaModel: typeof UserArea,
  ) {}

  getByUserId = async (userID: string): Promise<string[]> => {
    const areaModels = await this.UserAreaModel.findAll({
      where: {
        userID,
      },
    });

    return areaModels.map((a) => a.areaID);
  };
}
