import { Column, Model, Table } from 'sequelize-typescript';

@Table({
  modelName: 'SettlementSeller',
})
export class SettlementSeller extends Model {
  @Column estateVitecId: string;
  @Column isMortgaged: boolean | null;
  @Column idfyDocumentId: string | null;
  @Column signingStarted: Date | null;
  @Column signingFinished: Date | null;
  @Column fileId: string | null;
  @Column estateAssignmentNumber: string | null;
  @Column estateAddress: string | null;
  @Column isNotificationSent: boolean | null;
  @Column accountsComment: string | null;
}

@Table({
  modelName: 'SettlementSellerParticipant',
})
export class SettlementSellerParticipant extends Model {
  @Column({ primaryKey: true }) id: string;
  @Column settlementSellerId: string;
  @Column name: string;
  @Column email: string | null;
  @Column phoneNumber: string | null;
}
