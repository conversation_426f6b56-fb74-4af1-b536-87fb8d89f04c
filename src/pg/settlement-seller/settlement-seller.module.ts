import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { SettlementSeller, SettlementSellerParticipant } from './settlement-seller.model';
import { SettlementSellerService } from './settlement-seller.service';

@Module({
  imports: [
    PostgresModule,
    SequelizeModule.forFeature([SettlementSeller]),
    SequelizeModule.forFeature([SettlementSellerParticipant]),
  ],
  providers: [SettlementSellerService],
  exports: [SettlementSellerService],
})
export default class SettlementSellerModule {}
