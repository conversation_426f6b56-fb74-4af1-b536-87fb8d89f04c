import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { FindOptions, Op } from 'sequelize';
import { SettlementSeller, SettlementSellerParticipant } from './settlement-seller.model';

@Injectable()
export class SettlementSellerService {
  constructor(
    @InjectModel(SettlementSeller) private SettlementSellerModel: typeof SettlementSeller,
    @InjectModel(SettlementSellerParticipant)
    private SettlementSellerParticipantModel: typeof SettlementSellerParticipant,
  ) {}

  async findAllFinished(): Promise<SettlementSeller[] | null> {
    return this.SettlementSellerModel.findAll({
      where: {
        signingFinished: { [Op.ne]: null },
      },
    });
  }

  async setIsNotificationSent(estateId: string, value: boolean): Promise<void> {
    await this.SettlementSellerModel.update({ isNotificationSent: value }, { where: { estateVitecId: estateId } });
  }

  async getByEstateId(estateId: string): Promise<SettlementSeller> {
    return this.SettlementSellerModel.findOne({ where: { estateVitecId: estateId } });
  }

  async getParticipantsForForm(settlementSellerId: string): Promise<SettlementSellerParticipant[]> {
    return this.SettlementSellerParticipantModel.findAll({ where: { settlementSellerId } });
  }

  async getSettlementSeller(findOptions: FindOptions): Promise<SettlementSeller[]> {
    return this.SettlementSellerModel.findAll(findOptions);
  }

  async findAllUnfinishedBetweenSignStartDates(fromDate: Date, toDate: Date): Promise<SettlementSeller[]> {
    return this.SettlementSellerModel.findAll({
      where: {
        signingStarted: { [Op.gte]: fromDate, [Op.lte]: toDate },
        signingFinished: { [Op.is]: null },
      },
    });
  }
}
