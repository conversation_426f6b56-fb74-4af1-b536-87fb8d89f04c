import { Column, DataType, Model, Table } from 'sequelize-typescript';

export enum MailAuditType {
  SEND_MAIL = 'sendMail',
  SEND_TEMPLATE = 'sendTemplate',
  OTP = 'otp',
  SETTLEMENT = 'settlement',
  PEP = 'pep',
}

export type SendMailData = {
  subject: string;
  html: string;
};

export type SendTemplateData = {
  templateID: string;
  data: Record<string, string>;
};

@Table({
  modelName: 'MailAudit',
})
export class MailAudit extends Model {
  @Column type: MailAuditType;
  @Column from: string;
  @Column to: string;
  @Column(DataType.JSONB) data: SendMailData | SendTemplateData;
}
