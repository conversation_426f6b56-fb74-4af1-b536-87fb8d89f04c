import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { MailAudit } from './mail-audit.model';
import { MailAuditService } from './mail-audit.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([MailAudit])],
  providers: [MailAuditService],
  exports: [MailAuditService],
})
export default class MailAuditModule {}
