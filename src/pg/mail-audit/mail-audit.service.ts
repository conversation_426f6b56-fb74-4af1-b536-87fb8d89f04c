import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { MailAudit } from './mail-audit.model';

@Injectable()
export class MailAuditService {
  constructor(
    @InjectModel(MailAudit)
    private MailAuditModel: typeof MailAudit,
  ) {}

  async create(
    type: MailAudit['type'],
    from: MailAudit['from'],
    to: MailAudit['to'],
    data: MailAudit['data'],
  ): Promise<MailAudit> {
    const audit = await this.MailAuditModel.create({
      type,
      from,
      to,
      data,
    });
    return audit;
  }

  async findOne(id: string): Promise<MailAudit | null> {
    return this.MailAuditModel.findOne({
      where: {
        id,
      },
    });
  }
}
