import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { Estate } from './estate.model';
import { EstateService } from './estate.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([Estate])],
  providers: [EstateService],
  exports: [EstateService],
})
export default class EstatesModule {}
