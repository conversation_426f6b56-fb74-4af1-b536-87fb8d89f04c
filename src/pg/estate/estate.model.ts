import { Column, Model, Table } from 'sequelize-typescript';
import { JSONB } from 'sequelize';

export enum EstateType {
  FOR_SALE = 'forSale',
  OWNED = 'owned',
}

export enum UserPreference {
  NOW = 'now',
  WITHIN_SIX_MONTHS = 'withinSixMonths',
  RIGHT_PRICE = 'rightPrice',
  DONT = 'dont',
}

export type LandIdentificationMatrix = {
  knr: number;
  gnr: number;
  bnr: number;
  fnr: number;
  snr: number;
};

@Table({
  modelName: 'Estates',
})
export class Estate extends Model {
  @Column userID: string;
  @Column type: EstateType;
  @Column address: string;
  @Column propertyType: string;
  @Column numberOfBedrooms: number | null;
  @Column livingArea: number | null;
  @Column buildYear: number | null;
  @Column floor: number | null;
  @Column sellPreference: UserPreference;
  @Column(JSONB) landIdentificationMatrix: LandIdentificationMatrix | null;
  @Column connectToBroker: boolean;
  @Column ownership: string;
  @Column EVEstateID: string;
  @Column EVAddressID: string;
  @Column loanAmount: number | null;
  @Column interestRate: number | null;
  @Column originalMortgage: number | null;
  @Column mortgageYears: number | null;
  @Column imageUrl: string | null;
  @Column ShareNumber: string | null;
  @Column OrganizationNumber: string | null;
  @Column estimationOffset: number | null;
}
