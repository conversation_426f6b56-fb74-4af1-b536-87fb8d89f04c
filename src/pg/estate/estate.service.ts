import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Estate, LandIdentificationMatrix } from './estate.model';
import { User } from '../user/user.model';
import { WhereOptions } from 'sequelize';
import { omit } from 'lodash';

export type EstateProjected = Pick<
  Estate,
  'id' | 'landIdentificationMatrix' | 'estimationOffset' | 'OrganizationNumber' | 'ShareNumber'
>;

@Injectable()
export class EstateService {
  constructor(
    @InjectModel(Estate)
    private EstateModel: typeof Estate,
  ) {}

  async create(user: User, estate: Partial<Estate>): Promise<Estate> {
    return this.EstateModel.create({
      ...estate,
      userID: user.id,
    });
  }

  async getAllNonArchivedProjected(): Promise<EstateProjected[]> {
    const results = await this.EstateModel.findAll({
      attributes: ['id', 'landIdentificationMatrix', 'estimationOffset', 'OrganizationNumber', 'ShareNumber'],
      where: { isArchived: false },
    });
    return results.map((r) => r.toJSON() as EstateProjected);
  }

  async findByIdProjected(id: string): Promise<EstateProjected> {
    const res = await this.EstateModel.findByPk(id, {
      attributes: ['id', 'landIdentificationMatrix', 'estimationOffset', 'OrganizationNumber', 'ShareNumber'],
    });
    return res.toJSON() as EstateProjected;
  }

  async findByUser(user: User): Promise<Estate[]> {
    return this.EstateModel.findAll({
      where: {
        userID: user.id,
      },
    });
  }

  async findByLandIdentificationMatrix(landIdentificationMatrix: LandIdentificationMatrix, partNumber?: number | null) {
    const where: WhereOptions<Estate> = {
      landIdentificationMatrix,
    };

    if (typeof partNumber === 'number') {
      where['ShareNumber'] = partNumber.toString();
    }

    return this.EstateModel.findOne({
      where,
      order: [['createdAt', 'DESC']],
    });
  }

  static getEVIdentificationMatrixFromVitec(matrikkel: LandIdentificationMatrix[]): LandIdentificationMatrix {
    return omit(matrikkel[0], 'ownPart');
  }

  async update(estateId: string, values: Partial<Estate>) {
    return this.EstateModel.update(values, {
      where: {
        id: estateId,
      },
    });
  }

  async delete(where: WhereOptions<Estate>) {
    return this.EstateModel.destroy({
      where,
    });
  }
}
