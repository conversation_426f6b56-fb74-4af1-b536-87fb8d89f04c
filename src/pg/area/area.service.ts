import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Area } from './area.model';
import { flatten } from 'ramda';

@Injectable()
export class AreaService {
  constructor(
    @InjectModel(Area)
    private AreaModel: typeof Area,
  ) {}

  getPostalCodesByIds = async (ids: Area['id'][]): Promise<string[]> => {
    const areaModels = await this.AreaModel.findAll({
      where: {
        id: ids,
      },
    });

    return flatten(areaModels.map((area) => area.postalCodes));
  };
}
