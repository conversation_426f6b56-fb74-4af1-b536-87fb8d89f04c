import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { SettlementBuyer, SettlementBuyerParticipant } from './settlement-buyer.model';
import { SettlementBuyerService } from './settlement-buyer.service';

@Module({
  imports: [
    PostgresModule,
    SequelizeModule.forFeature([SettlementBuyer]),
    SequelizeModule.forFeature([SettlementBuyerParticipant]),
  ],
  providers: [SettlementBuyerService],
  exports: [SettlementBuyerService],
})
export default class SettlementBuyerModule {}
