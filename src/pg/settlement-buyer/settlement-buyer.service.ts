import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { FindOptions, Op } from 'sequelize';
import { EstateBaseType } from '../../sync/schema/estate.schema';
import { SettlementBuyer, SettlementBuyerParticipant } from './settlement-buyer.model';

@Injectable()
export class SettlementBuyerService {
  constructor(
    @InjectModel(SettlementBuyer) private SettlementBuyerModel: typeof SettlementBuyer,
    @InjectModel(SettlementBuyerParticipant) private SettlementBuyerParticipantModel: typeof SettlementBuyerParticipant,
  ) {}

  async findAllFinished(): Promise<SettlementBuyer[] | null> {
    return this.SettlementBuyerModel.findAll({
      where: {
        signingFinished: { [Op.ne]: null },
      },
    });
  }

  async setIsNotificationSent({
    estateId,
    value,
    estateBaseType,
  }: {
    estateId: string;
    value: boolean;
    estateBaseType: EstateBaseType;
  }): Promise<void> {
    await this.SettlementBuyerModel.update(
      { isNotificationSent: value },
      {
        where: {
          estateVitecId: estateId,
          estateBaseType: estateBaseType || null,
        },
      },
    );
  }

  async getByEstateIdAndEstateBaseTypeId(
    estateId: string,
    estateBaseType: EstateBaseType | null,
  ): Promise<SettlementBuyer> {
    return this.SettlementBuyerModel.findOne({
      where: { estateVitecId: estateId, estateBaseType: estateBaseType },
    });
  }

  async getParticipantsForForm(settlementBuyerId: string): Promise<SettlementBuyerParticipant[]> {
    return this.SettlementBuyerParticipantModel.findAll({ where: { settlementBuyerId } });
  }

  async getSettlementBuyer(findOptions: FindOptions): Promise<SettlementBuyer[]> {
    return this.SettlementBuyerModel.findAll(findOptions);
  }

  async findAllUnfinishedBetweenSignStartDates(fromDate: Date, toDate: Date): Promise<SettlementBuyer[]> {
    return this.SettlementBuyerModel.findAll({
      where: {
        signingStarted: { [Op.gte]: fromDate, [Op.lte]: toDate },
        signingFinished: { [Op.is]: null },
      },
    });
  }
}
