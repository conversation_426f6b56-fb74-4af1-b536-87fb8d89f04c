import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { EstateBaseType } from '../../sync/schema/estate.schema';
// import { EstateBaseType } from '../../sync/schema/estate.schema';

@Table({
  modelName: 'SettlementBuyer',
})
export class SettlementBuyer extends Model {
  @Column({ primaryKey: true }) id: string;
  @Column estateVitecId: string;
  @Column hasEquity: boolean | null;

  @Column financeAccountNumber: string | null;
  @Column financeAccountOwnerName: string | null;
  @Column hasFinanceAccountManagers: boolean | null;
  @Column({ type: DataType.JSON }) financeAccountManagers: FinanceAccountManager[] | null;

  @Column equityAccountNumber: string | null;
  @Column equityAccountHolderName: string | null;
  @Column hasEquityAccountManagers: boolean | null;
  @Column({ type: DataType.JSON }) equityAccountManagers: FinanceAccountManager[] | null;

  @Column isFinanceAndEquityAccountsTheSame: boolean | null;
  @Column financedWithLoan: string | null;
  @Column fileId: string | null;
  @Column estateAssignmentNumber: string | null;
  @Column estateAddress: string | null;
  @Column idfyDocumentId: string | null;

  @Column signingStarted: Date | null;
  @Column signingFinished: Date | null;
  @Column isNotificationSent: boolean | null;
  @Column estateBaseType: EstateBaseType;
}

export type FinanceAccountManager = {
  name: string;
  ssn: string;
  address: string;
};

@Table({
  modelName: 'SettlementBuyerParticipant',
})
export class SettlementBuyerParticipant extends Model {
  @Column({ primaryKey: true }) id: string;
  @Column settlementBuyerId: string;
  @Column name: string;
  @Column email: string | null;
  @Column phoneNumber: string | null;
  @Column hasEquity: boolean | null;
}
