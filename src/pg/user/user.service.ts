import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { parsePhoneNumber } from 'libphonenumber-js';
import { Op, Sequelize } from 'sequelize';
import { BuyerDocument } from '../../sync/schema/buyer.schema';
import { ProxyDocument } from '../../sync/schema/proxy.schema';
import { SellerDocument } from '../../sync/schema/seller.schema';
import { User, UserNotificationGroups } from './user.model';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User)
    private UserModel: typeof User,
  ) {}

  async findAll(): Promise<User[]> {
    return this.UserModel.findAll({});
  }

  async findOne(id: string): Promise<User | null> {
    return this.UserModel.findOne({
      where: {
        id,
      },
    });
  }

  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    return this.UserModel.findOne({
      where: {
        [Op.or]: [{ phoneNumber }, { phoneNumber: `+47${phoneNumber}` }],
      },
    });
  }

  async findAllByPhoneNumbers(phoneNumbers: string[]): Promise<User[]> {
    return this.UserModel.findAll({
      where: {
        phoneNumber: phoneNumbers,
      },
    });
  }

  async findAllByContacts(contacts: (BuyerDocument | SellerDocument | ProxyDocument)[]): Promise<User[]> {
    const phoneNumbers = contacts
      .filter((c) => c.mobilePhone)
      .map((c) => {
        const phoneNumber = parsePhoneNumber(c.mobilePhone || '', 'NO');
        return phoneNumber && phoneNumber.isValid() ? phoneNumber.number : null;
      })
      .filter((pn) => pn);

    return this.findAllByPhoneNumbers(phoneNumbers);
  }

  async findAllIdsWithUserSettingEnabled(userSetting: keyof UserNotificationGroups): Promise<Array<Pick<User, 'id'>>> {
    return this.UserModel.findAll({
      where: { [Op.and]: [Sequelize.literal(`"pushNotificationSettings"->>'${userSetting}' = 'true'`)] },
      attributes: ['id'],
    });
  }
}
