import { J<PERSON>N<PERSON> } from 'sequelize';
import { Column, Model, Table } from 'sequelize-typescript';
import { FeatureFlag } from '../../authorization/authorization.service';

export enum RegisteredWith {
  BANKID = 'BANKID',
  VIPPS = 'VIPPS',
  PHONE_NUMBER = 'PHONE_NUMBER',
}

export type UserNotificationGroups = {
  journey: boolean;
  propertyValue: boolean;
  prospect: boolean;
  quiz: boolean;
};

@Table({
  modelName: 'Users',
})
export class User extends Model<User> {
  @Column email!: string;
  @Column name: string;
  @Column phoneNumber: string;
  @Column password: string;
  @Column bankIdVerified: boolean;
  @Column passwordCode!: string;
  @Column verifyPasswordCode: string;
  @Column birthday: string;
  @Column annualIncome: number;
  @Column sumOfOtherLoans: number;
  @Column referralCode!: string;
  @Column visitedChecklist: boolean;
  @Column visitedSalesProcess: boolean;
  @Column registeredWith: RegisteredWith;
  @Column(JSONB) pushNotificationSettings: UserNotificationGroups;
}

export const UserNotificationGroupsMap: Record<keyof UserNotificationGroups, FeatureFlag[]> = {
  journey: [
    FeatureFlag.SendFirstDocumentPush,
    FeatureFlag.SendNordvikBoligStatisticsPush,
    FeatureFlag.SendFinnStatisticsPush,
    FeatureFlag.SendExponova2Push,
    FeatureFlag.SendStorebrandSellerPush,
    FeatureFlag.SendStorebrandBuyerPush,
    FeatureFlag.SendStorebrandAfterBiddingPush,
    FeatureFlag.SendHmhSellerMovingPush,
    FeatureFlag.SendHmhCleaningPush,
    FeatureFlag.SendHmhBuyerMovingPush,
    FeatureFlag.SendHmhBuyerMoving2Push,
    FeatureFlag.SendExponovaPush,
    FeatureFlag.SendEtakstExpiredPush,
    FeatureFlag.SendEkstraPush,
    FeatureFlag.SendCheckoutOffersPush,
    FeatureFlag.SendByggstartPush,
    FeatureFlag.SendOtpPush,
  ],
  propertyValue: [FeatureFlag.SendValueDistributionPush, FeatureFlag.SendValueDevelopmentPush],
  prospect: [],
  quiz: [FeatureFlag.SendPriceGuessingDailyPush],
};

// SendFavoriteEstateListedPush
// SendPremarketEstatePush
// SendVerisureBuyerPush
