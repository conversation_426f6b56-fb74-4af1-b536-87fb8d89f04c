import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { PriceGuessingEstate } from './price-guessing-estates.model';

@Injectable()
export class PriceGuessingService {
  constructor(
    @InjectModel(PriceGuessingEstate)
    private PriceGuessingEstateModel: typeof PriceGuessingEstate,
  ) {}

  async findAll(): Promise<PriceGuessingEstate[]> {
    return this.PriceGuessingEstateModel.findAll({});
  }

  async findForToday(): Promise<PriceGuessingEstate[]> {
    const todaysEstates = await this.PriceGuessingEstateModel.findAll({
      where: {
        dateOfGuessing: {
          [Op.between]: [new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(23, 59, 59, 999))],
        },
      },
    });
    return todaysEstates;
  }

  async createForToday(
    estates: { estateVitecId: string; priceSnapshot: number; dateOfGuessing: Date }[],
  ): Promise<void> {
    await this.PriceGuessingEstateModel.bulkCreate(estates);
  }
}
