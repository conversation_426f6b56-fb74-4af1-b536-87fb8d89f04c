import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { PriceGuessingEstate } from './price-guessing-estates.model';
import { PriceGuessingService } from './price-guessing-estates.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([PriceGuessingEstate])],
  providers: [PriceGuessingService],
  exports: [PriceGuessingService],
})
export default class PriceGuessingModule {}
