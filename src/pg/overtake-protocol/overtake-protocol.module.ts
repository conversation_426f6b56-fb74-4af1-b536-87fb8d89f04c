import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { OvertakeProtocol } from './overtake-protocol.model';
import { OvertakeProtocolService } from './overtake-protocol.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([OvertakeProtocol])],
  providers: [OvertakeProtocolService],
  exports: [OvertakeProtocolService],
})
export default class OvertakeProtocolModule {}
