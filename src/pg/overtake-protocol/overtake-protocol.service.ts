import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { OvertakeProtocol } from './overtake-protocol.model';

@Injectable()
export class OvertakeProtocolService {
  constructor(
    @InjectModel(OvertakeProtocol)
    private OvertakeProtocolModel: typeof OvertakeProtocol,
  ) {}

  async findAllUnfinishedBetweenSignStartDates(fromDate: Date, toDate: Date): Promise<OvertakeProtocol[] | null> {
    return this.OvertakeProtocolModel.findAll({
      where: {
        signingStarted: { [Op.gte]: fromDate, [Op.lte]: toDate },
        signingFinished: { [Op.is]: null },
      },
    });
  }

  async unlockOtp(otpId: string): Promise<void> {
    await this.OvertakeProtocolModel.update({ locked: false }, { where: { id: otpId } });
  }
}
