import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Feed } from './feed';

type FeedInput = {
  text: string;
  iconName: string;
  redirectUrl: string | null;
};
@Injectable()
export class FeedService {
  constructor(
    @InjectModel(Feed)
    private FeedModel: typeof Feed,
  ) {}

  async findAll(): Promise<Feed[]> {
    return this.FeedModel.findAll();
  }

  async delete(userID: string, redirectUrl: string): Promise<void> {
    await this.FeedModel.update({ deletedAt: new Date() }, { where: { userID, redirectUrl } });
  }

  async findOne(id: string): Promise<Feed | null> {
    return this.FeedModel.findOne({
      where: {
        id,
      },
    });
  }

  async insert(userID: string, feed: FeedInput): Promise<Feed> {
    return this.FeedModel.create({ userID, ...feed });
  }
}
