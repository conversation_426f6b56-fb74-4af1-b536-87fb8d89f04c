import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { EstatePriceHistories } from './estate-price-histories.model';
import { EstatePriceHistoriesService } from './estate-price-histories.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([EstatePriceHistories])],
  providers: [EstatePriceHistoriesService],
  exports: [EstatePriceHistoriesService],
})
export default class EstatePriceHistoriesModule {}
