import { Column, Model, Table } from 'sequelize-typescript';
import { JSONB } from 'sequelize';
import { LandIdentificationMatrix } from '../estate/estate.model';

@Table({
  modelName: 'EstatePriceHistories',
})
export class EstatePriceHistories extends Model {
  @Column postgresEstateId: string;
  @Column(JSONB) landIdentificationMatrix: LandIdentificationMatrix;
  @Column evPrice: number;
  @Column actualPriceWithVitecOffset: number;
}
