import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { LandIdentificationMatrix } from '../estate/estate.model';
import { EstatePriceHistories } from './estate-price-histories.model';

@Injectable()
export class EstatePriceHistoriesService {
  constructor(
    @InjectModel(EstatePriceHistories)
    private EstatePriceHistoriesModel: typeof EstatePriceHistories,
  ) {}

  async createOneRecordForOneEstate({
    postgresEstateId,
    landIdentificationMatrix,
    evPrice,
    actualPriceWithVitecOffset,
  }: {
    postgresEstateId: string;
    landIdentificationMatrix: LandIdentificationMatrix;
    evPrice: number;
    actualPriceWithVitecOffset: number;
  }): Promise<EstatePriceHistories> {
    return this.EstatePriceHistoriesModel.create({
      postgresEstateId,
      landIdentificationMatrix,
      evPrice,
      actualPriceWithVitecOffset,
    });
  }

  async getAllEstateHistory(postgresEstateId: string): Promise<EstatePriceHistories[]> {
    return this.EstatePriceHistoriesModel.findAll({
      where: {
        postgresEstateId: postgresEstateId,
      },
    });
  }
}
