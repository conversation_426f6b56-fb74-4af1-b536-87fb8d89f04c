import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserActivity, UserActivityType } from './user-activity.model';

@Injectable()
export class UserActivityService {
  constructor(
    @InjectModel(UserActivity)
    private UserActivityModel: typeof UserActivity,
  ) {}

  async findPrevLead(userIDs: string[], type: UserActivityType): Promise<UserActivity | null> {
    return this.UserActivityModel.findOne({
      where: {
        userID: userIDs,
        type,
      },
    });
  }
}
