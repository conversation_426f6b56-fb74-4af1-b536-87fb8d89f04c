import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import PostgresModule from '../pg.module';
import { UserActivity } from './user-activity.model';
import { UserActivityService } from './user-activity.service';

@Module({
  imports: [PostgresModule, SequelizeModule.forFeature([UserActivity])],
  providers: [UserActivityService],
  exports: [UserActivityService],
})
export default class UserActivityModule {}
