import { Column, Model, Table } from 'sequelize-typescript';
import { DATE, JSONB } from 'sequelize';

export enum UserActivityType {
  SIGNUP = 'signup',
  LOGIN = 'login',
  UPDATE_OPTIONS = 'updateOptions',
  GET_LIST_OF_ALL_ESTATES = 'getListOfAllEstates',
  GET_ESTATE = 'getEstate',
  GET_POPUPS = 'getPopups',
  GET_PREMARKET_ESTATES = 'getPremarketEstates',
  ADD_PREMARKET_ESTATE_TO_FAVORITES = 'addPremarketEstateToFavorites',
  REMOVE_PREMARKET_ESTATE_FROM_FAVORITES = 'removePremarketEstateFromFavorites',
  REQUEST_HMH_SERVICE = 'requestHmhService',
  REQUEST_STOREBRAND_SERVICE = 'requestStorebrandService',
  REQUEST_BROKER_VALUATION = 'requestBrokerValuation',
  GOOGLE_ANALYTICS_EVENT = 'googleAnalyticsEvent',
  REQUEST_EXPONOVA_SERVICE = 'requestExponovaService',
}

@Table({
  modelName: 'UserActivity',
})
export class UserActivity extends Model<UserActivity> {
  @Column userID: string;
  @Column(DATE) createdAt: string;
  @Column type: UserActivityType;
  @Column(JSONB) data: unknown;
}
