import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import AuthenticationService from './authentication.service';
import ApiKeyStrategy from './api-key.strategy';
import AppConfigModule from '../app-config/app-config.module';

@Module({
  imports: [AppConfigModule, PassportModule],
  providers: [AuthenticationService, ApiKeyStrategy],
  exports: [AuthenticationService],
})
export default class AuthenticationModule {}
