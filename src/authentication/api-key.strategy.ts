import { ConsoleLogger, Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { HeaderAPIKeyStrategy } from 'passport-headerapikey';
import AuthenticationService from './authentication.service';

@Injectable()
export default class ApiKeyStrategy extends PassportStrategy(HeaderAPIKeyStrategy, 'api-key') {
  private readonly logger = new ConsoleLogger(ApiKeyStrategy.name);

  constructor(private readonly authenticationService: AuthenticationService) {
    super(
      {
        header: 'X-API-Key',
        prefix: '',
      },
      true,
      async (
        apiKey: string,
        verified: (err: Error | null, user: any | null, info?: unknown) => void,
      ): Promise<void> => {
        const isApiKeyValid = await this.authenticationService.validateApiKey(apiKey);

        if (isApiKeyValid) {
          return verified(null, {});
        }

        return verified(new UnauthorizedException('Header "X-API-Key" is invalid'), null);
      },
    );
  }
}
