import { ConsoleLogger, Injectable } from '@nestjs/common';
import AWS from 'aws-sdk';
import sharp from 'sharp';
import urljoin from 'url-join';
import { v4 } from 'uuid';
import AppConfigService from '../app-config/app-config.service';

type Image = {
  quality: number;
  progressive: boolean;
  sizes: {
    large: number;
    medium: number;
    small: number;
  };
};

const image: Image = {
  quality: 80,
  progressive: true,
  sizes: {
    large: 1920,
    medium: 1080,
    small: 640,
  },
};

type ImageUrls = {
  large: string;
  medium: string;
  small: string;
};

@Injectable()
export class S3Service {
  private readonly logger = new ConsoleLogger(S3Service.name);

  constructor(private readonly appConfigService: AppConfigService) {
    AWS.config.update({
      accessKeyId: this.appConfigService.getS3AccessKeyId(),
      secretAccessKey: this.appConfigService.getS3SecretAccessKey(),
      region: this.appConfigService.getS3Region(),
    });
  }

  async uploadEmployeeImagesToS3(localImagePath: string, slug: string): Promise<ImageUrls> {
    return this.uploadImagesToS3(localImagePath, slug, this.appConfigService.getS3EmployeesDirectory());
  }

  async uploadIdfyDocument(doc: Buffer, estateId: string) {
    const s3 = new AWS.S3({ params: { Bucket: this.appConfigService.getS3Bucket() } });
    await s3
      .upload({
        Key: `documents/${estateId}.pdf`,
        Body: doc,
        ContentType: 'application/pdf',
        ACL: 'public-read',
        Bucket: this.appConfigService.getS3Bucket(),
      })
      .promise();
  }

  async uploadEstateImagesToS3(localImagePath: string, estateId: string, imageId: string): Promise<ImageUrls> {
    return this.uploadImagesToS3(
      localImagePath,
      imageId,
      urljoin(this.appConfigService.getS3EstatesDirectory(), estateId),
    );
  }

  async uploadImagesToS3(imageString: string, filename: string, foldername: string): Promise<ImageUrls> {
    const s3 = new AWS.S3({
      params: { Bucket: this.appConfigService.getS3Bucket() },
    });

    const uploadLarge = this.createAndUpload(imageString, filename, foldername, s3, 'large');
    const uploadMedium = this.createAndUpload(imageString, filename, foldername, s3, 'medium');
    const uploadSmall = this.createAndUpload(imageString, filename, foldername, s3, 'small');

    try {
      await Promise.all([uploadLarge, uploadMedium, uploadSmall]);
      this.logger.debug(`All images uploaded to S3`);

      return {
        large: urljoin(this.appConfigService.getCloudfrontUrl(), foldername, `${filename}-large.jpg`),
        medium: urljoin(this.appConfigService.getCloudfrontUrl(), foldername, `${filename}-medium.jpg`),
        small: urljoin(this.appConfigService.getCloudfrontUrl(), foldername, `${filename}-small.jpg`),
      };
    } catch (error) {
      this.logger.error(`Failed to upload images to S3: ${error}`);
      throw error;
    }
  }

  async createAndUpload(
    imageString: string,
    filename: string,
    foldername: string,
    s3: AWS.S3,
    size: keyof Image['sizes'],
  ) {
    const imageData = await sharp(imageString)
      .jpeg({
        quality: image.quality,
        progressive: image.progressive,
      })
      .resize(image.sizes[size])
      .toBuffer();

    return await s3
      .upload({
        Key: `${foldername}/${filename}-${size}.jpg`,
        Body: imageData,
        ContentType: 'image/jpeg',
        ACL: 'public-read',
        Bucket: this.appConfigService.getS3Bucket(),
      })
      .promise();
  }

  async uploadSingleImage({
    folder,
    imgExt,
    body,
    bucket,
    acl,
  }: {
    folder: string;
    imgExt: string;
    bucket: string;
    body: Buffer;
    acl: string;
  }) {
    const s3 = new AWS.S3();
    const fileName = `${v4()}.${imgExt}`;
    const key = urljoin(folder, fileName);
    const { Location } = await s3
      .upload({
        Key: key,
        Body: body,
        Bucket: bucket,
        ACL: acl,
      })
      .promise();
    return Location;
  }
}
