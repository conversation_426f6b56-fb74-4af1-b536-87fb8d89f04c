import { OvertakeProtocolNotificationService } from '../notification/services/overtake-protocol-notification.service';

export const mockOvertakeProtocolNotificationServiceFactory = (): OvertakeProtocolNotificationService =>
  ({
    sendNotificationsIfFeatureFlagEnabled: jest.fn() as any,
    sendNotification: jest.fn() as any,
    shouldCreateOtpForEstate: jest.fn() as any,
    trigger: jest.fn() as any,
  } as OvertakeProtocolNotificationService);
