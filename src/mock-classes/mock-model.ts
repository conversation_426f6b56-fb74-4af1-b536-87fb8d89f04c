import { Model } from 'mongoose';

export const mockModelFactory = <T>(): Model<T> =>
  ({
    aggregate: jest.fn() as any,
    bulkWrite: jest.fn() as any,
    count: jest.fn() as any,
    countDocuments: jest.fn() as any,
    create: jest.fn() as any,
    createCollection: jest.fn() as any,
    createIndexes: jest.fn() as any,
    deleteMany: jest.fn() as any,
    deleteOne: jest.fn() as any,
    ensureIndexes: jest.fn() as any,
    findById: jest.fn() as any,
    findOne: jest.fn() as any,
    hydrate: jest.fn() as any,
    init: jest.fn() as any,
    insertMany: jest.fn() as any,
    listIndexes: jest.fn() as any,
    populate: jest.fn() as any,
    syncIndexes: jest.fn() as any,
    diffIndexes: jest.fn() as any,
    startSession: jest.fn() as any,
    validate: jest.fn() as any,
    watch: jest.fn() as any,
    $where: jest.fn() as any,
    translateAliases: jest.fn() as any,
    distinct: jest.fn() as any,
    estimatedDocumentCount: jest.fn() as any,
    exists: jest.fn() as any,
    find: jest.fn() as any,
    findByIdAndDelete: jest.fn() as any,
    findByIdAndRemove: jest.fn() as any,
    findByIdAndUpdate: jest.fn() as any,
    findOneAndDelete: jest.fn() as any,
    findOneAndRemove: jest.fn() as any,
    findOneAndReplace: jest.fn() as any,
    findOneAndUpdate: jest.fn() as any,
    geoSearch: jest.fn() as any,
    mapReduce: jest.fn() as any,
    remove: jest.fn() as any,
    replaceOne: jest.fn() as any,
    update: jest.fn() as any,
    updateMany: jest.fn() as any,
    updateOne: jest.fn() as any,
    where: jest.fn() as any,
  } as unknown as Model<T>);
