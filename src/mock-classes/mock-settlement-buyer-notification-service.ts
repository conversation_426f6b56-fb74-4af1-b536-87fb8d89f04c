import { SettlementBuyerNotificationService } from '../notification/services/settlement-buyer-notification.service';

export const mockSettlementBuyerNotificationServiceFactory = (): SettlementBuyerNotificationService =>
  ({
    createFormAndSendNotificationsIfNotSent: jest.fn() as any,
    sendEmailAndSmsNotifications: jest.fn() as any,
    isSettlementBuyerFormCreationEnabled: jest.fn() as any,
    isSettlementBuyerProjectFormCreationEnabled: jest.fn() as any,
  } as SettlementBuyerNotificationService);
