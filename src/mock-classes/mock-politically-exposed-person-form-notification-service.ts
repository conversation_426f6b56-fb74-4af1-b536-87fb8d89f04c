import { PoliticallyExposedPersonFormNotificationService } from '../notification/services/politically-exposed-person-form-notification.service';

export const mockPoliticallyExposedPersonFormNotificationServiceFactory =
  (): PoliticallyExposedPersonFormNotificationService =>
    ({
      createFormAndSendSellerNotificationsIfNotSent: jest.fn() as any,
      isPepSellerFormCreationEnabled: jest.fn() as any,
      triggerSeller: jest.fn() as any,
    } as PoliticallyExposedPersonFormNotificationService);
