import { VitecService } from '../vitec/vitec.service';

export const mockVitecServiceFactory = (): VitecService =>
  ({
    getTips: jest.fn() as any,
    getEstateMetadata: jest.fn() as any,
    getEstate: jest.fn() as any,
    getProject: jest.fn() as any,
    getContact: jest.fn() as any,
    getContactInformation: jest.fn() as any,
    getContactRelations: jest.fn() as any,
    getActivities: jest.fn() as any,
    getCheckList: jest.fn() as any,
    getBids: jest.fn() as any,
    getDocuments: jest.fn() as any,
    getDepartments: jest.fn() as any,
    getBaseCommission: jest.fn() as any,
    getEmployees: jest.fn() as any,
    downloadEmployeeImage: jest.fn() as any,
    downloadEstateImage: jest.fn() as any,
    getEstateAds: jest.fn() as any,
    formatUrl: jest.fn() as any,
  } as VitecService);
