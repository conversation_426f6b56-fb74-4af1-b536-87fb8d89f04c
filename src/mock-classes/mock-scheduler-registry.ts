import { SchedulerRegistry } from '@nestjs/schedule';

export const mockSchedulerRegistryFactory = (): SchedulerRegistry =>
  ({
    doesExists: jest.fn() as any,
    getCronJob: jest.fn() as any,
    getInterval: jest.fn() as any,
    getTimeout: jest.fn() as any,
    addCronJob: jest.fn() as any,
    addInterval: jest.fn() as any,
    addTimeout: jest.fn() as any,
    getCronJobs: jest.fn() as any,
    deleteCronJob: jest.fn() as any,
    getIntervals: jest.fn() as any,
    deleteInterval: jest.fn() as any,
    getTimeouts: jest.fn() as any,
    deleteTimeout: jest.fn() as any,
  } as any as SchedulerRegistry);
