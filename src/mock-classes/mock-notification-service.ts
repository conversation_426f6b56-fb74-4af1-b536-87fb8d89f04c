import { NotificationService } from '../notification/notification.service';

export const mockNotificationServiceFactory = (): NotificationService =>
  ({
    triggerFeedNotification: jest.fn() as any,
    deleteFeedNotifications: jest.fn() as any,
    triggerSmsNotification: jest.fn() as any,
    triggerPushNotification: jest.fn() as any,
    delayByLastSentSms: jest.fn() as any,
  } as NotificationService);
