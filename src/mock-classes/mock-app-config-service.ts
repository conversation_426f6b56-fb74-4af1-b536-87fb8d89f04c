import AppConfigService from '../app-config/app-config.service';

export const mocAppConfigServiceFactory = (): AppConfigService =>
  ({
    getEnvironment: jest.fn() as any,
    isProductionEnvironment: jest.fn() as any,
    isDevelopmentEnvironment: jest.fn() as any,
    isTestEnvironment: jest.fn() as any,
    getPort: jest.fn() as any,
    getApiKey: jest.fn() as any,
    getMongoDbUri: jest.fn() as any,
    getUnleashAppName: jest.fn() as any,
    getUnleashAuthorizationHeader: jest.fn() as any,
    getUnleashUrl: jest.fn() as any,
    getUnleashInstanceId: jest.fn() as any,
    getCloudfrontUrl: jest.fn() as any,
    getVitecBaseUrl: jest.fn() as any,
    getVitecUsername: jest.fn() as any,
    getVitecPassword: jest.fn() as any,
    getVitecInstallationId: jest.fn() as any,
    getVitecRateLimitMax: jest.fn() as any,
    getVitecRateLimitTimeWindow: jest.fn() as any,
    getVitecRequestMaxRetry: jest.fn() as any,
    getVitecRequestRetryAfter: jest.fn() as any,
    getS3AccessKeyId: jest.fn() as any,
    getS3Bucket: jest.fn() as any,
    getS3SecretAccessKey: jest.fn() as any,
    getS3Region: jest.fn() as any,
    getS3EmployeesDirectory: jest.fn() as any,
    getS3EstatesDirectory: jest.fn() as any,
    getTwilioAccountSid: jest.fn() as any,
    getTwilioAuthToken: jest.fn() as any,
    getTwilioSmsPhoneNumber: jest.fn() as any,
    getAppUrl: jest.fn() as any,
    getPgHost: jest.fn() as any,
    getPgUsername: jest.fn() as any,
    getPgPassword: jest.fn() as any,
    getPgDatabase: jest.fn() as any,
    getPgPort: jest.fn() as any,
    getFirebaseProjectId: jest.fn() as any,
    getFirebasePrivateKey: jest.fn() as any,
    getFirebaseClientEmail: jest.fn() as any,
    getFirebaseServiceAccountId: jest.fn() as any,
    getSqsUrl: jest.fn() as any,
    getDisableSqs: jest.fn() as any,
    getEiendomsverdiPublicInformationRealtimeUrl: jest.fn() as any,
    getEiendomsverdiServiceUrl: jest.fn() as any,
    getEiendomsverdiUser: jest.fn() as any,
    getEiendomsverdiPassword: jest.fn() as any,
    getMapboxAccessToken: jest.fn() as any,
    getMapboxBaseUrl: jest.fn() as any,
    getMapboxUsername: jest.fn() as any,
    getMapboxStyle: jest.fn() as any,
    getMapboxZoom: jest.fn() as any,
    getBackendUrl: jest.fn() as any,
    getSendgridApikey: jest.fn() as any,
    getSlackApiBaseUrl: jest.fn() as any,
    getSlackDocumentErrorPostfix: jest.fn() as any,
    getSlackSmsDuplicationError: jest.fn() as any,
    getIdfyId: jest.fn() as any,
    getIdfySecret: jest.fn() as any,
    getBackendApiKey: jest.fn() as any,
    getLipscoreApiKey: jest.fn() as any,
    getLipscoreSecretApiKey: jest.fn() as any,
  } as AppConfigService);
