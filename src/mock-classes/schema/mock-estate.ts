import { Estate, EstateBaseType } from '../../sync/schema/estate.schema';

export const mockEstateFactory = (): Estate =>
  ({
    estateId: '',
    assignmentNum: '',
    assignmentType: 0,
    assignmentTypeGroup: null,
    systemId: '',
    finnCode: '',
    finnPublishDate: undefined,
    finnExpireDate: undefined,
    takeOverDate: undefined,
    contractMeetingDate: undefined,
    changedDate: undefined,
    address: null,
    ownership: 0,
    employeeId: '',
    brokersIdWithRoles: [],
    departmentId: 0,
    status: 0,
    defaultImageId: '',
    defaultImage: null,
    heading: '',
    showings: [],
    showingNote: '',
    noOfRooms: 0,
    noOfBedRooms: 0,
    textFields: null,
    floor: 0,
    facilities: [],
    estatePreferences: [],
    constructionYear: 0,
    energyLetter: 0,
    energyColorCode: 0,
    plot: null,
    partOwnership: null,
    estatePrice: null,
    estateSize: null,
    buildings: [],
    valuationTax: null,
    links: [],
    commissionAcceptedDate: undefined,
    soldDate: undefined,
    estateTypeId: '',
    estateType: '',
    estateBaseType: EstateBaseType.NOT_SET,
    estateTypeExternal: 0,
    createdDate: undefined,
    location: null,
    matrikkel: [],
    municipality: '',
    municipalityId: '',
    takeoverComment: '',
    appraiserContactId: '',
    appraiserContact: null,
    businessManagerContact: null,
    tag: '',
    projectId: '',
    projectRelation: 0,
    projectUnits: [],
    projectTextFields: null,
    projectName: '',
    activities: [],
    checkList: {
      checkListItems: [],
      lastChanged: new Date(),
    },
    statusChanges: [],
    url: '',
    sellers: [],
    employee: null,
    department: null,
    images: [],
    areaId: '',
    bids: [],
    documents: [],
    contacts: [],
    buyers: [],
    proxies: [],
    lastImageChangeDate: undefined,
    expireDate: undefined,
    lastSuccessfulSyncStartDate: undefined,
  } as Estate);
