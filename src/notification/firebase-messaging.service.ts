import { Injectable } from '@nestjs/common';
import * as firebaseAdmin from 'firebase-admin';
import AppConfigService from '../app-config/app-config.service';

@Injectable()
export class FirebaseMessagingService {
  private readonly app: firebaseAdmin.app.App;

  constructor(private readonly appConfigService: AppConfigService) {
    this.app = firebaseAdmin.initializeApp({
      credential: firebaseAdmin.credential.cert({
        projectId: appConfigService.getFirebaseProjectId(),
        privateKey: appConfigService.getFirebasePrivateKey().replace(/\\n/gm, '\n'),
        clientEmail: appConfigService.getFirebaseClientEmail(),
      }),
      serviceAccountId: appConfigService.getFirebaseServiceAccountId(),
      projectId: appConfigService.getFirebaseProjectId(),
    });
  }

  get messaging() {
    if (!this.app) {
      throw new Error('Firebase instance is undefined.');
    }
    return this.app.messaging();
  }

  sendMulticast(message: firebaseAdmin.messaging.MulticastMessage): Promise<firebaseAdmin.messaging.BatchResponse> {
    return this.messaging.sendEachForMulticast(message);
  }
}
