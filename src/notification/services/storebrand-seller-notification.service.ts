import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class StorebrandSellerNotificationService {
  private readonly logger = new ConsoleLogger(StorebrandSellerNotificationService.name);
  private static readonly NOTIFICATION_MESSAGE = 'Gratulerer! Utforsk dine muligheter med Storebrand';

  private static readonly ICON_NAME = 'revenue';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/storebrand';

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('30 19 * * *', {
    name: StorebrandSellerNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled storebrand seller notifications');
    await this.trigger();
    this.logger.log(`Scheduled storebrand seller notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { hours: 2, days: 1 });
    const endDate = add(startDate, { days: 1 });
    const soldTodayEstates = await this.estateModel.find({
      statusChanges: {
        $elemMatch: {
          from: 2,
          to: 3,
          date: { $lt: endDate, $gte: startDate },
        },
      },
    });

    this.logger.debug(`Estates sold today: ${JSON.stringify(soldTodayEstates.map((estate) => estate.estateId))}`);

    await this.estateNotifierService.notifyEstates({
      estates: soldTodayEstates,
      iconName: StorebrandSellerNotificationService.ICON_NAME,
      message: StorebrandSellerNotificationService.NOTIFICATION_MESSAGE,
      url: StorebrandSellerNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true }, // todo: buyers
      pushFeatureFlag: FeatureFlag.SendStorebrandSellerPush,
      feedFeatureFlag: FeatureFlag.SendStorebrandSellerFeed,
    });
  }
}
