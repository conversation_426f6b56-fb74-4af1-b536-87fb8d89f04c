import { ConsoleLogger, Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { SmsAuditService } from '../../pg/sms-audit/sms-audit.service';
import { SlackService } from '../../slack/slack.service';

@Injectable()
export class SmsDuplicationNotificationService {
  private readonly logger = new ConsoleLogger(SmsDuplicationNotificationService.name);
  constructor(private readonly smsAuditService: SmsAuditService, private readonly slackService: SlackService) {}

  @Cron('0 7 * * *', {
    name: SmsDuplicationNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering sms duplication report');
    await this.trigger();
    this.logger.log(`Finished sms duplication report`);
  }

  async trigger(): Promise<void> {
    const yesterday = new Date();
    yesterday.setDate(new Date().getDate() - 1);
    const smsDuplicates = await this.smsAuditService.findDuplicatesAtDate(yesterday);
    this.logger.log(`Duplicated sms-es sent out: ${JSON.stringify(smsDuplicates)}`);
    const smsCountSent = smsDuplicates.reduce((sum, current) => sum + parseInt(current.count), 0);

    if (smsCountSent > 0) {
      const errorMessage = `${smsCountSent} sms were sent out to ${smsDuplicates.length} phone numbers`;
      this.logger.log(`Sending Slack notification...`);
      this.logger.log(errorMessage);
      await this.slackService.sendSmsDuplicationError(errorMessage);
    }
  }
}
