import { ConsoleLogger, Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import axios from 'axios';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { UserService } from '../../pg/user/user.service';
import { sign } from '../../utils/jwt.utils';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';

export type WealthManagementSummaryEntry = {
  estateId?: string;
  streetAddress?: string;
  totalEstimatedValue: number | null;
  areFinancialDetailsFilled?: boolean;
  hasRecentBrokerValuation?: boolean;
  mortgage: {
    percentage: number | null;
    value: number | null;
  };
  equity: {
    percentage: number | null;
    value: number | null;
  };
  value: {
    initial: number | null;
    current: number | null;
    increasePercentage: number | null;
    increaseValue: number | null;
  };
  storebrandSaving: number | null;
  extendedMortgage: number | null;
  moreExpensiveProperty: number | null;
  affordMoreSpace: number | null;
};

export type WealthManagementSummary = {
  hasSentStorebrandLead: boolean;
  estates: WealthManagementSummaryEntry[];
  aggregation: WealthManagementSummaryEntry | null;
};

@Injectable()
export class ValueDistributionNotification {
  private static readonly NOTIFICATION_MESSAGE = 'Din estimerte egenkapital er nå [equityValue]';

  private static readonly ICON_NAME = 'officeRevenue';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/wealth';

  private readonly logger = new ConsoleLogger(ValueDistributionNotification.name);

  constructor(
    private readonly userService: UserService,
    private readonly appConfigService: AppConfigService,
    private readonly notificationService: NotificationService,
    private authorizationService: AuthorizationService,
    private readonly userNotifierService: UserNotifierService,
  ) {}

  @Cron('00 20 1 * *', {
    name: ValueDistributionNotification.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled value distribution notifications');
    await this.trigger();
    this.logger.log(`Scheduled value distribution notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const appUsers = await this.userService.findAll();

    for (const user of appUsers) {
      const token = sign({ userID: user.id, phoneNumber: user.phoneNumber, role: 'user' }, user.passwordCode);
      const valuationUrl = urljoin(this.appConfigService.getBackendUrl(), '/estates/valuation');
      const response = await axios.get<WealthManagementSummary>(valuationUrl, {
        headers: { authorization: `Bearer ${token}` },
        validateStatus: () => true,
      });

      if (response.status !== 200) {
        this.logger.error(`Received error from backend: ${response.status}, ${response.data}`);
        continue;
      }

      if (!response.data?.aggregation?.equity?.value) {
        continue;
      }

      this.logger.log(`Valuation for user ${user.id}: ${JSON.stringify(response.data.aggregation.equity.value)}`);

      const message = ValueDistributionNotification.NOTIFICATION_MESSAGE.replace(
        '[equityValue]',
        response.data.aggregation?.equity?.value.toLocaleString().replace(/,/g, ' '),
      );

      await this.userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
        feedFeatureFlag: FeatureFlag.SendValueDistributionFeed,
        iconName: ValueDistributionNotification.ICON_NAME,
        isFeatureEnabled: this.authorizationService.isFeatureEnabled,
        message,
        pushFeatureFlag: FeatureFlag.SendValueDistributionPush,
        redirectUrl: ValueDistributionNotification.NOTIFICATION_REDIRECT_URL,
        userId: user.id,
      });
    }
  }
}
