import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as he from 'he';
import { parsePhoneNumber } from 'libphonenumber-js';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { CraftCMSService } from '../../craft-cms/craft-cms.service';
import { UserService } from '../../pg/user/user.service';
import { Employee, EmployeeDocument } from '../../sync/schema/employee.schema';
import { UserNotifierService } from '../user-notifier.service';

export type CraftEntryType = 'incident' | 'news' | 'resource';

@Injectable()
export class BrokerNewsOrIncidentNotificationService {
  // These URLs will trigger the correct intent in the app
  private static readonly INCIDENT_NOTIFICATION_REDIRECT_URL = 'https://megler.nordvikbolig.no/driftsmeldinger';
  private static readonly NEWS_NOTIFICATION_REDIRECT_URL = 'https://megler.nordvikbolig.no/nyheter';
  private static readonly RESOURCE_NOTIFICATION_REDIRECT_URL = 'https://megler.nordvikbolig.no/hjelpesenter';

  private readonly logger = new ConsoleLogger(BrokerNewsOrIncidentNotificationService.name);

  constructor(
    private readonly userNotifierService: UserNotifierService,
    private readonly userService: UserService,
    private readonly craftCmsService: CraftCMSService,
    @InjectModel(Employee.name) private employeeModel: Model<EmployeeDocument>,
  ) {}

  async trigger({
    entryId,
    testUserIds,
    employeeIds,
    type,
    debug,
  }: {
    entryId: number;
    testUserIds?: string[];
    employeeIds?: string[];
    type: CraftEntryType;
    debug: boolean;
  }): Promise<void> {
    this.logger.log(`Triggering notification for entry ${entryId}`);
    let articleEntry = null;
    let incidentEntry = null;
    try {
      if (type.toLowerCase() === 'incident') {
        incidentEntry = await this.craftCmsService.getIncident(entryId.toString());
      } else {
        articleEntry = await this.craftCmsService.getArticle(entryId.toString());
      }
    } catch (error) {
      this.logger.error(`[Broker Push Notification]:[/craft-update] Error: failed to fetch article ${error}`);
    }

    if (!incidentEntry && !articleEntry) {
      this.logger.log('[Broker Push Notification]:[/craft-update] No incident or article found');
      // Do nothing, only act when incident or articles are updated
      return;
    }

    // Phone numbers on brokers in Mongo are without counry code
    let brokers = await this.employeeModel.find({
      employeeActive: true,
    });

    if (articleEntry && employeeIds?.length > 0) {
      brokers = brokers.filter((broker) => employeeIds.includes(broker.employeeId));
    }

    // Prepend country code, so we can query Postgres (where phone numbers are stored with country code)
    const brokerPhones = brokers
      .map((broker) => {
        const phone = broker.mobilePhone ? parsePhoneNumber(broker.mobilePhone, 'NO') : null;
        if (!phone || !phone.isValid()) {
          this.logger.warn(`Contact ${broker.name} has an invalid phone number set: ${broker.mobilePhone}`);
          return null;
        }
        return phone.number;
      })
      .filter((phone) => {
        return phone?.length > 0;
      });

    const usersByBrokers = await this.userService.findAllByPhoneNumbers(brokerPhones);

    this.logger.log(`Identified ${brokers.length}, of which ${usersByBrokers.length} have connected users in the app`);

    const defaultTestUserIds = [
      '56683747-bed9-4dca-8c1b-0ed961988667', // Max
      'd361a3db-1ab5-438f-8ab5-03db554f62f0', // Maciej private
      '26fd6821-fca4-4c3a-8a25-7a2db05a2ee0', // Maciej Unfold
    ];

    const userIds = debug ? testUserIds ?? defaultTestUserIds : usersByBrokers.map((user) => user.id);

    try {
      if (articleEntry) {
        const lowerCaseType = type.toLowerCase();
        const articleType = lowerCaseType === 'resource' ? 'Hjelpesenter' : 'Nyhet';
        const cleanText = he.decode(stripHTML(articleEntry.excerpt));
        const cleanTitle = he.decode(stripHTML(articleEntry.title));
        await this.userNotifierService.broadcastPushNotifyUsers({
          title: `${articleType}: ${cleanTitle}`,
          message: cleanText,
          userIds: userIds,
          redirectUrl: `${
            lowerCaseType === 'resource'
              ? BrokerNewsOrIncidentNotificationService.RESOURCE_NOTIFICATION_REDIRECT_URL
              : BrokerNewsOrIncidentNotificationService.NEWS_NOTIFICATION_REDIRECT_URL
          }/${articleEntry.slug}`,
          pushFeatureFlag: FeatureFlag.SendBrokerNews,
        });
      } else if (incidentEntry) {
        let text = incidentEntry.title;

        if ((incidentEntry?.updates.length ?? 0) > 0) {
          text = incidentEntry.updates[incidentEntry.updates.length - 1].text;
        }

        if (incidentEntry.resolved) {
          text = incidentEntry.resolvedComment ? incidentEntry.resolvedComment : text;
        }

        this.logger.log(`Notification for incident entry sending to ${usersByBrokers.length} brokers: ${text}`);

        const cleanText = he.decode(stripHTML(text));
        const cleanTitle = he.decode(stripHTML(incidentEntry.title));

        await this.userNotifierService.broadcastPushNotifyUsers({
          title: `${incidentEntry.resolved ? '✅ ' : ''}Driftsmelding: ${cleanTitle}`,
          message: cleanText,
          userIds: userIds,
          redirectUrl: `${BrokerNewsOrIncidentNotificationService.INCIDENT_NOTIFICATION_REDIRECT_URL}/${incidentEntry.slug}`,
          pushFeatureFlag: FeatureFlag.SendBrokerNews,
        });
      }
    } catch (e) {
      this.logger.log('Error sending push notification', e);
    }
  }
}

function stripHTML(htmlString) {
  return htmlString.replace(/<[^>]+>/g, '').replace(/&[0-9A-Za-z]+;/g, '');
}
