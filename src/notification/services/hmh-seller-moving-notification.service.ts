import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron } from '@nestjs/schedule';
import { add, startOfDay } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class HmhSellerMovingNotificationService {
  private static readonly NOTIFICATION_MESSAGE = '<PERSON><PERSON><PERSON><PERSON> flytteprosessen lettere, bestill flyttehjelp her!';

  private static readonly ICON_NAME = 'icon_finance_package'; // box icon

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/hmh-moving';

  private readonly logger = new ConsoleLogger(HmhSellerMovingNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: HmhSellerMovingNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled hmhmoving notifications');
    await this.trigger();
    this.logger.log(`Scheduled hmhmoving notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = add(startOfDay(new Date()), { days: 30, hours: 22 });
    const endDate = add(startDate, { days: 1 });
    const estatesWith31daysBeforeOtp = await this.estateModel.find({
      takeOverDate: {
        $lt: endDate,
        $gte: startDate,
      },
    });

    this.logger.debug(
      `Estates which have otp in 31 days: ${JSON.stringify(
        estatesWith31daysBeforeOtp.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesWith31daysBeforeOtp,
      iconName: HmhSellerMovingNotificationService.ICON_NAME,
      message: HmhSellerMovingNotificationService.NOTIFICATION_MESSAGE,
      url: HmhSellerMovingNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true },
      pushFeatureFlag: FeatureFlag.SendHmhSellerMovingPush,
      feedFeatureFlag: FeatureFlag.SendHmhSellerMovingFeed,
    });
  }
}
