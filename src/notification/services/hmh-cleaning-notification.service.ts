import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class HmhCleaningNotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'Er boligen din klar for fotografering? Bestill vask her';

  private static readonly ICON_NAME = 'key'; // todo

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/hmh-cleaning';

  private readonly logger = new ConsoleLogger(HmhCleaningNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 21 * * *', {
    name: HmhCleaningNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled hmhcleaning notifications');
    await this.trigger();
    this.logger.log(`Scheduled hmhcleaning notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { hours: 2, days: 1 });
    const endDate = add(startDate, { days: 1 });
    const biddingCompleteToday = await this.estateModel.find({
      statusChanges: {
        $elemMatch: {
          to: 1,
          date: { $lt: endDate, $gte: startDate },
        },
      },
    });

    this.logger.debug(
      `Estates which had bidding complete today: ${JSON.stringify(
        biddingCompleteToday.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: biddingCompleteToday,
      iconName: HmhCleaningNotificationService.ICON_NAME,
      message: HmhCleaningNotificationService.NOTIFICATION_MESSAGE,
      url: HmhCleaningNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true },
      pushFeatureFlag: FeatureFlag.SendHmhCleaningPush,
      feedFeatureFlag: FeatureFlag.SendHmhCleaningFeed,
    });
  }
}
