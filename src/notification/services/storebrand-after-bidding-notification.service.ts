import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class StorebrandAfterBiddingNotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'Gratulerer! Utforsk dine muligheter med Storebrand';

  private static readonly ICON_NAME = 'revenue';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/storebrand';

  private readonly logger = new ConsoleLogger(StorebrandAfterBiddingNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('10 20 * * *', {
    name: StorebrandAfterBiddingNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled storebrand buyer notifications');
    await this.trigger();
    this.logger.log(`Scheduled storebrand buyer notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 1, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesSoldYesterday = await this.estateModel.find({
      soldDate: { $lt: endDate, $gte: startDate },
    });

    this.logger.debug(
      `Estates which were sold recently: ${JSON.stringify(estatesSoldYesterday.map((estate) => estate.estateId))}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesSoldYesterday,
      iconName: StorebrandAfterBiddingNotificationService.ICON_NAME,
      message: StorebrandAfterBiddingNotificationService.NOTIFICATION_MESSAGE,
      url: StorebrandAfterBiddingNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true, buyer: true },
      pushFeatureFlag: FeatureFlag.SendStorebrandAfterBiddingPush,
      feedFeatureFlag: FeatureFlag.SendStorebrandAfterBiddingFeed,
    });
  }
}
