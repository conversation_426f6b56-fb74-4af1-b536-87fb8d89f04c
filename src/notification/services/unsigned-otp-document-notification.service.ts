import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';
import { add, startOfDay, sub } from 'date-fns';
import { ParseError, parsePhoneNumberWithError } from 'libphonenumber-js';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { OvertakeProtocolParticipantService } from '../../pg/overtake-protocol-participant/overtake-protocol-participant.service';
import { OvertakeProtocolService } from '../../pg/overtake-protocol/overtake-protocol.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { BrokerRole, Estate, EstateBrokerIdWithRolesNested, EstateDocument } from '../../sync/schema/estate.schema';
import { NotificationService } from '../notification.service';
@Injectable()
export class UnsignedOtpDocumentNotificationService {
  private readonly logger = new ConsoleLogger(UnsignedOtpDocumentNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE =
    'Hei. Vi mangler signaturen til [name] for å kunne fullføre overtakelsesen av [address].\n\nFølg denne linken for å signere og fullføre overtakelsen: [link].\n\nMed vennlig hilsen,\nNordvik\n\nNB! Dette er en automatisk generert SMS';

  private static readonly NOTIFICATION_MESSAGE_HTML_TEMPLATE =
    'Hei. Vi mangler signaturen til [name] for å kunne fullføre overtakelsesen av [address].<br><br>Følg denne linken for å signere og fullføre overtakelsen: <a href="[link]">[link]</a>.';

  private readonly idfyClient = new IdfyClient(
    this.appConfigService.getIdfyId(),
    this.appConfigService.getIdfySecret(),
    ['document_read', 'document_file', 'document_write'],
  );

  constructor(
    private readonly otpService: OvertakeProtocolService,
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly participantService: OvertakeProtocolParticipantService,
    private readonly mailService: MailService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  sendNotification = async (
    signers: {
      link: string;
      phoneNumber: string;
      name: string;
    }[],
    estate: Estate,
    broker: EstateBrokerIdWithRolesNested,
  ): Promise<void> => {
    for (const signer of signers) {
      this.logger.log(
        `Sending unsigned OTP documenet notification to ${signer.phoneNumber}, ${broker.employee.email} and ${broker.employee.mobilePhone}`,
      );

      const url = await axios.post<{ shortUrl: string }>(
        urljoin(this.appConfigService.getBackendUrl(), '/url'),
        { url: signer.link },
        { headers: { 'x-api-key': this.appConfigService.getBackendApiKey() } },
      );
      const message = UnsignedOtpDocumentNotificationService.NOTIFICATION_MESSAGE.replace(
        '[link]',
        urljoin(this.appConfigService.getBackendUrl(), '/url', url.data.shortUrl),
      )
        .replace('[name]', signer.name)
        .replace('[address]', estate.address.streetAdress);
      const messageHtml = UnsignedOtpDocumentNotificationService.NOTIFICATION_MESSAGE_HTML_TEMPLATE.replace(
        '[link]',
        urljoin(this.appConfigService.getBackendUrl(), '/url', url.data.shortUrl),
      )
        .replace('[name]', signer.name)
        .replace('[address]', estate.address.streetAdress);

      // if (broker.employee.mobilePhone) {
      //   try {
      //     await this.notificationService.triggerSmsNotification({
      //       message,
      //       phoneNumber: broker.employee.mobilePhone,
      //       audit: { smsAuditType: SmsAuditType.OTP_SIGNING_REMINDER, estateId: estate.estateId },
      //       vitecContactOptions: {
      //         departmentId: estate.departmentId,
      //         fromEmployeeId: estate.brokersIdWithRoles.find((b) => b.brokerRole === BrokerRole.MAIN_BROKER).employeeId,
      //         contactId: '',
      //       },
      //     });
      //   } catch (e) {
      //     this.logger.error(`Error sending broker SMS to ${broker.employee.mobilePhone}, error ${e}`);
      //   }
      // } else {
      //   this.logger.warn(
      //     `SMS to broker about otp signing ${estate.address} reminder was not sent because no phone number was found.`,
      //   );
      // }

      // try {
      //   await this.mailService.sendMail({
      //     to: broker.employee.email,
      //     html: messageHtml,
      //     subject: 'Mangler signatur fra Overtakelsesprotokoll',
      //     mailAuditType: MailAuditType.OTP,
      //   });
      // } catch (e) {
      //   this.logger.error(`Error sending email to ${broker.employee.email}, error ${e}`);
      // }

      if (!signer.phoneNumber) {
        this.logger.warn(`Signer ${signer.name} has no phone number set for sending SMS about otp signing reminder`);
        continue;
      }

      try {
        const parsedSignerPhoneNumber = parsePhoneNumberWithError(signer.phoneNumber, 'NO').number;
        const contact = [...estate.sellers, ...estate.buyers, ...estate.proxies].find(
          (s) => parsePhoneNumberWithError(s.mobilePhone, 'NO').number === parsedSignerPhoneNumber,
        );
        await this.notificationService.triggerSmsNotification({
          message,
          phoneNumber: signer.phoneNumber,
          audit: { smsAuditType: SmsAuditType.OTP_SIGNING_REMINDER, estateId: estate.estateId },
          vitecContactOptions: {
            departmentId: estate.departmentId,
            fromEmployeeId: undefined,
            contactId: contact.contactId,
          },
        });
      } catch (e) {
        this.logger.error(
          `Error sending SMS to ${signer.phoneNumber}, error ${e instanceof ParseError ? e.message : e}`,
        );
      }
    }
  };

  @Cron(CronExpression.EVERY_DAY_AT_9AM, {
    name: UnsignedOtpDocumentNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering unsigned overtake-protocol notifications');
    await this.trigger();
    this.logger.log(`Scheduled unsigned overtake-protocol notifications are triggered`);
  }

  async trigger(): Promise<void> {
    if (!this.authorizationService.isFeatureEnabled(FeatureFlag.SendUnsignedDocumentNotification)) {
      this.logger.warn(
        `No notifications of any kind will be sent out, because the corresponding feature flag is disabled: ${FeatureFlag.SendUnsignedDocumentNotification}`,
      );
      return;
    }
    const startDate = sub(startOfDay(new Date()), { days: 1 });
    const endDate = add(startDate, { days: 1 });
    const unfinalizedOtpsYesterday = await this.otpService.findAllUnfinishedBetweenSignStartDates(startDate, endDate);

    this.logger.debug(
      `Estates that had unfinalized OTP yesterday: ${JSON.stringify(
        unfinalizedOtpsYesterday.map((estate) => estate.estateVitecId),
      )}`,
    );

    for (const otp of unfinalizedOtpsYesterday) {
      try {
        const estate = await this.estateModel.findOne({ estateId: otp.estateVitecId });
        const mainBroker = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0];
        const signers = await this.idfyClient.signature.listSigners(otp.idfyDocumentId);
        const unsignedSigners = signers.filter((s) => !s.documentSignature).map((s) => s.externalSignerId);
        const participants = await this.participantService.findAllForOtp(otp.id);
        const unsignedParticipants = participants
          .filter((p) => unsignedSigners.includes(p.id))
          .map((p) => ({
            link: signers.find((s) => s.externalSignerId === p.id).url,
            phoneNumber: p.phoneNumber,
            name: p.name,
          }));
        await this.sendNotification(unsignedParticipants, estate, mainBroker);
      } catch (e) {
        this.logger.error(e);
      }
    }
  }
}
