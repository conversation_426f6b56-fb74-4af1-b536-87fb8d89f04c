import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class EtakstExpiredNotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    'E-taksten på boligen din er utgått. Ta kontakt med din megler for å fornye den';

  private static readonly ICON_NAME = 'script';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/wealth/';

  private readonly logger = new ConsoleLogger(EtakstExpiredNotificationService.name);

  constructor(
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    private readonly estateNotifierService: UserNotifierService,
  ) {}

  @Cron('00 20 * * *', {
    name: EtakstExpiredNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled etakst expired notifications');
    await this.trigger();
    this.logger.log(`Scheduled etakst expired notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 93, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesWithExpiredEtakstPublish = await this.estateModel.find({
      documents: {
        $elemMatch: {
          docType: 14,
          lastChanged: { $lt: endDate, $gte: startDate },
        },
      },
    });

    this.logger.debug(
      `Estates which had expired etakts: ${JSON.stringify(
        estatesWithExpiredEtakstPublish.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesWithExpiredEtakstPublish,
      iconName: EtakstExpiredNotificationService.ICON_NAME,
      message: EtakstExpiredNotificationService.NOTIFICATION_MESSAGE,
      url: EtakstExpiredNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true },
      pushFeatureFlag: FeatureFlag.SendEtakstExpiredPush,
      feedFeatureFlag: FeatureFlag.SendEtakstExpiredFeed,
    });
  }
}
