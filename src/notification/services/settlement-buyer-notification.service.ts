import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { startOfDay, sub } from 'date-fns';
import { ParseError, parsePhoneNumberWithError } from 'libphonenumber-js';
import { isNull } from 'lodash';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { MailAuditType } from '../../pg/mail-audit/mail-audit.model';
import { SettlementBuyer, SettlementBuyerParticipant } from '../../pg/settlement-buyer/settlement-buyer.model';
import { SettlementBuyerService } from '../../pg/settlement-buyer/settlement-buyer.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { Buyer, BuyerDocument } from '../../sync/schema/buyer.schema';
import {
  BrokerRole,
  CheckListTag,
  CheckListValue,
  Estate,
  EstateBaseType,
  EstateDocument,
} from '../../sync/schema/estate.schema';
import { populateSettlementMessageTemplate } from '../../utils/message.utils';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';

@Injectable()
export class SettlementBuyerNotificationService {
  private readonly logger = new ConsoleLogger(SettlementBuyerNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE_TEMPLATE =
    'Hei,\n\nOppgjørsskjema for kjøp av [address] er klart til utfylling. Følg linken under for å fylle ut skjemaet. Merk at skjemaet må signeres av alle kjøpere.\n\n[URL]\n\nMed vennlig hilsen\n\nNordvik';
  private static readonly NOTIFICATION_MESSAGE_HTML_TEMPLATE =
    'Oppgjørsskjema for kjøp av [address] er klart til utfylling. Følg linken under for å fylle ut skjemaet. Merk at skjemaet må signeres av alle kjøpere.<br><br><a href="[URL]">[URL]</a>';

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly mailService: MailService,
    private readonly settlementBuyerService: SettlementBuyerService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
  ) {}

  isSettlementBuyerFormCreationEnabled(estate: Estate): boolean {
    if (!estate || estate.status !== VitecEstateStatus.OVERSOLD) {
      return false;
    }
    return this.notificationService.estateHasOneOfTheChecklistsClicked({
      estate,
      tags: [
        CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SALE,
        CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SETTLEMENT,
        CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_FORECLOSURE,
      ],
    });
  }

  isSettlementBuyerProjectFormCreationEnabled(estate: Estate): boolean {
    if (!estate) {
      return false;
    }
    return this.notificationService.estateHasOneOfTheChecklistsClicked({
      estate,
      tags: [
        CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_PROJECT,
        CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_PROJECT_ADVANCE,
      ],
    });
  }

  private isSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementBuyerSms, estateId);

  private isBrokerSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementBuyerBrokerSms, estateId);

  private isEmailEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementBuyerEmail, estateId);

  private async getOrCreateSettlementBuyerForm(estateId: string, isProject = false): Promise<SettlementBuyer> {
    let settlementBuyer = await this.settlementBuyerService.getByEstateIdAndEstateBaseTypeId(
      estateId,
      isProject ? EstateBaseType.PROJECT : null,
    );
    if (isNull(settlementBuyer)) {
      try {
        const url = urljoin(
          this.appConfigService.getBackendUrl(),
          'estate',
          estateId,
          'settlement-buyer',
          isProject ? '?estateBaseType=4' : '',
        );

        const response = await axios.post<SettlementBuyer>(url, {
          validateStatus: () => true,
        });
        settlementBuyer = response.data;

        this.logger.log(`Settlement buyer form created for estate with estateId: ${estateId}`);
      } catch (error) {
        this.logger.error(`Received error from BE: ${error.response.status}:${JSON.stringify(error.response.data)}`);
      }
    }

    return settlementBuyer;
  }

  async createFormAndSendNotificationsIfNotSent(estate: Estate, isProject = false): Promise<void> {
    if (
      !this.isSmsEnabled(estate.estateId) &&
      !this.isEmailEnabled(estate.estateId) &&
      !this.isBrokerSmsEnabled(estate.estateId)
    ) {
      this.logger.warn(
        `Skipping settlement-buyer notification sending for estate: ${estate.estateId}. The following feature flags are disabled: ${FeatureFlag.SendSettlementBuyerEmail}, ${FeatureFlag.SendSettlementBuyerSms}, ${FeatureFlag.SendSettlementBuyerBrokerSms}`,
      );
      return;
    }

    const existingRegularSettlementBuyerFormForEstate =
      await this.settlementBuyerService.getByEstateIdAndEstateBaseTypeId(estate.estateId, null);
    if (isProject && !!existingRegularSettlementBuyerFormForEstate) {
      this.logger.log(
        `Regular settlement-buyer form exists for estate ${estate.estateId}, will not create settlement-buyer-project form.`,
      );
      return;
    }

    const settlementBuyer = await this.getOrCreateSettlementBuyerForm(estate.estateId, isProject);
    if (isNull(settlementBuyer)) {
      // some error occured, continue to next estate
      return;
    }

    if (settlementBuyer.isNotificationSent || settlementBuyer.signingStarted) {
      this.logger.log(
        `Skipping settlement-buyer notification sending: the notification is already sent or signing already started for estate: ${estate.estateId}`,
      );
      return;
    }

    // getOrCreateSettlementBuyerForm will create the form and the participants on the backend that need to be notified
    const buyers = await this.settlementBuyerService.getParticipantsForForm(settlementBuyer.id);
    this.logger.log(
      `Found settlement-buyers for estate: ${estate.estateId}, buyers: ${buyers.map((b) => b.id).join(',')}`,
    );

    await this.sendEmailAndSmsNotifications(estate, buyers, isProject);
    await this.settlementBuyerService.setIsNotificationSent({
      estateId: estate.estateId,
      value: true,
      estateBaseType: isProject ? EstateBaseType.PROJECT : null,
    });
  }

  async sendEmailAndSmsNotifications(
    estate: Estate,
    buyers: SettlementBuyerParticipant[],
    isProject = false,
  ): Promise<void> {
    const message = populateSettlementMessageTemplate({
      template: SettlementBuyerNotificationService.NOTIFICATION_MESSAGE_TEMPLATE,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
      type: 'buyer',
      isProject,
    });
    const mailMessage = populateSettlementMessageTemplate({
      template: SettlementBuyerNotificationService.NOTIFICATION_MESSAGE_HTML_TEMPLATE,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
      type: 'buyer',
      isProject,
    });

    const brokerPhoneNumber = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0]
      ?.employee?.mobilePhone;

    // if (brokerPhoneNumber && this.isBrokerSmsEnabled(estate.estateId)) {
    //   await this.sendBrokerSms(estate, brokerPhoneNumber, message);
    // } else {
    //   this.logger.warn(`Skipping settlement-buyer broker sms notification sending for estate: ${estate.estateId}.
    //   The following feature flags are disabled: ${FeatureFlag.SendSettlementBuyerBrokerSms}`);
    // }

    if (this.isEmailEnabled(estate.estateId)) {
      await this.sendBrokerEmail(estate, brokerPhoneNumber, mailMessage);
    } else {
      this.logger.warn(`Skipping settlement-buyer broker email notification sending for estate: ${estate.estateId}.
      The following feature flags are disabled: ${FeatureFlag.SendSettlementBuyerEmail}`);
    }

    for (const buyer of buyers) {
      if (!buyer.phoneNumber) {
        this.logger.warn(`Buyer ${buyer.id} has no phone number set`);
      } else {
        if (this.isSmsEnabled(estate.estateId)) {
          await this.sendBuyerParticipantSms(estate, buyer, message);
        } else {
          this.logger.warn(
            `Skipping settlement-buyer sms notification sending for estate: ${estate.estateId}, to buyer ${buyer.phoneNumber}. The following feature flags are disabled: ${FeatureFlag.SendSettlementBuyerSms}`,
          );
        }
      }

      if (this.isEmailEnabled(estate.estateId)) {
        await this.sendBuyerParticipantEmail(estate, buyer, mailMessage);
      } else {
        this.logger.warn(
          `Skipping settlement-buyer email notification sending for estate: ${estate.estateId}, to buyer ${buyer.phoneNumber}. The following feature flags are disabled: ${FeatureFlag.SendSettlementBuyerEmail}`,
        );
      }
    }
  }

  private async sendBuyerParticipantEmail(estate: Estate, buyer: SettlementBuyerParticipant, mailMessage: string) {
    this.logger.log(
      `Sending settlement-buyer email notification for estate: ${estate.estateId}, to buyer ${buyer.phoneNumber}`,
    );
    try {
      await this.mailService.sendMail({
        to: buyer.email,
        subject: 'Oppgjørsskjema Kjøper',
        html: mailMessage,
        mailAuditType: MailAuditType.SETTLEMENT,
      });
    } catch (e) {
      this.logger.error(`Error sending email to customer ${buyer.email}, error ${e}`);
    }
  }

  private async sendBuyerParticipantSms(estate: Estate, buyer: SettlementBuyerParticipant, message: string) {
    this.logger.log(
      `Sending settlement-buyer sms notification for estate: ${estate.estateId}, to buyer ${buyer.phoneNumber}`,
    );
    try {
      const parsedBuyerPhoneNumber = parsePhoneNumberWithError(buyer.phoneNumber, 'NO').number;
      const contact = [...estate.buyers, ...estate.proxies].find(
        (b) => parsePhoneNumberWithError(b.mobilePhone, 'NO').number === parsedBuyerPhoneNumber,
      );
      await this.notificationService.delayByLastSentSms(buyer.phoneNumber, SmsAuditType.SETTLEMENT_BUYER_SMS);

      await this.notificationService.triggerSmsNotification({
        message,
        phoneNumber: buyer.phoneNumber,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.SETTLEMENT_BUYER_SMS,
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: contact.contactId,
        },
      });
    } catch (e) {
      this.logger.error(`Error sending SMS to ${buyer.phoneNumber}, error ${e instanceof ParseError ? e.message : e}`);
    }
  }

  private async sendBrokerEmail(estate: Estate, brokerPhoneNumber: string, mailMessage: string) {
    const brokerEmail = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0]?.employee
      ?.email;
    this.logger.log(
      `Sending settlement-buyer broker email notification for estate: ${estate.estateId}, broker: ${brokerPhoneNumber}`,
    );
    await this.mailService
      .sendMail({
        to: brokerEmail,
        subject: `Oppgjørsskjema Kjøper - ${estate.assignmentNum} ${estate.address.streetAdress}`,
        html: mailMessage,
        mailAuditType: MailAuditType.SETTLEMENT,
      })
      .catch((e) =>
        this.logger.error(
          `Error sending settlement-buyer email for estate: ${estate.estateId}, to ${brokerEmail}, error: ${e}`,
        ),
      );
  }

  private async sendBrokerSms(estate: Estate, brokerPhoneNumber: string, message: string) {
    this.logger.log(
      `Sending settlement-buyer broker sms notification for estate: ${estate.estateId}, broker: ${brokerPhoneNumber}`,
    );

    await this.notificationService.delayByLastSentSms(brokerPhoneNumber, SmsAuditType.SETTLEMENT_BUYER_SMS);

    await this.notificationService
      .triggerSmsNotification({
        message,
        phoneNumber: brokerPhoneNumber,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.SETTLEMENT_BUYER_SMS,
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: '',
        },
      })
      .catch((e) =>
        this.logger.error(
          `Error sending settlement-buyer sms for estate: ${estate.estateId}, to broker ${brokerPhoneNumber}, error: ${e}`,
        ),
      );
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 10 });

    const estates = await this.estateModel.find({
      status: VitecEstateStatus.OVERSOLD,
      'checkList.checkListItems': {
        $elemMatch: {
          tags: {
            $in: [
              CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SALE,
              CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SETTLEMENT,
              CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_FORECLOSURE,
            ],
          },
          value: CheckListValue.YES,
          changedDate: {
            $gte: startDate,
          },
        },
      },
    });

    this.logger.log(
      `Estates that we are sending settlement-buyer notifications: ${JSON.stringify(
        estates.map((estate) => estate.estateId),
      )}`,
    );

    for (const estate of estates) {
      try {
        await this.createFormAndSendNotificationsIfNotSent(estate);
      } catch (e) {
        this.logger.error(`Error sending settlement-buyer notifications to estate: ${estate.estateId}, error ${e}`);
      }
    }
  }

  async triggerProject(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 3 });

    const estates = await this.estateModel.find({
      'checkList.checkListItems': {
        $elemMatch: {
          tags: {
            $in: [
              CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_PROJECT,
              CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_PROJECT_ADVANCE,
            ],
          },
          value: CheckListValue.YES,
          changedDate: {
            $gte: startDate,
          },
        },
      },
    });

    this.logger.log(
      `Estates that we are sending settlement-buyer project notifications: ${JSON.stringify(
        estates.map((estate) => estate.estateId),
      )}`,
    );

    for (const estate of estates) {
      try {
        await this.createFormAndSendNotificationsIfNotSent(estate, true);
      } catch (e) {
        this.logger.error(`Error sending settlement-buyer notifications to estate: ${estate.estateId}, error ${e}`);
      }
    }
  }
}
