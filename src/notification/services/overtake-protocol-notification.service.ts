import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, endOfDay, startOfDay, sub } from 'date-fns';
import { parsePhoneNumber } from 'libphonenumber-js';
import { uniqBy } from 'lodash';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { NordvikboligApiService } from '../../norvikbolig-api/nordvikbolig-api.service';
import { MailAuditType } from '../../pg/mail-audit/mail-audit.model';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { UserService } from '../../pg/user/user.service';
import { Buyer, BuyerDocument } from '../../sync/schema/buyer.schema';
import {
  BrokerRole,
  CheckListTag,
  Estate,
  EstateBrokerIdWithRolesNested,
  EstateDocument,
  isCheckListItemChecked,
} from '../../sync/schema/estate.schema';
import { Proxy, ProxyDocument } from '../../sync/schema/proxy.schema';
import { Seller, SellerDocument } from '../../sync/schema/seller.schema';
import { populateOtpMessageTemplate } from '../../utils/message.utils';
import { EstateAssignmentTypeGroup } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class OvertakeProtocolNotificationService {
  private static readonly NOTIFICATION_MESSAGE_TEMPLATE =
    'For overtakelse av [address], bruk følgende link: [URL].\n\nDin megler har aktivert overtakelsesprotokollen, og den skal åpnes sammen av kjøper og selger på overtakelsen. Husk å ta med BankID til overtakelsen.\n\nNB! Dere skal kun bruke én telefon for å fylle ut protokollen.\n\nMed vennlig hilsen Nordvik';

  private static readonly NOTIFICATION_MESSAGE_HTML_TEMPLATE =
    'For overtakelse av [address], bruk følgende link: <a href="[URL]">[URL]</a>.<br><br>Din megler har aktivert overtakelsesprotokollen, og den skal åpnes sammen av kjøper og selger på overtakelsen. Husk å ta med BankID til overtakelsen.<br><br>NB! Dere skal kun bruke én telefon for å fylle ut protokollen.';

  private static readonly ICON_NAME = 'key';

  constructor(
    private readonly userService: UserService,
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly mailService: MailService,
    private readonly userNotifierService: UserNotifierService,
    private readonly logger: ConsoleLogger,
    private readonly nordvikApiService: NordvikboligApiService,

    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
    @InjectModel(Proxy.name) private proxyModel: Model<ProxyDocument>,
  ) {
    this.logger.setContext(OvertakeProtocolNotificationService.name);
  }

  private isSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendOtpSms, estateId);

  private isBrokerSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendBrokerOtpSms, estateId);

  private isEmailEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendOtpEmail, estateId);

  private isFeedEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendOtpFeed, estateId);

  private isPushEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendOtpPush, estateId);

  private async getEstateContacts(estate: Estate): Promise<
    Array<{
      firstName: string;
      lastName: string;
      contactId: string;
      mobilePhone: string;
      email: string;
    }>
  > {
    const proxyIds = estate.proxies.map((p) => p.contactId);
    const proxiedSellerIds = estate.proxies.flatMap((p) => p.proxyOf);
    const proxies = await this.proxyModel.find({ contactId: { $in: proxyIds } });
    const sellerIds = estate.sellers.map((s) => s.contactId).filter((s) => !proxiedSellerIds.includes(s));
    const sellers = await this.sellerModel.find({ contactId: { $in: sellerIds } });
    const buyerIds = estate.buyers.map((b) => b.contactId);
    const buyers = await this.buyerModel.find({ contactId: { $in: buyerIds } });

    const list = uniqBy([...sellers, ...buyers, ...proxies], (e) => e.contactId);

    try {
      const apiContacts = await this.nordvikApiService.getByRawGraphql<{
        estate?: {
          sellersEntries?: {
            contactId: string;
            proxy?: {
              firstName: string;
              lastName: string;
              contactId: string;
              mobilePhone: string;
              email: string;
            } | null;
            companyContactPerson?: {
              firstName: string;
              lastName: string;
              contactId: string;
              mobilePhone: string;
              email: string;
            } | null;
          }[];
        };
      }>(
        `
          query Estates($estateId: String!) {
            estate(id: $estateId, all: true, statuses: [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9]) {
              sellersEntries {
                contactId
                proxy {
                  firstName
                  lastName
                  contactId
                  mobilePhone
                  email
                }
                companyContactPerson {
                  firstName
                  lastName
                  contactId
                  mobilePhone
                  email
                }
              }
            }
          }
        `,
      );

      return list.map((e) => {
        const toSwap = apiContacts.estate?.sellersEntries?.find((entry) => entry.contactId === e.contactId);

        if (toSwap?.proxy) {
          return toSwap.proxy;
        }

        if (toSwap?.companyContactPerson) {
          return toSwap.companyContactPerson;
        }

        return e;
      });
    } catch (e) {
      this.logger.error(
        `Error fetching contacts from Nordvik API for estate: ${estate.estateId}, error: ${e}. Returning contacts from Vitec.`,
      );
      return list;
    }
  }

  shouldCreateOtpForEstate(estate: Estate): boolean {
    if (!estate) {
      return false;
    }
    switch (estate.assignmentTypeGroup) {
      case EstateAssignmentTypeGroup.PROJECT_SALE:
        return isCheckListItemChecked(estate.checkList, CheckListTag.ACTIVATE_OTP_FOR_PROJECT);
      case EstateAssignmentTypeGroup.SALE:
        return !isCheckListItemChecked(estate.checkList, CheckListTag.DEACTIVATE_OTP_FOR_SALE);
      case EstateAssignmentTypeGroup.SETTLEMENT:
        return !isCheckListItemChecked(estate.checkList, CheckListTag.DEACTIVATE_OTP_FOR_SETTLEMENT);
      default:
        return true;
    }
  }

  async sendNotificationsIfFeatureFlagEnabled(estate: Estate): Promise<void> {
    if (
      !this.isSmsEnabled(estate.estateId) &&
      !this.isBrokerSmsEnabled(estate.estateId) &&
      !this.isEmailEnabled(estate.estateId) &&
      !this.isFeedEnabled(estate.estateId) &&
      !this.isPushEnabled(estate.estateId)
    ) {
      this.logger.warn(
        `Skipping otp notification sending for estate: ${estate.estateId}. The following feature flags are disabled: ${FeatureFlag.SendOtpSms}, ${FeatureFlag.SendBrokerOtpSms}, ${FeatureFlag.SendOtpEmail}, ${FeatureFlag.SendOtpFeed}, ${FeatureFlag.SendOtpPush}`,
      );
      return;
    }

    const contacts = await this.getEstateContacts(estate);
    await this.sendNotification(estate, contacts);
  }

  async sendNotification(
    estate: Estate,
    contacts: Array<{
      firstName: string;
      lastName: string;
      contactId: string;
      mobilePhone: string;
      email: string;
    }>,
  ): Promise<void> {
    try {
      const relativeUrl = urljoin('customer', 'overtake-protocol', estate.estateId);
      const url = urljoin(this.appConfigService.getAppUrl(), relativeUrl);
      const message = populateOtpMessageTemplate({
        template: OvertakeProtocolNotificationService.NOTIFICATION_MESSAGE_TEMPLATE,
        address: estate.address.streetAdress,
        url,
      });
      const mailMessage = populateOtpMessageTemplate({
        template: OvertakeProtocolNotificationService.NOTIFICATION_MESSAGE_HTML_TEMPLATE,
        address: estate.address.streetAdress,
        url,
      });

      const brokerRolesToInclude = [BrokerRole.MAIN_BROKER, BrokerRole.RESPONSIBLE_BROKER];
      const brokers = uniqBy(
        estate.brokersIdWithRoles.filter((b) => brokerRolesToInclude.includes(b.brokerRole)),
        (b) => b.employeeId,
      );

      this.logger.log(`Sending OTP broker notifications for estate: ${estate.estateId}`);
      for (const broker of brokers) {
        //   if (this.isBrokerSmsEnabled(estate.estateId)) {
        //     if (broker.employee.mobilePhone) {
        //       await this.sendBrokerSms(broker, message, estate);
        //     } else {
        //       this.logger.warn(
        //         `Broker: ${broker.employee.name} has no phone number set, skipping OTP broker SMS for estate: ${estate.estateId}`,
        //       );
        //     }
        //   } else {
        //     this.logger.warn(
        //       `Skipping OTP broker SMS notification sending for estate: ${estate.estateId}, to broker ${broker.employee.name}.
        //     The following feature flags are disabled: ${FeatureFlag.SendBrokerOtpSms}`,
        //     );
        //   }

        if (this.isEmailEnabled(estate.estateId)) {
          if (broker.employee.email) {
            await this.sendBrokerEmail(estate, broker.employee, mailMessage);
          } else {
            this.logger.warn(
              `Broker: ${broker.employee.name} has no email set, skipping OTP broker email for estate: ${estate.estateId}`,
            );
          }
        } else {
          this.logger.warn(
            `Skipping OTP broker email notification sending for estate: ${estate.estateId}, broker: ${broker.employee.name}.
          The following feature flags are disabled: ${FeatureFlag.SendOtpEmail}`,
          );
        }
      }

      for (const contact of contacts) {
        this.logger.log(`Sending OTP contact notifications for estate: ${estate.estateId}`);

        if (this.isSmsEnabled(estate.estateId)) {
          if (contact.mobilePhone) {
            await this.sendContactSms(contact, message, estate);
          } else {
            this.logger.warn(
              `Contact ${contact.contactId} has no phone number set, skipping OTP SMS for estate: ${estate.estateId}`,
            );
          }
        } else {
          this.logger.warn(
            `Skipping OTP contact SMS notification sending for estate: ${estate.estateId}, to contact ${contact.contactId}.
          The following feature flags are disabled: ${FeatureFlag.SendOtpSms}`,
          );
        }

        if (this.isEmailEnabled(estate.estateId)) {
          if (contact.email) {
            await this.sendContactEmail(estate, contact, mailMessage);
          } else {
            this.logger.warn(
              `Contact with contactId: ${contact.contactId} has no email set, skipping OTP email for estate: ${estate.estateId}`,
            );
          }
        } else {
          this.logger.warn(
            `Skipping OTP contact email notification sending for estate: ${estate.estateId}, to contact: ${contact.contactId}.
          The following feature flags are disabled: ${FeatureFlag.SendOtpEmail}`,
          );
        }

        if (!contact.mobilePhone) {
          this.logger.warn(
            `Contact: ${contact.contactId} has no phone number set, skipping in-app OTP user notifications for estate: ${estate.estateId}`,
          );
          continue;
        }

        const phoneNumber = parsePhoneNumber(contact.mobilePhone || '', 'NO');
        if (!phoneNumber || !phoneNumber.isValid()) {
          this.logger.warn(
            `Contact ${contact.contactId} has an invalid phone number set: ${contact.mobilePhone}, skipping in-app OTP user notifications for estate: ${estate.estateId}`,
          );
          continue;
        }

        const appUser = await this.userService.findByPhoneNumber(phoneNumber.number);

        if (!appUser) {
          this.logger.warn(
            `AppUser not found with phoneNumber: ${phoneNumber.number}, skipping in-app OTP user notifications for estate: ${estate.estateId}`,
          );
          continue;
        }

        await this.userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
          feedFeatureFlag: FeatureFlag.SendOtpFeed,
          pushFeatureFlag: FeatureFlag.SendOtpPush,
          isFeatureEnabled: (featureFlag) =>
            this.authorizationService.isFeatureEnabledForEstate(featureFlag, estate.estateId),
          iconName: OvertakeProtocolNotificationService.ICON_NAME,
          message: message.substr(0, 254),
          redirectUrl: relativeUrl,
          userId: appUser.id,
        });
      }
    } catch (e) {
      this.logger.error(`Error sending OTP notifications to estate: ${estate.estateId}, error ${e}`);
    }
  }

  private async sendContactEmail(
    estate: Estate,
    contact: {
      firstName: string;
      lastName: string;
      contactId: string;
      email: string;
    },
    mailMessage: string,
  ) {
    this.logger.log(
      `Sending OTP contact email notification for estate: ${estate.estateId}, contactId: ${contact.contactId}, email: ${contact.email}.`,
    );
    await this.mailService
      .sendMail({
        to: contact.email,
        subject: 'Overtakelsesprotokoll',
        html: mailMessage,
        mailAuditType: MailAuditType.OTP,
      })
      .catch((e) =>
        this.logger.error(
          `Error sending OTP contact email notification for estate: ${estate.estateId}, contact: ${contact.contactId}, email: ${contact.email}, error: ${e}`,
        ),
      );
  }

  private async sendContactSms(
    contact: { firstName: string; lastName: string; contactId: string; mobilePhone: string },
    message: string,
    estate: Estate,
  ) {
    await this.notificationService.delayByLastSentSms(contact.mobilePhone, SmsAuditType.OTP_SMS);

    await this.notificationService
      .triggerSmsNotification({
        message,
        phoneNumber: contact.mobilePhone,
        audit: {
          smsAuditType: SmsAuditType.OTP_SMS,
          variant: 'A',
          estateId: estate.estateId,
        },
        vitecContactOptions: {
          contactId: contact.contactId,
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
        },
      })
      .catch((e) =>
        this.logger.error(
          `Error sending OTP contact SMS notification for estate: ${estate.estateId}, to contact: ${contact.contactId}, phone: ${contact.mobilePhone}, error: ${e}`,
        ),
      );
  }

  private async sendBrokerEmail(
    estate: Estate,
    broker: {
      image: { large: string; medium: string; small: string };
      slug: string;
      name: string;
      email: string;
      title: string;
      mobilePhone: string;
      workPhone: string;
    },
    mailMessage: string,
  ) {
    this.logger.log(`Sending OTP broker email notification for estate: ${estate.estateId}, broker: ${broker.email}`);
    await this.mailService
      .sendMail({
        to: broker.email,
        subject: `Overtakelsesprotokoll - ${estate.assignmentNum} ${estate.address.streetAdress}`,
        html: mailMessage,
        mailAuditType: MailAuditType.OTP,
      })
      .catch((e) =>
        this.logger.error(
          `Error sending OTP broker email notification for estate: ${estate.estateId}, broker: ${broker.name}, email: ${broker.email}, error: ${e}`,
        ),
      );
  }

  private async sendBrokerSms(broker: EstateBrokerIdWithRolesNested, message: string, estate: Estate) {
    await this.notificationService.delayByLastSentSms(broker.employee.mobilePhone, SmsAuditType.BROKER_OTP_SMS);

    await this.notificationService
      .triggerSmsNotification({
        message,
        phoneNumber: broker.employee.mobilePhone,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.BROKER_OTP_SMS,
          variant: 'A',
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: '',
        },
      })
      .catch((e) =>
        this.logger.error(
          `Error sending OTP broker SMS notification for estate: ${estate.estateId}, broker: ${broker.employee.name}, phone: ${broker.employee.mobilePhone}, error: ${e}`,
        ),
      );
  }

  @Cron('55 7 * * *', {
    name: OvertakeProtocolNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering overtake-protocol notifications');
    await this.trigger();
    this.logger.log(`Scheduled overtake-protocol notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const otpTomorrowEstates = await this.estateModel.find({
      takeOverDate: {
        $gte: add(sub(startOfDay(new Date()), { hours: 2 }), { days: 1 }),
        $lt: add(sub(endOfDay(new Date()), { hours: 2 }), { days: 1 }),
      },
    });

    // filters for specific flags enabled for specific assignmentTypeGroups
    const filteredEstates = otpTomorrowEstates.filter((estate) => this.shouldCreateOtpForEstate(estate));

    this.logger.debug(
      `Estates having OTP tomorrow: ${JSON.stringify(filteredEstates.map((estate) => estate.estateId))}`,
    );

    for (const estate of filteredEstates) {
      try {
        await this.sendNotificationsIfFeatureFlagEnabled(estate);
      } catch (e) {
        this.logger.error(`Error sending OTP notifications to estate: ${estate.estateId}, error ${e}`);
      }
    }
  }

  async triggerForEstate(estateId: string): Promise<void> {
    this.logger.log(`Triggering overtake-protocol notification for estate: ${estateId}`);

    const estate = await this.estateModel.findOne({ estateId });

    if (!estate) {
      this.logger.error(`Estate not found with estateId: ${estateId}`);
      throw new Error(`Estate not found with estateId: ${estateId}`);
    }

    if (!this.shouldCreateOtpForEstate(estate)) {
      this.logger.warn(`OTP should not be created for estate: ${estateId} based on assignment type and checklist`);
      return;
    }

    try {
      await this.sendNotificationsIfFeatureFlagEnabled(estate);
      this.logger.log(`Successfully triggered overtake-protocol notification for estate: ${estateId}`);
    } catch (e) {
      this.logger.error(`Error sending OTP notifications to estate: ${estateId}, error ${e}`);
      throw e;
    }
  }
}
