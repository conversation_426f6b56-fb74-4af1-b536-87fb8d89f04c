import { SchedulerRegistry } from '@nestjs/schedule';

import { Model } from 'mongoose';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService from '../../authorization/authorization.service';
import { EiendomsverdiService } from '../../eiendomsverdi/eiendomsverdi.service';
import { MailService } from '../../mail/mail.service';
import { mocAppConfigServiceFactory } from '../../mock-classes/mock-app-config-service';
import { mockAuthorizationServiceFactory } from '../../mock-classes/mock-authorization-service';
import { mockEstateServiceFactory } from '../../mock-classes/mock-estate-service';
import { mockEvServiceFactory } from '../../mock-classes/mock-ev-service';
import { mockMailService } from '../../mock-classes/mock-mail-services';
import { mockModelFactory } from '../../mock-classes/mock-model';
import { mockMongoServiceFactory } from '../../mock-classes/mock-mongo-service';
import { mockNotificationServiceFactory } from '../../mock-classes/mock-notification-service';
import { mockS3ServiceFactory } from '../../mock-classes/mock-s3-service';
import { mockSchedulerRegistryFactory } from '../../mock-classes/mock-scheduler-registry';
import { mockSyncServiceFactory } from '../../mock-classes/mock-sync-service';
import { mockUserNotifierServiceFactory } from '../../mock-classes/mock-user-notifier-service';
import { mockUserServiceFactory } from '../../mock-classes/mock-user-service';
import { mockVitecServiceFactory } from '../../mock-classes/mock-vitec-service';
import { MongoService } from '../../mongo/mongo.service';
import { EstateService } from '../../pg/estate/estate.service';
import { User } from '../../pg/user/user.model';
import { UserService } from '../../pg/user/user.service';
import { S3Service } from '../../s3/s3.service';
import { BuyerDocument } from '../../sync/schema/buyer.schema';
import { ContactDocument } from '../../sync/schema/contact.schema';
import { DepartmentDocument } from '../../sync/schema/department.schema';
import { EmployeeDocument } from '../../sync/schema/employee.schema';
import { BrokerRole, EstateDocument } from '../../sync/schema/estate.schema';
import { ProxyDocument } from '../../sync/schema/proxy.schema';
import { SellerDocument } from '../../sync/schema/seller.schema';
import { SyncService } from '../../sync/sync.service';
import { VitecService } from '../../vitec/vitec.service';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';
import { BeforeArchiveNotificationService, ContactType } from './before-archive-notification.service';

const getMockDependencies = (): {
  mockSchedulerRegistry: SchedulerRegistry;
  mockVitecService: VitecService;
  mockMongoService: MongoService;
  mockSyncService: SyncService;
  mockS3Service: S3Service;
  mockAuthorizationService: AuthorizationService;
  mockUserService: UserService;
  mockNotificationService: NotificationService;
  mockAppConfigService: AppConfigService;
  mockUserNotifierService: UserNotifierService;
  estateModel: Model<EstateDocument>;
  departmentModel: Model<DepartmentDocument>;
  employeeModel: Model<EmployeeDocument>;
  sellerModel: Model<SellerDocument>;
  buyerModel: Model<BuyerDocument>;
  proxyModel: Model<ProxyDocument>;
  contactModel: Model<ContactDocument>;
  estateService: EstateService;
  evService: EiendomsverdiService;
  mockMailService: MailService;
} => ({
  mockSchedulerRegistry: mockSchedulerRegistryFactory(),
  mockVitecService: mockVitecServiceFactory(),
  mockMongoService: mockMongoServiceFactory(),
  mockSyncService: mockSyncServiceFactory(),
  mockS3Service: mockS3ServiceFactory(),
  mockAuthorizationService: mockAuthorizationServiceFactory(),
  mockUserService: mockUserServiceFactory(),
  mockNotificationService: mockNotificationServiceFactory(),
  mockAppConfigService: mocAppConfigServiceFactory(),
  mockUserNotifierService: mockUserNotifierServiceFactory(),
  estateModel: mockModelFactory<EstateDocument>(),
  departmentModel: mockModelFactory<DepartmentDocument>(),
  employeeModel: mockModelFactory<EmployeeDocument>(),
  sellerModel: mockModelFactory<SellerDocument>(),
  buyerModel: mockModelFactory<BuyerDocument>(),
  proxyModel: mockModelFactory<ProxyDocument>(),
  contactModel: mockModelFactory<ContactDocument>(),
  estateService: mockEstateServiceFactory(),
  evService: mockEvServiceFactory(),
  mockMailService: mockMailService(),
});

let deps = getMockDependencies();

describe('BeforeArchiveNotificationService', () => {
  beforeEach(async () => {
    deps = getMockDependencies();

    const mockAppUser = {
      id: 'mockAppUserId',
      phoneNumber: '+4791111111',
    } as User;

    (deps.mockAppConfigService.getAppUrl as jest.Mock).mockReturnValue('');
    (deps.mockAuthorizationService.isFeatureEnabledForEstate as jest.Mock).mockReturnValue(true);
    (deps.mockNotificationService.triggerFeedNotification as jest.Mock).mockResolvedValue('');
    (deps.mockNotificationService.triggerPushNotification as jest.Mock).mockResolvedValue('');
    (deps.mockNotificationService.triggerSmsNotification as jest.Mock).mockResolvedValue('');
    (deps.mockNotificationService.delayByLastSentSms as jest.Mock).mockResolvedValue('');
    (deps.mockMailService.sendMail as jest.Mock).mockResolvedValue('');
    (deps.mockUserService.findByPhoneNumber as jest.Mock).mockResolvedValue(mockAppUser);
    (deps.mockUserService.findAllByPhoneNumbers as jest.Mock).mockResolvedValue([mockAppUser]);
  });

  describe('findRegisteredBuyersAndSellers', () => {
    it('should only return registered buyers and sellers', async () => {
      const beforeArchiveNotificationService = new BeforeArchiveNotificationService(
        deps.mockAuthorizationService,
        deps.mockMailService,
        deps.mockUserService,
        deps.estateModel,
        deps.buyerModel,
        deps.sellerModel,
      );

      const inputBuyers = [
        { contactId: 'aaa', mobilePhone: '91111111' } as BuyerDocument,
        { contactId: 'bbb', mobilePhone: '92222222' } as BuyerDocument,
        { contactId: 'ccc', mobilePhone: '+4791111111' } as BuyerDocument,
      ];

      const inputSellers = [
        { contactId: 'aaa', mobilePhone: '91111111' } as SellerDocument,
        { contactId: 'bbb', mobilePhone: '92222222' } as SellerDocument,
        { contactId: 'ccc', mobilePhone: '+4791111111' } as SellerDocument,
      ];

      const { buyers, sellers } = await beforeArchiveNotificationService.findRegisteredBuyersAndSellers(
        inputBuyers,
        inputSellers,
      );

      expect(buyers).toEqual([inputBuyers[0], inputBuyers[2]]);
      expect(sellers).toEqual([inputSellers[0], inputSellers[2]]);
    });
  });

  describe('sendNotification', () => {
    it('should not send when contact type is DO_NOT_SEND (1)', async () => {
      const estate = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 2,
        address: {
          streetAdress: 'mockStreetAddress',
        },
        brokersIdWithRoles: [
          {
            brokerRole: BrokerRole.MAIN_BROKER,
            employee: {
              mobilePhone: 'mockBrokerMobilePhoneNumber',
              email: '<EMAIL>',
            },
          },
        ],
      } as EstateDocument;

      const buyer: BuyerDocument[] = [
        {
          id: 'mockBuyerId',
          mobilePhone: 'mockMobilePhone',
          contactId: 'mockContactId',
          email: '<EMAIL>',
          contactType: ContactType.COMPANY,
        } as BuyerDocument,
      ];

      const seller: SellerDocument[] = [
        {
          id: 'mockSellerId',
          mobilePhone: 'mockMobilePhone',
          contactId: 'mockContactId',
          email: '<EMAIL>',
          contactType: ContactType.COMPANY,
        } as SellerDocument,
      ];

      (deps.mockMailService.sendMail as jest.Mock).mockResolvedValue('');

      const beforeArchiveNotificationService = new BeforeArchiveNotificationService(
        deps.mockAuthorizationService,
        deps.mockMailService,
        deps.mockUserService,
        deps.estateModel,
        deps.buyerModel,
        deps.sellerModel,
      );

      await beforeArchiveNotificationService.sendNotification({
        estate: estate,
        buyers: buyer,
        sellers: seller,
      });

      expect(deps.mockMailService.sendMail).toHaveBeenCalledTimes(0);
    });
    it('should send when contact type is SEND (0)', async () => {
      const estate = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 2,
        address: {
          streetAdress: 'mockStreetAddress',
        },
        brokersIdWithRoles: [
          {
            brokerRole: BrokerRole.MAIN_BROKER,
            employee: {
              mobilePhone: 'mockBrokerMobilePhoneNumber',
              email: '<EMAIL>',
            },
          },
        ],
      } as EstateDocument;

      const buyer: BuyerDocument[] = [
        {
          id: 'mockBuyerId',
          mobilePhone: 'mockMobilePhone',
          contactId: 'mockContactId',
          email: '<EMAIL>',
          contactType: ContactType.PERSON,
        } as BuyerDocument,
      ];

      const seller: SellerDocument[] = [
        {
          id: 'mockSellerId',
          mobilePhone: 'mockMobilePhone',
          contactId: 'mockContactId',
          email: '<EMAIL>',
          contactType: ContactType.PERSON,
        } as SellerDocument,
      ];

      (deps.mockMailService.sendMail as jest.Mock).mockResolvedValue('');

      const beforeArchiveNotificationService = new BeforeArchiveNotificationService(
        deps.mockAuthorizationService,
        deps.mockMailService,
        deps.mockUserService,
        deps.estateModel,
        deps.buyerModel,
        deps.sellerModel,
      );

      await beforeArchiveNotificationService.sendNotification({
        estate: estate,
        buyers: buyer,
        sellers: seller,
      });

      expect(deps.mockMailService.sendMail).toHaveBeenCalledTimes(2);
    });
  });
});
