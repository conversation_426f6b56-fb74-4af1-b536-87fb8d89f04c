import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import axios from 'axios';
import { sub } from 'date-fns';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { UserService } from '../../pg/user/user.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { Seller, SellerDocument } from '../../sync/schema/seller.schema';
import { sign } from '../../utils/jwt.utils';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';

type WealthManagementSummaryEntry = {
  finnNo: FinnNoStatistics | null;
  nordvikboligNo: NordvikboligNoStatistics | null;
};

type FinnNoStatistics = {
  adViews: number;
  receivedAdByEmail: number;
  savedAd: number;
};

type NordvikboligNoStatistics = {
  totalNumberOfPageViews: number;
  dailyPageViews: number[];
  numberOfSalesProspectDownloads: number;
  numberOfDirectPageViews: number;
  numberOfPaidFinnNoPageViews: number;
  numberOfFinnNoPageViews: number;
  numberOfFacebookPageViews: number;
  numberOfGooglePageViews: number;
};

@Injectable()
export class StatisticsNotification {
  private static readonly FINN_NOTIFICATION_MESSAGE = 'Finn-annonsen din har [views] besøk';

  private static readonly NORDVIKBOLIG_NOTIFICATION_MESSAGE = 'Annonsen din har [views] nye visninger';

  private static readonly ICON_NAME = 'officeRevenue';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/wealth';

  private readonly logger = new ConsoleLogger(StatisticsNotification.name);

  constructor(
    private readonly userService: UserService,
    private readonly appConfigService: AppConfigService,
    private readonly notificationService: NotificationService,
    private authorizationService: AuthorizationService,
    private readonly userNotifierService: UserNotifierService,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('30 19 * * *', {
    name: StatisticsNotification.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled value distribution notifications');
    await this.trigger();
    this.logger.log(`Scheduled value distribution notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const estates = await this.estateModel.find({
      status: VitecEstateStatus.FOR_SALE,
      ads: {
        $elemMatch: {
          publishStart: { $lt: sub(new Date(), { hours: 2 }) },
          publishEnd: { $gt: sub(new Date(), { hours: 2 }) },
          $or: [{ channel: 1 }, { channel: 32 }],
        },
      },
    });

    this.logger.log(`Estates in status 2, with active ad: ${JSON.stringify(estates.map((e) => e.estateId))}`);

    for (const estate of estates) {
      const sellers = await this.sellerModel.find({ contactId: { $in: estate.sellers.map((s) => s.contactId) } });
      for (const seller of sellers) {
        const appUser = await this.userService.findByPhoneNumber(seller.mobilePhone);
        if (!appUser) {
          continue;
        }

        const token = sign(
          { userID: appUser.id, phoneNumber: appUser.phoneNumber, role: 'user' },
          appUser.passwordCode,
        );
        const statisticsUrl = urljoin(this.appConfigService.getBackendUrl(), `/estates/${estate.estateId}/statistics`);
        const response = await axios.get<WealthManagementSummaryEntry>(statisticsUrl, {
          headers: { authorization: `Bearer ${token}` },
          validateStatus: () => true,
        });

        if (response.status !== 200) {
          this.logger.error(`Received error from backend: ${response.status}, ${response.data}`);
          continue;
        }

        // FINN
        this.logger.log(`Finn view for user ${appUser.id}: ${JSON.stringify(response.data.finnNo?.adViews)}`);
        if (response.data.finnNo?.adViews) {
          const message = StatisticsNotification.FINN_NOTIFICATION_MESSAGE.replace(
            '[views]',
            response.data.finnNo.adViews.toLocaleString().replace(/,/g, ' '),
          );

          await this.userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
            feedFeatureFlag: FeatureFlag.SendFinnStatisticsFeed,
            iconName: StatisticsNotification.ICON_NAME,
            isFeatureEnabled: (featureFlag) =>
              this.authorizationService.isFeatureEnabledForEstate(featureFlag, estate.estateId),
            message,
            pushFeatureFlag: FeatureFlag.SendFinnStatisticsPush,
            redirectUrl: StatisticsNotification.NOTIFICATION_REDIRECT_URL,
            userId: appUser.id,
          });
        }

        // NORDVIKBOLIG
        this.logger.log(
          `Nordvikbolig view for user ${appUser.id}: ${JSON.stringify(response.data.nordvikboligNo?.dailyPageViews)}`,
        );
        if (response.data.nordvikboligNo?.dailyPageViews.length) {
          const message = StatisticsNotification.NORDVIKBOLIG_NOTIFICATION_MESSAGE.replace(
            '[views]',
            response.data.nordvikboligNo.dailyPageViews[response.data.nordvikboligNo.dailyPageViews.length - 1]
              .toLocaleString()
              .replace(/,/g, ' '),
          );

          await this.userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
            feedFeatureFlag: FeatureFlag.SendNordvikBoligStatisticsFeed,
            iconName: StatisticsNotification.ICON_NAME,
            isFeatureEnabled: (featureFlag) =>
              this.authorizationService.isFeatureEnabledForEstate(featureFlag, estate.estateId),
            message,
            pushFeatureFlag: FeatureFlag.SendNordvikBoligStatisticsPush,
            redirectUrl: StatisticsNotification.NOTIFICATION_REDIRECT_URL,
            userId: appUser.id,
          });
        }
      }
    }
  }
}
