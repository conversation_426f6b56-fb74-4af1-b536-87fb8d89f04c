import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { endOfDay, startOfDay, subDays } from 'date-fns';
import { ParseError, parsePhoneNumberWithError } from 'libphonenumber-js';
import { Model } from 'mongoose';
import { Op } from 'sequelize';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { SettlementBuyerParticipant } from '../../pg/settlement-buyer/settlement-buyer.model';
import { SettlementBuyerService } from '../../pg/settlement-buyer/settlement-buyer.service';
import { SettlementSellerParticipant } from '../../pg/settlement-seller/settlement-seller.model';
import { SettlementSellerService } from '../../pg/settlement-seller/settlement-seller.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { BrokerRole, Estate, EstateBrokerIdWithRolesNested, EstateDocument } from '../../sync/schema/estate.schema';
import { NotificationService } from '../notification.service';

const smsMessage = `Hei,\n\nDette er en påminnelse om at oppgjørsskjema for [address] må signeres og fullføres så raskt som mulig.\n\nMed vennlig hilsen\n\n[mainBroker]\n\nNB! Dette er en automatisk generert SMS`;

@Injectable()
export class SettlementSigningSMSReminderNotificationService {
  private readonly logger = new ConsoleLogger(SettlementSigningSMSReminderNotificationService.name);

  private readonly idfyClient = new IdfyClient(
    this.appConfigService.getIdfyId(),
    this.appConfigService.getIdfySecret(),
    ['document_read', 'document_file', 'document_write'],
  );

  constructor(
    private notificationService: NotificationService,
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly settlementBuyerService: SettlementBuyerService,
    private readonly settlementSellerService: SettlementSellerService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  private isSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementSigningReminderSms, estateId);

  @Cron('55 18 * * *', {
    name: SettlementSigningSMSReminderNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering sms signing reminder');
    await this.trigger();
    this.logger.log(`Finished sms signing reminder`);
  }

  private async getDocumentDetails(idfyDocumentId: string): Promise<{
    documentFound: boolean;
    participantIds?: string[];
  }> {
    if (!idfyDocumentId) {
      return {
        documentFound: false,
        participantIds: null,
      };
    }

    try {
      const status = await this.idfyClient.signature.getDocumentStatus(idfyDocumentId);

      if (status.documentStatus === 'canceled') {
        this.logger.debug('document was cancelled ' + idfyDocumentId);
        return {
          documentFound: true,
          participantIds: null,
        };
      }
    } catch (e) {
      this.logger.error('idfy get document status error with idfyDocumentId ' + idfyDocumentId);
    }

    try {
      const signers = await this.idfyClient.signature.listSigners(idfyDocumentId);
      if (!signers || signers.length === 0) {
        // idfy returns an empty array if the document is not found
        this.logger.debug('no document found with idfyDocumentId ' + idfyDocumentId);
        return {
          documentFound: false,
          participantIds: null,
        };
      }

      return {
        documentFound: true,
        participantIds: signers.filter((s) => !s.documentSignature).map((s) => s.externalSignerId),
      };
    } catch (e) {
      this.logger.error('idfy list signers error with idfyDocumentId ' + idfyDocumentId);
      return {
        documentFound: false,
        participantIds: null,
      };
    }
  }

  async sendNotification({
    address,
    broker,
    unsignedParticipants,
    estate,
  }: {
    unsignedParticipants: {
      phoneNumber: string;
      name: string;
    }[];
    address: string;
    broker: EstateBrokerIdWithRolesNested;
    estate: Estate;
  }) {
    for (const signer of unsignedParticipants) {
      this.logger.log(`Sending sms reminder for unsigned Settlement document to ${signer.phoneNumber}.`);

      const message = smsMessage.replace('[mainBroker]', broker.employee.name).replace('[address]', address);

      if (!signer.phoneNumber) {
        this.logger.warn(
          `Signer ${signer.name} has no phone number set for sending SMS about settlement signing reminder`,
        );
        continue;
      }

      try {
        const parsedSignerPhoneNumber = parsePhoneNumberWithError(signer.phoneNumber, 'NO').number;
        const contact = [...estate.sellers, ...estate.buyers, ...estate.proxies].find(
          (s) => parsePhoneNumberWithError(s.mobilePhone, 'NO').number === parsedSignerPhoneNumber,
        );
        await this.notificationService.triggerSmsNotification({
          message,
          phoneNumber: signer.phoneNumber,
          audit: { smsAuditType: SmsAuditType.SETTLEMENT_SIGNING_REMINDER, estateId: estate.estateId },
          vitecContactOptions: {
            departmentId: estate.departmentId,
            fromEmployeeId: undefined,
            contactId: contact.contactId,
          },
        });
      } catch (e) {
        this.logger.error(
          `Error sending SMS to ${signer.phoneNumber}, error ${e instanceof ParseError ? e.message : e}`,
        );
      }
    }
  }

  async trigger(): Promise<void> {
    const now = new Date();
    const fiveDaysFromNow: [Date, Date] = [startOfDay(subDays(now, 5)), endOfDay(subDays(now, 5))];
    const tenDaysFromNow: [Date, Date] = [startOfDay(subDays(now, 10)), endOfDay(subDays(now, 10))];

    const options = {
      where: {
        createdAt: {
          [Op.or]: [
            {
              [Op.gte]: fiveDaysFromNow[0],
              [Op.lte]: fiveDaysFromNow[1],
            },
            {
              [Op.gte]: tenDaysFromNow[0],
              [Op.lte]: tenDaysFromNow[1],
            },
          ],
        },
        isNotificationSent: true,
        signingFinished: null,
      },
    };
    const buyers = (await this.settlementBuyerService.getSettlementBuyer(options)).map((s) => ({
      estateVitecId: s.estateVitecId,
      idfyDocumentId: s.idfyDocumentId,
      id: s.id,
      isBuyer: true,
    }));
    this.logger.debug(`Estates with unsigned buyer settlements: ${JSON.stringify(buyers.map((b) => b.estateVitecId))}`);

    const sellers = (await this.settlementSellerService.getSettlementSeller(options)).map((s) => ({
      estateVitecId: s.estateVitecId,
      idfyDocumentId: s.idfyDocumentId,
      id: s.id,
      isBuyer: false,
    }));
    this.logger.debug(
      `Estates with unsigned seller settlements: ${JSON.stringify(sellers.map((s) => s.estateVitecId))}`,
    );
    const settlements = [...buyers, ...sellers];

    for (const settlement of settlements) {
      if (!this.isSmsEnabled(settlement.estateVitecId)) {
        this.logger.warn(
          `Skipping settlement signing reminder SMS notification sending for estate: ${settlement.estateVitecId}. The following feature flag is disabled: ${FeatureFlag.SendSettlementSigningReminderSms}`,
        );
        continue;
      }

      try {
        const estate = await this.estateModel.findOne({ estateId: settlement.estateVitecId });
        if (!estate) {
          this.logger.error('Estate not found by ID ' + settlement.estateVitecId);
          continue;
        }
        const mainBroker = estate.brokersIdWithRoles.find((b) => b.brokerRole === BrokerRole.MAIN_BROKER);
        if (!mainBroker) {
          this.logger.error('Estate does not have a mainBroker ' + settlement.estateVitecId);
          continue;
        }

        const participants: Array<SettlementBuyerParticipant | SettlementSellerParticipant> = settlement.isBuyer
          ? await this.settlementBuyerService.getParticipantsForForm(settlement.id)
          : await this.settlementSellerService.getParticipantsForForm(settlement.id);

        const documentDetails = await this.getDocumentDetails(settlement.idfyDocumentId);

        let unsignedParticipants;
        if (!documentDetails.documentFound) {
          unsignedParticipants = participants.map((p) => ({ phoneNumber: p.phoneNumber, name: p.name }));
        } else {
          unsignedParticipants = participants
            .filter((p) => documentDetails.participantIds?.includes(p.id))
            .map((p) => ({
              phoneNumber: p.phoneNumber,
              name: p.name,
            }));
        }

        if (unsignedParticipants.length === 0) {
          this.logger.warn(`No unsigned participants found for estate ${settlement.estateVitecId}`);
          continue;
        }

        await this.sendNotification({
          broker: mainBroker,
          address: estate.address.streetAdress,
          unsignedParticipants,
          estate,
        });
      } catch (e) {
        this.logger.error(e);
      }
    }
  }
}
