import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { startOfDay, sub } from 'date-fns';
import add from 'date-fns/add';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class ByggstartNotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    'Ønsker du å pusse opp din nye bolig? Vi kan hjelpe deg med stort og smått!';

  private static readonly ICON_NAME = 'icon_paint_roller';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/byggstart';

  private readonly logger = new ConsoleLogger(ByggstartNotificationService.name);

  constructor(
    private readonly userNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: ByggstartNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled byggstart notifications');
    await this.trigger();
    this.logger.log(`Scheduled byggstart notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 7, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesWith7dayContractMeeting = await this.estateModel.find({
      contractMeetingDate: {
        $lt: endDate,
        $gte: startDate,
      },
    });

    this.logger.debug(
      `Estates which had a contract meeting 7 days ago: ${JSON.stringify(
        estatesWith7dayContractMeeting.map((estate) => estate.estateId),
      )}`,
    );

    await this.userNotifierService.notifyEstates({
      estates: estatesWith7dayContractMeeting,
      iconName: ByggstartNotificationService.ICON_NAME,
      message: ByggstartNotificationService.NOTIFICATION_MESSAGE,
      url: ByggstartNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { buyer: true },
      pushFeatureFlag: FeatureFlag.SendByggstartPush,
      feedFeatureFlag: FeatureFlag.SendByggstartFeed,
    });
  }
}
