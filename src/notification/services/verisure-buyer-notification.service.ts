import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class VerisureBuyerNotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    'Beskytt det som ikke kan erstattes, bestill boligalarm for din nye bolig her!';

  private static readonly ICON_NAME = 'icon_finance_bull-horn'; // loudspeaker icon

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/verisure';

  private readonly logger = new ConsoleLogger(VerisureBuyerNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('30 11 * * *', {
    name: VerisureBuyerNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled verisure buyer notifications');
    await this.trigger();
    this.logger.log(`Scheduled verisure buyer notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 7, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesWithRecentTakeOver = await this.estateModel.find({
      takeOverDate: { $lt: endDate, $gte: startDate },
    });

    this.logger.debug(
      `Estates which had a take over recently: ${JSON.stringify(
        estatesWithRecentTakeOver.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesWithRecentTakeOver,
      iconName: VerisureBuyerNotificationService.ICON_NAME,
      message: VerisureBuyerNotificationService.NOTIFICATION_MESSAGE,
      url: VerisureBuyerNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { buyer: true },
      pushFeatureFlag: FeatureFlag.SendVerisureBuyerPush,
      feedFeatureFlag: FeatureFlag.SendVerisureBuyerFeed,
    });
  }
}
