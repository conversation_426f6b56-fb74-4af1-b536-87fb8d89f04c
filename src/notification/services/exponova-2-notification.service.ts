import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay } from 'date-fns';
import { Model } from 'mongoose';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { UserActivityType } from '../../pg/user-activity/user-activity.model';
import { UserActivityService } from '../../pg/user-activity/user-activity.service';
import { UserService } from '../../pg/user/user.service';
import { Buyer, BuyerDocument } from '../../sync/schema/buyer.schema';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class Exponova2NotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'La oss hjelpe deg med å finne ditt drømmeinteriør!';

  private static readonly ICON_NAME = 'icon_bedrooms';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/exponova';

  private readonly logger = new ConsoleLogger(Exponova2NotificationService.name);

  constructor(
    private readonly userService: UserService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly userActivityService: UserActivityService,
    private readonly userNotifierService: UserNotifierService,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: Exponova2NotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled exponova notifications');
    await this.trigger();
    this.logger.log(`Scheduled exponova notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = add(startOfDay(new Date()), { hours: 22, days: 16 });
    const endDate = add(startDate, { days: 1 });
    const otpIn15Days = await this.estateModel.find({
      takeOverDate: {
        $gte: startDate,
        $lt: endDate,
      },
    });

    this.logger.debug(`Estates having OTP in 15 days: ${JSON.stringify(otpIn15Days.map((estate) => estate.estateId))}`);

    for (const estate of otpIn15Days) {
      const buyerIds = estate.buyers.map((s) => s.contactId);
      const buyers = await this.buyerModel.find({ contactId: { $in: buyerIds } });

      const users = await this.userService.findAllByContacts(buyers);
      const prevLead = await this.userActivityService.findPrevLead(
        users.map((u) => u.id),
        UserActivityType.REQUEST_EXPONOVA_SERVICE,
      );
      if (prevLead) {
        continue;
      }

      await this.userNotifierService.notifyContacts({
        contacts: buyers,
        feedFeatureFlag: FeatureFlag.SendExponova2Feed,
        iconName: Exponova2NotificationService.ICON_NAME,
        isFeatureEnabled: (featureFlag) =>
          this.authorizationService.isFeatureEnabledForEstate(featureFlag, estate.estateId),
        message: Exponova2NotificationService.NOTIFICATION_MESSAGE,
        pushFeatureFlag: FeatureFlag.SendExponova2Push,
        redirectUrl: Exponova2NotificationService.NOTIFICATION_REDIRECT_URL,
      });
    }
  }
}
