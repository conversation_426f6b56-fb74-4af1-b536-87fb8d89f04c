import { IdfyClient } from '@idfy/sdk';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';
import { add, startOfDay, sub } from 'date-fns';
import { ParseError, parsePhoneNumberWithError } from 'libphonenumber-js';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { SettlementBuyer, SettlementBuyerParticipant } from '../../pg/settlement-buyer/settlement-buyer.model';
import { SettlementBuyerService } from '../../pg/settlement-buyer/settlement-buyer.service';
import { SettlementSeller, SettlementSellerParticipant } from '../../pg/settlement-seller/settlement-seller.model';
import { SettlementSellerService } from '../../pg/settlement-seller/settlement-seller.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { BrokerRole, Estate, EstateBrokerIdWithRolesNested, EstateDocument } from '../../sync/schema/estate.schema';
import { NotificationService } from '../notification.service';
@Injectable()
export class UnsignedSettlementDocumentNotificationService {
  private readonly logger = new ConsoleLogger(UnsignedSettlementDocumentNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE =
    'Hei. Vi mangler signaturen til [name] for å kunne fullføre oppgjørsskjemaet for [address].\n\nFølg denne linken for å signere og fullføre skjemaet: [link].\n\nMed vennlig hilsen,\nNordvik\n\nNB! Dette er en automatisk generert SMS';

  private static readonly NOTIFICATION_MESSAGE_HTML_TEMPLATE =
    'Hei. Vi mangler signaturen til [name] for å kunne fullføre oppgjørsskjemaet for [address].<br><br>Følg denne linken for å signere og fullføre skjemaet: <a href="[link]">[link]</a>.';

  private readonly idfyClient = new IdfyClient(
    this.appConfigService.getIdfyId(),
    this.appConfigService.getIdfySecret(),
    ['document_read', 'document_file', 'document_write'],
  );

  constructor(
    private readonly settlementSellerService: SettlementSellerService,
    private readonly settlementBuyerService: SettlementBuyerService,
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly mailService: MailService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  sendNotification = async (
    signers: {
      link: string;
      phoneNumber: string;
      name: string;
    }[],
    estate: Estate,
    broker: EstateBrokerIdWithRolesNested,
  ): Promise<void> => {
    for (const signer of signers) {
      this.logger.log(
        `Sending unsigned Settlement documenet notification to ${signer.phoneNumber}, ${broker.employee.email} and ${broker.employee.mobilePhone}`,
      );

      const url = await axios.post<{ shortUrl: string }>(
        urljoin(this.appConfigService.getBackendUrl(), '/url'),
        { url: signer.link },
        { headers: { 'x-api-key': this.appConfigService.getBackendApiKey() } },
      );
      const message = UnsignedSettlementDocumentNotificationService.NOTIFICATION_MESSAGE.replace(
        '[link]',
        urljoin(this.appConfigService.getBackendUrl(), '/url', url.data.shortUrl),
      )
        .replace('[name]', signer.name)
        .replace('[address]', estate.address.streetAdress);
      const messageHtml = UnsignedSettlementDocumentNotificationService.NOTIFICATION_MESSAGE_HTML_TEMPLATE.replace(
        '[link]',
        urljoin(this.appConfigService.getBackendUrl(), '/url', url.data.shortUrl),
      )
        .replace('[name]', signer.name)
        .replace('[address]', estate.address.streetAdress);

      // Broker notifications
      // if (broker.employee.mobilePhone) {
      //   try {
      //     await this.notificationService.triggerSmsNotification({
      //       message,
      //       phoneNumber: broker.employee.mobilePhone,
      //       audit: { smsAuditType: SmsAuditType.SETTLEMENT_SIGNING_REMINDER, estateId: estate.estateId },
      //       vitecContactOptions: {
      //         departmentId: estate.departmentId,
      //         fromEmployeeId: broker.employeeId,
      //         contactId: '',
      //       },
      //     });
      //   } catch (e) {
      //     this.logger.error(`Error sending broker SMS to ${broker.employee.mobilePhone}, error ${e}`);
      //   }
      // } else {
      //   this.logger.warn(
      //     `SMS to broker about settlement signing ${estate.address} reminder was not sent because no phone number was found.`,
      //   );
      // }
      // try {
      //   await this.mailService.sendMail({
      //     to: broker.employee.email,
      //     html: messageHtml,
      //     subject: 'Mangler signatur fra Oppgjørsskjema',
      //     mailAuditType: MailAuditType.SETTLEMENT,
      //   });
      // } catch (e) {
      //   this.logger.error(`Error sending email to ${broker.employee.email}, error ${e}`);
      // }

      if (!signer.phoneNumber) {
        this.logger.warn(
          `Signer ${signer.name} has no phone number set for sending SMS about settlement signing reminder`,
        );
        continue;
      }

      try {
        const parsedSignerPhoneNumber = parsePhoneNumberWithError(signer.phoneNumber, 'NO').number;
        const contact = [...estate.sellers, ...estate.buyers, ...estate.proxies].find(
          (s) => parsePhoneNumberWithError(s.mobilePhone, 'NO').number === parsedSignerPhoneNumber,
        );
        await this.notificationService.triggerSmsNotification({
          message,
          phoneNumber: signer.phoneNumber,
          audit: { smsAuditType: SmsAuditType.SETTLEMENT_SIGNING_REMINDER, estateId: estate.estateId },
          vitecContactOptions: {
            departmentId: estate.departmentId,
            fromEmployeeId: undefined,
            contactId: contact.contactId,
          },
        });
      } catch (e) {
        this.logger.error(
          `Error sending SMS to ${signer.phoneNumber}, error ${e instanceof ParseError ? e.message : e}`,
        );
      }
    }
  };

  @Cron(CronExpression.MONDAY_TO_FRIDAY_AT_9AM, {
    name: UnsignedSettlementDocumentNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering unsigned settlement form notifications');
    await this.trigger();
    this.logger.log(`Scheduled unsigned settlement form notifications are triggered`);
  }

  async trigger(): Promise<void> {
    if (!this.authorizationService.isFeatureEnabled(FeatureFlag.SendUnsignedSettlementDocumentNotification)) {
      this.logger.warn(
        `No notifications of any kind will be sent out, because the corresponding feature flag is disabled: ${FeatureFlag.SendUnsignedSettlementDocumentNotification}`,
      );
      return;
    }
    const startDate = sub(startOfDay(new Date()), { days: 4 });
    const endDate = add(startDate, { days: 1 });
    const unfinishedSettlementSellerDocuments4DaysAgo =
      await this.settlementSellerService.findAllUnfinishedBetweenSignStartDates(startDate, endDate);
    const unfinishedSettlementBuyerDocuments4DaysAgo =
      await this.settlementBuyerService.findAllUnfinishedBetweenSignStartDates(startDate, endDate);

    const allUnfinishedSettlements = [
      ...unfinishedSettlementSellerDocuments4DaysAgo,
      ...unfinishedSettlementBuyerDocuments4DaysAgo,
    ];
    this.logger.debug(
      `Estates that had unfinalized settlements 4 days ago: ${JSON.stringify(
        allUnfinishedSettlements.map((estate) => estate.estateVitecId),
      )}`,
    );
    await Promise.all([
      this.sendNotificationsToUnsignedParticipants(unfinishedSettlementBuyerDocuments4DaysAgo, true),
      this.sendNotificationsToUnsignedParticipants(unfinishedSettlementSellerDocuments4DaysAgo, false),
    ]);
  }

  private async sendNotificationsToUnsignedParticipants(
    unfinishedSettlementDocuments3DaysAgo: Array<SettlementBuyer | SettlementSeller>,
    isBuyer: boolean,
  ) {
    for (const settlement of unfinishedSettlementDocuments3DaysAgo) {
      try {
        const estate = await this.estateModel.findOne({ estateId: settlement.estateVitecId });
        const mainBroker = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0];
        const signers = await this.idfyClient.signature.listSigners(settlement.idfyDocumentId);
        const unsignedSigners = signers.filter((s) => !s.documentSignature).map((s) => s.externalSignerId);
        const participants: Array<SettlementBuyerParticipant | SettlementSellerParticipant> = isBuyer
          ? await this.settlementBuyerService.getParticipantsForForm(settlement.id)
          : await this.settlementSellerService.getParticipantsForForm(settlement.id);
        const unsignedParticipants = participants
          .filter((p) => unsignedSigners.includes(p.id))
          .map((p) => ({
            link: signers.find((s) => s.externalSignerId === p.id).url,
            phoneNumber: p.phoneNumber,
            name: p.name,
          }));
        await this.sendNotification(unsignedParticipants, estate, mainBroker);
      } catch (e) {
        this.logger.error(e);
      }
    }
  }
}
