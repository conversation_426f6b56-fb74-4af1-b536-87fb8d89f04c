import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { sub } from 'date-fns';
import { parsePhoneNumber } from 'libphonenumber-js';
import { isNil } from 'lodash';
import { Model } from 'mongoose';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { SmsAuditService } from '../../pg/sms-audit/sms-audit.service';
import { UserService } from '../../pg/user/user.service';
import { Buyer, BuyerDocument } from '../../sync/schema/buyer.schema';
import { BrokerRole, Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { EstateAssignmentTypeGroup, VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';

@Injectable()
export class AfterSaleNotificationService {
  private readonly logger = new ConsoleLogger(AfterSaleNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE =
    'Hei [firstName],\n\nSom boligkjøper i Nordvik har du tilgang til blant annet dokumenter og signeringslenker i Nordvik-appen. Ta i bruk appen her: https://nordvik.app\n\nMed vennlig hilsen\nNordvik';

  constructor(
    private readonly userService: UserService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly smsAuditService: SmsAuditService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
  ) {}

  private async sendNotification(estate: EstateDocument, contacts: BuyerDocument[]): Promise<void> {
    const isSendableAdoptionSMS =
      !isNil(estate.assignmentTypeGroup) && estate.assignmentTypeGroup === EstateAssignmentTypeGroup.SALE;
    if (!isSendableAdoptionSMS) {
      this.logger.warn(
        `EstateAssignmentTypeGroup was ${estate.assignmentTypeGroup}, skipping adoption SMS sending for estate ${estate.estateId}`,
      );
      return;
    }

    const broker = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0];
    if (!broker) {
      this.logger.warn(`No primary broker for estate ${estate.estateId}`);
      return;
    }

    const appUsers = await this.userService.findAllByContacts(contacts);
    if (appUsers.length) {
      // user already registered
      return;
    }

    const { messageBase, variant } = { messageBase: AfterSaleNotificationService.NOTIFICATION_MESSAGE, variant: 'B' };

    for (const contact of contacts) {
      if (!contact.mobilePhone) {
        this.logger.warn(`Contact ${contact.contactId} has no phone number set`);
        continue;
      }

      const phoneNumber = parsePhoneNumber(contact.mobilePhone || '', 'NO');
      if (!phoneNumber || !phoneNumber.isValid()) {
        this.logger.warn(`Contact ${contact.contactId} has an invalid phone number set: ${contact.mobilePhone}`);
        continue;
      }

      const existingSms = await this.smsAuditService.findByTypeToPhoneNumber(
        phoneNumber.number,
        SmsAuditType.AFTER_SALE_SMS,
        estate.estateId,
      );

      if (existingSms) {
        // sms already sent
        continue;
      }

      let message = messageBase
        .replace('[brokerName]', broker.employee.name)
        .replace('[streetAddress]', estate.address.streetAdress);

      if (contact.contactType === 1 || (!contact.firstName && !contact.lastName)) {
        message = message.replace('[firstName]', '');
      } else {
        message = message.replace('[firstName]', contact.firstName || contact.lastName);
      }

      this.logger.log(`Sending after sale notification (SMS) to ${phoneNumber.number}`);

      if (!this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.AfterSaleSms, estate.estateId)) {
        this.logger.warn(
          `SMS was not sent because the corresponding feature flag is disabled: ${FeatureFlag.AfterSaleSms}`,
        );
        return;
      }

      try {
        await this.notificationService.triggerSmsNotification({
          message,
          phoneNumber: phoneNumber.number,
          audit: {
            smsAuditType: SmsAuditType.AFTER_SALE_SMS,
            estateId: estate.estateId,
            variant,
          },
          vitecContactOptions: {
            contactId: contact.contactId,
            departmentId: estate.departmentId,
          },
        });
      } catch (e) {
        this.logger.error(`Error sending sms ${e}`);
      }
    }
  }

  private async getEstateContacts(estate: EstateDocument): Promise<BuyerDocument[]> {
    const buyerIds = estate.buyers.map((s) => s.contactId);
    const buyers = await this.buyerModel.find({ contactId: { $in: buyerIds } });

    return buyers;
  }

  // on weekends
  @Cron('24,54 09-20 * * 6,0', {
    name: AfterSaleNotificationService.name + '2',
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering after sale notifications');
    await this.trigger();
    this.logger.log(`Scheduled after sale notifications are triggered`);
  }

  // on weekdays
  @Cron('23,53 08-10,19-21 * * 1-5', {
    name: AfterSaleNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger2() {
    this.logger.log('Triggering after sale notifications');
    await this.trigger();
    this.logger.log(`Scheduled after sale notifications are triggered`);
  }
  async trigger(): Promise<void> {
    const startDate = sub(new Date(), { days: 2 });
    const endDate = new Date();

    const recentlySignedEstates = await this.estateModel.find({
      status: VitecEstateStatus.OVERSOLD,
      statusChanges: {
        $elemMatch: {
          from: VitecEstateStatus.FOR_SALE,
          to: VitecEstateStatus.OVERSOLD,
          date: {
            $gte: startDate,
            $lt: endDate,
          },
        },
      },
      estateTypeId: {
        $in: ['1', '2', '3', '4', '5', '6', '7'],
      },
    });

    this.logger.debug(
      `Recently sold estates: ${JSON.stringify(recentlySignedEstates.map((estate) => estate.estateId))}`,
    );
    for (const estate of recentlySignedEstates) {
      const contacts = await this.getEstateContacts(estate);
      await this.sendNotification(estate, contacts);
    }
  }
}
