import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import axios from 'axios';
import { CronJob } from 'cron';
import { difference, isNumber } from 'lodash';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { PriceGuessingService } from '../../pg/price-guessing-estates/price-guessing-estates.service';
import { UserNotificationGroups } from '../../pg/user/user.model';
import { UserService } from '../../pg/user/user.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { VitecService } from '../../vitec/vitec.service';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class DailyPriceGuessingGameEstateNotificationService {
  private readonly logger = new ConsoleLogger(DailyPriceGuessingGameEstateNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE = 'Dagens boligquiz er klar. Lykke til!';

  private static readonly ICON_NAME = 'icon_interfaces_heart';

  constructor(
    readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly userService: UserService,
    private readonly priceGuessingService: PriceGuessingService,
    private readonly userNotificationService: UserNotifierService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly vitecService: VitecService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {
    const triggerCronExpressionForDaily = `${appConfigService.getPriceGuessingEstateMinuteOfDailyQuiz()} ${appConfigService.getPriceGuessingEstateHourOfDailyQuiz()} * * *`;

    const job = new CronJob(
      triggerCronExpressionForDaily,
      async () => {
        this.logger.log('Triggering DailyPriceGuessingGameEstateNotificationService estate selection');
        await this.triggerEstateSelectionForDaily();
        this.logger.log('Scheduled DailyPriceGuessingGameEstateNotificationService estate selection are triggered');
      },
      () => {
        this.logger.debug('DailyPriceGuessingGameEstateNotificationService job stopped');
      },
      false,
      'Europe/Oslo',
    );

    this.schedulerRegistry.addCronJob(DailyPriceGuessingGameEstateNotificationService.name + '_estateSelection', job);
    job.start();
  }

  @Cron(CronExpression.EVERY_DAY_AT_6PM, {
    name: DailyPriceGuessingGameEstateNotificationService.name + '_notification',
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering DailyPriceGuessingGameEstateNotificationService notification');
    await this.triggerNotification();
    this.logger.log('Scheduled DailyPriceGuessingGameEstateNotificationService notifications are triggered');
  }

  private isPriceGuessingNotificationEnabled = (): boolean =>
    this.authorizationService.isFeatureEnabled(FeatureFlag.SendPriceGuessingDailyPush) ||
    this.authorizationService.isFeatureEnabled(FeatureFlag.SendPriceGuessingDailyFeed);

  async triggerEstateSelectionForDaily(): Promise<void> {
    const selectingAmount = this.appConfigService.getPriceGuessingEstatesPerDay();

    this.logger.log('Daily estate selection for price guessing is triggered');
    const estatesAlreadySelectedForToday = await this.priceGuessingService.findForToday();
    if (estatesAlreadySelectedForToday.length === selectingAmount) {
      this.logger.error(
        `Estates are already selected for today in the configured amount, pgId in PriceGuessingEstates: ${estatesAlreadySelectedForToday.map(
          (e) => e.id,
        )}`,
      );
      return;
    }

    const previouslySelectedEstates = await this.priceGuessingService.findAll();

    const minPrice = 1000000;
    const maxPrice = 20000000;
    const possibleDailyMongoEstates = await this.estateModel.find({
      status: VitecEstateStatus.FOR_SALE,
      assignmentTypeGroup: 1,
      estateBaseType: 1,
      'estatePrice.totalPrice': { $gte: minPrice, $lte: maxPrice },
      estateId: { $nin: previouslySelectedEstates.map((e) => e.estateVitecId) },
    });

    const foundEstatesWithImages: EstateDocument[] = [];

    const randomizedEstates = possibleDailyMongoEstates.sort(() => Math.random() - 0.5);
    for (const mongoEstate of randomizedEstates) {
      if (process.env.NODE_ENV === 'development') {
        foundEstatesWithImages.push(mongoEstate);
        if (foundEstatesWithImages.length === selectingAmount) {
          break;
        }
      } else {
        const images = await this.vitecService.fetchGalleryNordvikbolig(mongoEstate.estateId);
        if (images.data.estate?.images.length) {
          foundEstatesWithImages.push(mongoEstate);
          if (foundEstatesWithImages.length === selectingAmount) {
            break;
          }
        }
      }
    }

    if (!foundEstatesWithImages) {
      this.logger.error('Could not find possible estate for daily price guessing quiz');
      return;
    }

    this.logger.log(
      `Selected daily estates for price guessing: ${foundEstatesWithImages.map((e) => e.estateId).join(', ')}`,
    );

    await this.priceGuessingService.createForToday(
      foundEstatesWithImages.map((e) => ({
        estateVitecId: e.estateId,
        dateOfGuessing: new Date(),
        priceSnapshot: isNumber(e.estatePrice.totalPrice) ? Math.round(e.estatePrice.totalPrice) : minPrice,
      })),
    );
  }

  async triggerNotification(): Promise<void> {
    this.logger.log('Daily estate selection notification for price guessing is triggered');

    if (!this.isPriceGuessingNotificationEnabled()) {
      this.logger.error(
        `The following feature flags are not enabled, not sending any notifications: ${FeatureFlag.SendPriceGuessingDailyPush}, ${FeatureFlag.SendPriceGuessingDailyFeed}`,
      );
      return;
    }
    const notiGroupNameOfQuiz: keyof UserNotificationGroups = 'quiz';
    const allUsersWhoHaveQuizEnabled = await this.userService.findAllIdsWithUserSettingEnabled(notiGroupNameOfQuiz);
    let finishedUserIds: string[] = [];
    try {
      const { data } = await axios.get<{ userIdsWhoFinishedQuizForToday: string[] }>(
        urljoin(this.appConfigService.getBackendUrl(), 'getQuizFinishedUsersForToday'),
      );
      finishedUserIds = data.userIdsWhoFinishedQuizForToday;
    } catch (e) {
      this.logger.error(`Got error trying to get users who finished for today: ${JSON.stringify(e)}`);
    }
    const userIdsWhoHaveNotFinishedForTodayAndQuizNotiIsEnabled = difference(
      allUsersWhoHaveQuizEnabled.map((u) => u.id),
      finishedUserIds,
    );
    this.logger.log(
      `Sending price guessing quiz notification to ${userIdsWhoHaveNotFinishedForTodayAndQuizNotiIsEnabled.length} users, ${finishedUserIds.length} already completed`,
    );

    await this.userNotificationService.broadcastPushNotifyUsers({
      userIds: userIdsWhoHaveNotFinishedForTodayAndQuizNotiIsEnabled,
      pushFeatureFlag: FeatureFlag.SendPriceGuessingDailyPush,
      message: DailyPriceGuessingGameEstateNotificationService.NOTIFICATION_MESSAGE,
      redirectUrl: '/customer/quiz/today',
    });
  }
}
