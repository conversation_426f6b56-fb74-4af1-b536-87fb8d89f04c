import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class StorebrandBuyerNotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    'Gratulerer med boligkjøpet! Sjekk hvor mye du kan spare på å refinansiere boliglånet gjennom vår partner Storebrand';

  private static readonly ICON_NAME = 'revenue';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/storebrand';

  private readonly logger = new ConsoleLogger(StorebrandBuyerNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('05 20 * * *', {
    name: StorebrandBuyerNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled storebrand buyer notifications');
    await this.trigger();
    this.logger.log(`Scheduled storebrand buyer notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 1, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesWithRecentContractMeeting = await this.estateModel.find({
      contractMeetingDate: { $lt: endDate, $gte: startDate },
    });

    this.logger.debug(
      `Estates which had a contract meeting recently: ${JSON.stringify(
        estatesWithRecentContractMeeting.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesWithRecentContractMeeting,
      iconName: StorebrandBuyerNotificationService.ICON_NAME,
      message: StorebrandBuyerNotificationService.NOTIFICATION_MESSAGE,
      url: StorebrandBuyerNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { buyer: true },
      pushFeatureFlag: FeatureFlag.SendStorebrandBuyerPush,
      feedFeatureFlag: FeatureFlag.SendStorebrandBuyerFeed,
    });
  }
}
