import { ConsoleLogger } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';

import { Test } from '@nestjs/testing';
import { Model } from 'mongoose';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService from '../../authorization/authorization.service';
import { EiendomsverdiService } from '../../eiendomsverdi/eiendomsverdi.service';
import { MailService } from '../../mail/mail.service';
import { mocAppConfigServiceFactory } from '../../mock-classes/mock-app-config-service';
import { mockAuthorizationServiceFactory } from '../../mock-classes/mock-authorization-service';
import { mockEstateServiceFactory } from '../../mock-classes/mock-estate-service';
import { mockEvServiceFactory } from '../../mock-classes/mock-ev-service';
import { mockMailService } from '../../mock-classes/mock-mail-services';
import { mockModelFactory } from '../../mock-classes/mock-model';
import { mockMongoServiceFactory } from '../../mock-classes/mock-mongo-service';
import { mockNordvikApiServiceFactory } from '../../mock-classes/mock-nordvik-api-service';
import { mockNotificationServiceFactory } from '../../mock-classes/mock-notification-service';
import { mockS3ServiceFactory } from '../../mock-classes/mock-s3-service';
import { mockSchedulerRegistryFactory } from '../../mock-classes/mock-scheduler-registry';
import { mockSyncServiceFactory } from '../../mock-classes/mock-sync-service';
import { mockUserNotifierServiceFactory } from '../../mock-classes/mock-user-notifier-service';
import { mockUserServiceFactory } from '../../mock-classes/mock-user-service';
import { mockVitecServiceFactory } from '../../mock-classes/mock-vitec-service';
import { mockEstateFactory } from '../../mock-classes/schema/mock-estate';
import { MongoService } from '../../mongo/mongo.service';
import { NordvikboligApiService } from '../../norvikbolig-api/nordvikbolig-api.service';
import { EstateService } from '../../pg/estate/estate.service';
import { User } from '../../pg/user/user.model';
import { UserService } from '../../pg/user/user.service';
import { S3Service } from '../../s3/s3.service';
import { BuyerDocument } from '../../sync/schema/buyer.schema';
import { ContactDocument } from '../../sync/schema/contact.schema';
import { DepartmentDocument } from '../../sync/schema/department.schema';
import { EmployeeDocument } from '../../sync/schema/employee.schema';
import { BrokerRole, CheckListTag, CheckListValue, Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { ProxyDocument } from '../../sync/schema/proxy.schema';
import { SellerDocument } from '../../sync/schema/seller.schema';
import { SyncService } from '../../sync/sync.service';
import { VitecService } from '../../vitec/vitec.service';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';
import { OvertakeProtocolNotificationService } from './overtake-protocol-notification.service';

const getMockDependencies = (): {
  mockSchedulerRegistry: SchedulerRegistry;
  mockVitecService: VitecService;
  mockMongoService: MongoService;
  mockSyncService: SyncService;
  mockS3Service: S3Service;
  mockAuthorizationService: AuthorizationService;
  mockUserService: UserService;
  mockNotificationService: NotificationService;
  mockAppConfigService: AppConfigService;
  mockUserNotifierService: UserNotifierService;
  mockNordvikApiService: NordvikboligApiService;
  estateModel: Model<EstateDocument>;
  departmentModel: Model<DepartmentDocument>;
  employeeModel: Model<EmployeeDocument>;
  sellerModel: Model<SellerDocument>;
  buyerModel: Model<BuyerDocument>;
  proxyModel: Model<ProxyDocument>;
  contactModel: Model<ContactDocument>;
  estateService: EstateService;
  evService: EiendomsverdiService;
  mockMailService: MailService;
} => ({
  mockSchedulerRegistry: mockSchedulerRegistryFactory(),
  mockVitecService: mockVitecServiceFactory(),
  mockMongoService: mockMongoServiceFactory(),
  mockSyncService: mockSyncServiceFactory(),
  mockS3Service: mockS3ServiceFactory(),
  mockAuthorizationService: mockAuthorizationServiceFactory(),
  mockUserService: mockUserServiceFactory(),
  mockNotificationService: mockNotificationServiceFactory(),
  mockAppConfigService: mocAppConfigServiceFactory(),
  mockUserNotifierService: mockUserNotifierServiceFactory(),
  mockNordvikApiService: mockNordvikApiServiceFactory(),
  estateModel: mockModelFactory<EstateDocument>(),
  departmentModel: mockModelFactory<DepartmentDocument>(),
  employeeModel: mockModelFactory<EmployeeDocument>(),
  sellerModel: mockModelFactory<SellerDocument>(),
  buyerModel: mockModelFactory<BuyerDocument>(),
  proxyModel: mockModelFactory<ProxyDocument>(),
  contactModel: mockModelFactory<ContactDocument>(),
  estateService: mockEstateServiceFactory(),
  evService: mockEvServiceFactory(),
  mockMailService: mockMailService(),
});

let deps = getMockDependencies();
let logger: ConsoleLogger;

describe('OvertakeProtocolNotificationService', () => {
  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        {
          provide: ConsoleLogger,
          useValue: {
            log: jest.fn(),
            setContext: jest.fn(),
            error: jest.fn().mockImplementation(console.log),
          },
        },
      ],
    }).compile();
    logger = moduleRef.get<ConsoleLogger>(ConsoleLogger);
    deps = getMockDependencies();

    const mockAppUser = {
      id: 'mockAppUserId',
    } as User;

    (deps.mockAppConfigService.getAppUrl as jest.Mock).mockReturnValue('');
    (deps.mockAuthorizationService.isFeatureEnabledForEstate as jest.Mock).mockReturnValue(true);
    (deps.mockNotificationService.triggerFeedNotification as jest.Mock).mockResolvedValue('');
    (deps.mockNotificationService.triggerPushNotification as jest.Mock).mockResolvedValue('');
    (deps.mockNotificationService.triggerSmsNotification as jest.Mock).mockResolvedValue('');
    (deps.mockNotificationService.delayByLastSentSms as jest.Mock).mockResolvedValue('');
    (deps.mockMailService.sendMail as jest.Mock).mockResolvedValue('');
    (deps.mockUserService.findByPhoneNumber as jest.Mock).mockResolvedValue(mockAppUser);
  });

  describe('sendNotification', () => {
    it('should not throw error', async () => {
      const mockAppUser = {
        id: 'mockAppUserId',
      } as User;

      const estate = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 2,
        address: {
          streetAdress: 'mockStreetAddress',
        },
        brokersIdWithRoles: [
          {
            brokerRole: BrokerRole.MAIN_BROKER,
            employee: {
              mobilePhone: 'mockBrokerMobilePhoneNumber',
              email: '<EMAIL>',
            },
          },
        ],
      } as EstateDocument;

      const contacts: BuyerDocument[] = [
        {
          id: 'mockContactId',
          mobilePhone: 'mockMobilePhone',
          contactId: 'mockContactId',
          email: '<EMAIL>',
        } as BuyerDocument,
      ];

      (deps.mockAppConfigService.getAppUrl as jest.Mock).mockReturnValue('');
      (deps.mockAuthorizationService.isFeatureEnabledForEstate as jest.Mock).mockReturnValue(true);
      (deps.mockNotificationService.triggerFeedNotification as jest.Mock).mockResolvedValue('');
      (deps.mockNotificationService.triggerPushNotification as jest.Mock).mockResolvedValue('');
      (deps.mockNotificationService.triggerSmsNotification as jest.Mock).mockResolvedValue('');
      (deps.mockNotificationService.delayByLastSentSms as jest.Mock).mockResolvedValue('');
      (deps.mockMailService.sendMail as jest.Mock).mockResolvedValue('');
      (deps.mockUserService.findByPhoneNumber as jest.Mock).mockResolvedValue(mockAppUser);

      const otpNotificationService = new OvertakeProtocolNotificationService(
        deps.mockUserService,
        deps.mockAppConfigService,
        deps.mockAuthorizationService,
        deps.mockNotificationService,
        deps.mockMailService,
        deps.mockUserNotifierService,
        logger,
        deps.mockNordvikApiService,
        deps.estateModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
      );

      await otpNotificationService.sendNotification(estate, contacts);

      expect(deps.mockNotificationService.triggerSmsNotification).toHaveBeenCalled();
    });
    it('should catch every error', async () => {
      (deps.mockAppConfigService.getAppUrl as jest.Mock).mockReturnValue(undefined);

      const otpNotificationService = new OvertakeProtocolNotificationService(
        deps.mockUserService,
        deps.mockAppConfigService,
        deps.mockAuthorizationService,
        deps.mockNotificationService,
        deps.mockMailService,
        deps.mockUserNotifierService,
        logger,
        deps.mockNordvikApiService,
        deps.estateModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
      );

      const estate = {
        estateId: 'mockVitecEstateId',
        departmentId: 123,
        estatePrice: {} as any,
        status: 2,
        address: {
          streetAdress: 'mockStreetAddress',
        },
        brokersIdWithRoles: [
          {
            brokerRole: BrokerRole.MAIN_BROKER,
            employee: {
              mobilePhone: 'mockBrokerMobilePhoneNumber',
              email: '<EMAIL>',
            },
          },
        ],
      } as EstateDocument;

      const contacts: BuyerDocument[] = [
        {
          id: 'mockContactId',
          mobilePhone: 'mockMobilePhone',
          contactId: 'mockContactId',
          email: '<EMAIL>',
        } as BuyerDocument,
      ];

      await otpNotificationService.sendNotification(estate, contacts);

      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('shouldCreateOtpForEstate', () => {
    const otpNotificationServiceFactory = () =>
      new OvertakeProtocolNotificationService(
        deps.mockUserService,
        deps.mockAppConfigService,
        deps.mockAuthorizationService,
        deps.mockNotificationService,
        deps.mockMailService,
        deps.mockUserNotifierService,
        logger,
        deps.mockNordvikApiService,
        deps.estateModel,
        deps.sellerModel,
        deps.buyerModel,
        deps.proxyModel,
      );

    it('should create otp with sale assignmentTypeGroup without a checklist', () => {
      const estate: Estate = {
        ...mockEstateFactory(),
        assignmentTypeGroup: 1,
        checkList: undefined,
      };

      const result = otpNotificationServiceFactory().shouldCreateOtpForEstate(estate);
      expect(result).toBeTruthy();
    });

    it('should create otp with settlement assignmentTypeGroup without checklist items', () => {
      const estate: Estate = {
        ...mockEstateFactory(),
        assignmentTypeGroup: 4,
      };

      const result = otpNotificationServiceFactory().shouldCreateOtpForEstate(estate);
      expect(result).toBeTruthy();
    });

    it('should not create OTP with SALE with DEACTIVATED item', () => {
      const estate: Estate = {
        ...mockEstateFactory(),
        assignmentTypeGroup: 1,
      };

      estate.checkList.checkListItems = [
        {
          tags: [CheckListTag.DEACTIVATE_OTP_FOR_SALE],
          value: CheckListValue.YES,
          changedBy: null,
          changedDate: null,
        },
      ];

      const result = otpNotificationServiceFactory().shouldCreateOtpForEstate(estate);
      expect(result).toBeFalsy();
    });

    it('should not create OTP with SETTLEMENT with DEACTIVATED item', () => {
      const estate: Estate = {
        ...mockEstateFactory(),
        assignmentTypeGroup: 4,
      };

      estate.checkList.checkListItems = [
        {
          tags: [CheckListTag.DEACTIVATE_OTP_FOR_SETTLEMENT],
          value: CheckListValue.YES,
          changedBy: null,
          changedDate: null,
        },
      ];

      const result = otpNotificationServiceFactory().shouldCreateOtpForEstate(estate);
      expect(result).toBeFalsy();
    });

    it('should not create OTP with PROJECT without ACTIVATED item', () => {
      const estate: Estate = {
        ...mockEstateFactory(),
        assignmentTypeGroup: 7,
      };

      estate.checkList.checkListItems = [
        {
          tags: [],
          value: CheckListValue.YES,
          changedBy: null,
          changedDate: null,
        },
      ];

      const result = otpNotificationServiceFactory().shouldCreateOtpForEstate(estate);
      expect(result).toBeFalsy();
    });

    it('should create OTP with PROJECT with ACTIVATED item', () => {
      const estate: Estate = {
        ...mockEstateFactory(),
        assignmentTypeGroup: 7,
      };

      estate.checkList.checkListItems = [
        {
          tags: [CheckListTag.ACTIVATE_OTP_FOR_PROJECT],
          value: CheckListValue.YES,
          changedBy: null,
          changedDate: null,
        },
      ];

      const result = otpNotificationServiceFactory().shouldCreateOtpForEstate(estate);
      expect(result).toBeTruthy();
    });
  });
});
