import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class HmhBuyerMoving2NotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    'Gjø<PERSON> det enkelt å flytte til din nye bolig. Bestill sikker nedpakking, transport og utpakking av dine eiendeler';

  private static readonly ICON_NAME = 'icon_finance_package'; // box icon

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/hmh-moving';

  private readonly logger = new ConsoleLogger(HmhBuyerMoving2NotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: HmhBuyerMoving2NotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled hmhmoving 2 notifications');
    await this.trigger();
    this.logger.log(`Scheduled hmhmoving notifications 2 are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = add(startOfDay(new Date()), { days: 28, hours: 22 });
    const endDate = add(startDate, { days: 1 });
    const estates1WeekAfterContractMeeting = await this.estateModel.find({
      takeOverDate: {
        $gte: startDate,
        $lt: endDate,
      },
    });

    this.logger.debug(
      `Estates which had contractMeeting 7 days ago: ${JSON.stringify(
        estates1WeekAfterContractMeeting.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estates1WeekAfterContractMeeting,
      iconName: HmhBuyerMoving2NotificationService.ICON_NAME,
      message: HmhBuyerMoving2NotificationService.NOTIFICATION_MESSAGE,
      url: HmhBuyerMoving2NotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { buyer: true },
      pushFeatureFlag: FeatureFlag.SendHmhBuyerMoving2Push,
      feedFeatureFlag: FeatureFlag.SendHmhBuyerMoving2Feed,
    });
  }
}
