import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { addHours, isBefore, sub } from 'date-fns';
import { Model } from 'mongoose';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { FavoriteService } from '../../pg/favorite/favorite.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class FavoriteEstateListedNotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    '[address] er nå tilgjengelig på markedet med prisantydning [estimatedPrice]';

  private static readonly ICON_NAME = 'icon_interfaces_heart';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/pre-market/favorites';

  private readonly logger = new ConsoleLogger(FavoriteEstateListedNotificationService.name);

  constructor(
    private readonly favoriteService: FavoriteService,
    private readonly notificationService: NotificationService,
    private readonly authorizationService: AuthorizationService,
    private readonly userNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron(CronExpression.EVERY_2_HOURS, {
    name: FavoriteEstateListedNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled favoriteestatelisted notifications');
    await this.trigger();
    this.logger.log(`Scheduled favoriteestatelisted notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(new Date(), { hours: 4 });
    const endDate = sub(new Date(), { hours: 2 });
    const favorites = await this.favoriteService.findAll();
    const favoriteEstateIds = favorites.map((f) => f.estateID);

    const listedFavoritedEstates = (
      await this.estateModel.find({
        estateId: { $in: favoriteEstateIds },
        status: VitecEstateStatus.FOR_SALE,
        statusChanges: {
          $elemMatch: {
            from: VitecEstateStatus.PREPARATION,
            to: VitecEstateStatus.FOR_SALE,
            date: {
              $gt: startDate,
              $lte: endDate,
            },
          },
        },
      })
    ).filter((e) => isBefore(addHours(new Date(), 2), e.expireDate as Date));

    const listedFavoritedEstatesIds = listedFavoritedEstates.map((e) => e.estateId);

    const listedFavorites = favorites
      .filter((f) => listedFavoritedEstatesIds.includes(f.estateID))
      .map((f) => ({ userID: f.userID, estate: listedFavoritedEstates.find((e) => e.estateId === f.estateID) }));

    this.logger.debug(
      `Recently listed favorited estates: ${JSON.stringify(listedFavoritedEstates.map((estate) => estate.estateId))}`,
    );

    for (const favorite of listedFavorites) {
      const isFeatureEnabled = (featureFlag) =>
        this.authorizationService.isFeatureEnabledForEstate(featureFlag, favorite.estate.estateId);
      const message = FavoriteEstateListedNotificationService.NOTIFICATION_MESSAGE.replace(
        '[address]',
        favorite.estate.address.streetAdress,
      ).replace('[estimatedPrice]', `kr. ${favorite.estate.estatePrice.priceSuggestion?.toLocaleString('de-DE')}`);

      await this.userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
        feedFeatureFlag: FeatureFlag.SendFavoriteEstateListedFeed,
        iconName: FavoriteEstateListedNotificationService.ICON_NAME,
        isFeatureEnabled: isFeatureEnabled,
        message,
        userId: favorite.userID,
        pushFeatureFlag: FeatureFlag.SendFavoriteEstateListedPush,
        redirectUrl: FavoriteEstateListedNotificationService.NOTIFICATION_REDIRECT_URL.replace(
          '[estateID]',
          favorite.estate.estateId,
        ),
      });
    }
  }
}
