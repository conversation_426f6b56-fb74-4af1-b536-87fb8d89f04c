import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { endOfDay, startOfDay, sub } from 'date-fns';
import { parsePhoneNumber } from 'libphonenumber-js';
import { Model } from 'mongoose';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { UserService } from '../../pg/user/user.service';
import { Buyer, BuyerDocument } from '../../sync/schema/buyer.schema';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { Seller, SellerDocument } from '../../sync/schema/seller.schema';

export enum ContactType {
  PERSON = 0,
  COMPANY = 1,
}

@Injectable()
export class BeforeArchiveNotificationService {
  private readonly logger = new ConsoleLogger(BeforeArchiveNotificationService.name);
  private static readonly EMAIL_FEATURE_FLAG = FeatureFlag.SendBeforeArchiveEmail;
  private static readonly BUYER_EMAIL_SUBJECT = 'Følg verdiutviklingen på din nye bolig';
  private static readonly BUYER_EMAIL_HTML_TEMPLATE = `[firstName],<br><br>Gratulerer med overtakelsen av [address]. Vi vil gjerne informere deg om at kjøpsprosessen din snart forsvinner fra appen. Vi anbefaler deg derfor å lagre informasjonen og dokumentene du behøver.<br><br>Så snart du er registrert som hjemmelshaver på boligen, kan du identifisere deg på nytt i appen og følge verdiutviklingen på din bolig. For å identifisere deg på nytt, gå til “Min bolig” i appen, trykk på spørsmålstegn-ikonet og følg veiviseren. Det tar normalt 1-4 uker fra overtakelse, til du er registrert som hjemmelshaver hos kartverket.`;
  private static readonly SELLER_EMAIL_SUBJECT = 'Salgsprosessen din forsvinner snart fra appen';
  private static readonly SELLER_EMAIL_HTML_TEMPLATE = `[firstName],<br><br>Gratulerer med salget av [address]. Vi vil gjerne informere deg om at salgsprosessen din snart forsvinner fra appen. Vi anbefaler deg derfor å lagre informasjonen og dokumentene du behøver.<br><br>Dersom du har kjøpt ny bolig etter at du registrerte deg i appen, kan du identifisere deg på nytt og følge verdiutviklingen på din nye bolig. For å identifisere deg på nytt, gå til “Min bolig” i appen, trykk på spørsmålstegn-ikonet og følg veiviseren. Merk at den ikke vil dukke opp før du er registrert som hjemmelshaver hos kartverket på den nye boligen. Det tar normalt 1-4 uker fra overtakelse, til du er registrert som hjemmelshaver hos kartverket.`;

  constructor(
    private readonly authorizationService: AuthorizationService,
    private readonly mailService: MailService,
    private readonly userService: UserService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
  ) {}

  private isEmailEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(BeforeArchiveNotificationService.EMAIL_FEATURE_FLAG, estateId);

  sendNotification = async ({
    estate,
    buyers,
    sellers,
  }: {
    estate: EstateDocument;
    buyers: BuyerDocument[];
    sellers: SellerDocument[];
  }): Promise<void> => {
    const sendMail = async ({
      subject,
      template,
      user,
    }: {
      subject: string;
      template: string;
      user: { firstName: string; email: string };
    }) => {
      const mailMessage = template
        .replace('[address]', estate.address.streetAdress)
        .replace('[firstName]', user.firstName || '');

      this.logger.log(
        `Sending before-archive email notification for estate: ${estate.estateId}, to user ${user.email}`,
      );
      try {
        await this.mailService.sendMail({
          to: user.email,
          subject,
          html: mailMessage,
        });
      } catch (e) {
        this.logger.error(`Error sending email to user ${user.email}, error ${e}`);
      }
    };

    for (const buyer of buyers) {
      if (buyer.contactType === ContactType.PERSON) {
        this.logger.log(`Sending before-archive buyer email notifications for estate: ${estate.estateId}`);
        await sendMail({
          subject: BeforeArchiveNotificationService.BUYER_EMAIL_SUBJECT,
          template: BeforeArchiveNotificationService.BUYER_EMAIL_HTML_TEMPLATE,
          user: buyer,
        });
        this.logger.log(`Finished sending before-archive buyer email notifications for estate: ${estate.estateId}`);
      } else {
        this.logger.log(
          `Skipped before-archive buyer email notifications for estate: ${estate.estateId}, contact type: ${buyer.contactType}`,
        );
      }
    }
    for (const seller of sellers) {
      if (seller.contactType === ContactType.PERSON) {
        this.logger.log(`Sending before-archive seller email notifications for estate: ${estate.estateId}`);
        await sendMail({
          subject: BeforeArchiveNotificationService.SELLER_EMAIL_SUBJECT,
          template: BeforeArchiveNotificationService.SELLER_EMAIL_HTML_TEMPLATE,
          user: seller,
        });
        this.logger.log(`Finished sending before-archive seller email notifications for estate: ${estate.estateId}`);
      } else {
        this.logger.log(
          `Skipped before-archive seller email notifications for estate: ${estate.estateId}, contact type: ${seller.contactType}`,
        );
      }
    }
  };

  async findRegisteredBuyersAndSellers(
    inputBuyers: BuyerDocument[],
    inputSellers: SellerDocument[],
  ): Promise<{
    buyers: BuyerDocument[];
    sellers: SellerDocument[];
  }> {
    const idAndParsedPhoneNumber = [...inputBuyers, ...inputSellers]
      .map((c: BuyerDocument | SellerDocument) => {
        const phoneNumber = parsePhoneNumber(c.mobilePhone || '', 'NO');
        return {
          contactId: c.contactId,
          phoneNumber: phoneNumber && phoneNumber.isValid() ? phoneNumber.number : null,
        };
      })
      .filter((c) => c.phoneNumber);

    const pgUsers = await this.userService.findAllByPhoneNumbers(idAndParsedPhoneNumber.map((c) => c.phoneNumber));

    const buyers = inputBuyers.filter((b) =>
      idAndParsedPhoneNumber.find(
        (c) => b.contactId === c.contactId && pgUsers.find((u) => u.phoneNumber === c.phoneNumber),
      ),
    );
    const sellers = inputSellers.filter((s) =>
      idAndParsedPhoneNumber.find(
        (c) => s.contactId === c.contactId && pgUsers.find((u) => u.phoneNumber === c.phoneNumber),
      ),
    );

    return {
      buyers,
      sellers,
    };
  }

  @Cron(CronExpression.EVERY_DAY_AT_7PM, {
    name: BeforeArchiveNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering before-archive-notifications');
    await this.trigger();
    this.logger.log(`Scheduled before-archive-notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(sub(startOfDay(new Date()), { hours: 2 }), { days: 2 });
    const endDate = sub(sub(endOfDay(new Date()), { hours: 2 }), { days: 2 });

    const estates = await this.estateModel.find({
      takeOverDate: {
        $gte: startDate,
        $lt: endDate,
      },
    });

    this.logger.log(
      `Estates that we are sending before-archive-notifications to: ${JSON.stringify(
        estates.map((estate) => estate.estateId),
      )}`,
    );

    for (const estate of estates) {
      if (!this.isEmailEnabled(estate.estateId)) {
        this.logger.warn(
          `Skipping before-archive-notification sending for estate: ${estate.estateId}.
          The following feature flag is disabled: ${BeforeArchiveNotificationService.EMAIL_FEATURE_FLAG}`,
        );
        continue;
      }

      const buyerIds = estate.buyers.map((b) => b.contactId);
      const sellerIds = estate.sellers.map((s) => s.contactId);
      const mongoBuyers = await this.buyerModel.find({ contactId: { $in: buyerIds } });
      const mongoSellers = await this.sellerModel.find({ contactId: { $in: sellerIds } });

      const { buyers, sellers } = await this.findRegisteredBuyersAndSellers(mongoBuyers, mongoSellers);

      const buyerEmailsString = buyers.map((b) => b.email).join(',');
      const sellerEmailsString = sellers.map((b) => b.email).join(',');
      this.logger.log(
        `Found contacts for before-archive-notification sending, estate: ${estate.estateId}, buyers: ${buyerEmailsString}, sellers: ${sellerEmailsString}`,
      );

      await this.sendNotification({ estate, buyers, sellers });
    }
    this.logger.log('Finished sending before-archive-notifications');
  }
}
