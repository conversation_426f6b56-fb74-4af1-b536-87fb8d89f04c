import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class EkstraNotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'Nå flere potensielle budgivere med Nordvik Ekstra';

  private static readonly ICON_NAME = 'ranking';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/estates/[estateId]/timeline/preparation';

  private readonly logger = new ConsoleLogger(EkstraNotificationService.name);

  constructor(
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    private readonly estateNotifierService: UserNotifierService,
  ) {}

  @Cron('45 20 * * *', {
    name: EkstraNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled Ekstra notifications');
    await this.trigger();
    this.logger.log(`Scheduled Ekstra notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 2, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesWithRecentFinnPublish = await this.estateModel.find({
      finnPublishDate: { $lt: endDate, $gte: startDate },
    });

    this.logger.debug(
      `Estates which had finn publish date recently: ${JSON.stringify(
        estatesWithRecentFinnPublish.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesWithRecentFinnPublish,
      iconName: EkstraNotificationService.ICON_NAME,
      message: EkstraNotificationService.NOTIFICATION_MESSAGE,
      url: EkstraNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true },
      pushFeatureFlag: FeatureFlag.SendEkstraPush,
      feedFeatureFlag: FeatureFlag.SendEkstraFeed,
      urlReplacer: (url, estate) => url.replace('[estateId]', estate.estateId),
    });
  }
}
