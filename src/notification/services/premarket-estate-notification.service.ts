import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { addHours, isBefore, sub } from 'date-fns';
import { isEmpty, isNil } from 'lodash';
import { Model } from 'mongoose';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { AreaService } from '../../pg/area/area.service';
import { UserOptionService } from '../../pg/user-options/user-options.service';
import { UserAreaService } from '../../pg/userarea/userarea.service';
import { CheckListTag, CheckListValue, Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class PremarketEstateNotificationService {
  private static readonly NOTIFICATION_MESSAGE =
    '[numProperties] nye boliger som matcher dine kriterier kommer snart for salg';

  private static readonly ICON_NAME = 'icon_interfaces_heart';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/pre-market/suggestions';

  private readonly logger = new ConsoleLogger(PremarketEstateNotificationService.name);

  constructor(
    private readonly userOptionservice: UserOptionService,
    private readonly notificationService: NotificationService,
    private readonly authorizationService: AuthorizationService,
    private readonly areaService: AreaService,
    private readonly userAreaService: UserAreaService,
    private readonly userNotifierEstate: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('30 20 */3 * *', {
    name: PremarketEstateNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled premarket estate listed notifications');
    await this.trigger();
    this.logger.log(`Scheduled premarket estate listed notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(new Date(), { hours: 74 });
    const endDate = sub(new Date(), { hours: 2 });
    const premarketEstates = (
      await this.estateModel.find(
        {
          status: VitecEstateStatus.PREPARATION,
          statusChanges: {
            $elemMatch: {
              to: VitecEstateStatus.PREPARATION,
              date: {
                $gte: startDate,
                $lt: endDate,
              },
            },
          },
        },
        {
          estateSize: 1,
          buildings: 1,
          estatePrice: 1,
          estateId: 1,
          address: 1,
          noOfBedRooms: 1,
          checkList: 1,
          expireDate: 1,
        },
      )
    ).filter((e) => isBefore(addHours(new Date(), 2), e.expireDate as Date));

    const userOptions = await this.userOptionservice.findAll();

    this.logger.debug(`Recent premarket estates: ${JSON.stringify(premarketEstates.map((estate) => estate.estateId))}`);

    for (const userOption of userOptions) {
      const areaIds = await this.userAreaService.getByUserId(userOption.userID);
      const postalCodes = await this.areaService.getPostalCodesByIds(areaIds);
      const priceRange = getPriceRange(userOption.price);
      const livingAreaRange = getLivingAreaRange(userOption.area);
      const numberOfBedrooms = getNumberOfBedroomsRange(userOption.profile?.rooms);
      const matchingEstates = premarketEstates.filter(
        (estate) =>
          estate.estateSize?.primaryRoomArea &&
          estate.estatePrice?.priceSuggestion &&
          postalCodes.includes(estate.address.zipCode || '') &&
          (estate.noOfBedRooms || 0) >= numberOfBedrooms.from &&
          estate.estateSize.primaryRoomArea >= livingAreaRange.from &&
          estate.estateSize.primaryRoomArea <= livingAreaRange.to &&
          estate.estatePrice.priceSuggestion >= priceRange.from &&
          estate.estatePrice.priceSuggestion <= priceRange.to &&
          !estate.checkList?.checkListItems?.find(
            (e) => e.tags.includes(CheckListTag.OFF_MARKET) && e.value === CheckListValue.YES,
          ),
      );

      if (isEmpty(matchingEstates)) {
        continue;
      }

      await this.userNotifierEstate.notifyUserIfHisSettingsAreEnabledForThisFeature({
        feedFeatureFlag: FeatureFlag.SendPremarketEstateFeed,
        iconName: PremarketEstateNotificationService.ICON_NAME,
        isFeatureEnabled: this.authorizationService.isFeatureEnabled,
        message: PremarketEstateNotificationService.NOTIFICATION_MESSAGE.replace(
          '[numProperties]',
          matchingEstates.length.toString(),
        ),
        pushFeatureFlag: FeatureFlag.SendPremarketEstatePush,
        redirectUrl: PremarketEstateNotificationService.NOTIFICATION_REDIRECT_URL,
        userId: userOption.userID,
        deletePreviousFeed: true,
      });
    }
  }
}

type RangeSelector = { from: number; to: number };

type Limits = { oldMin: number; newMin: number; oldMax: number; newMax: number };

type MinMaxLimitExtender = (limits: Limits) => (input: number) => number;
const minLimitExtender: MinMaxLimitExtender =
  ({ oldMin, newMin, oldMax }) =>
  (input) => {
    if (input <= oldMin) {
      return newMin;
    }
    if (input >= oldMax) {
      return oldMax;
    }
    return input;
  };

const maxLimitExtender: MinMaxLimitExtender =
  ({ oldMin, oldMax, newMax }) =>
  (input) => {
    if (input <= oldMin) {
      return oldMin;
    }
    if (input >= oldMax) {
      return newMax;
    }
    return input;
  };

type GetRange = (inputRange: RangeSelector | null | undefined) => RangeSelector;
type GetRangeFactory = (params: Limits) => GetRange;
const getRangeFactory: GetRangeFactory = (limits) => (inputRange) => {
  if (isNil(inputRange)) {
    return {
      from: limits.newMin,
      to: limits.newMax,
    };
  }

  return {
    from: minLimitExtender(limits)(inputRange.from),
    to: maxLimitExtender(limits)(inputRange.to),
  };
};

const PRICE_LIMITS: Limits = {
  oldMin: 10000,
  newMin: 10000,
  oldMax: 15000000,
  newMax: 999999999999,
};

const getPriceRange = getRangeFactory(PRICE_LIMITS);

const LIVING_AREA_LIMITS: Limits = {
  oldMin: 10,
  newMin: 0,
  oldMax: 300,
  newMax: 999999999999,
};

const getLivingAreaRange = getRangeFactory(LIVING_AREA_LIMITS);

const NUMBER_OF_BEDROOMS_LIMITS: Limits = {
  oldMin: 0,
  newMin: 0,
  oldMax: 999999999999,
  newMax: 999999999999,
};

const getNumberOfBedroomsRange = getRangeFactory(NUMBER_OF_BEDROOMS_LIMITS);
