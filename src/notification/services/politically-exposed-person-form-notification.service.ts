import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { startOfDay, sub } from 'date-fns';
import { isNull } from 'lodash';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { MailAuditType } from '../../pg/mail-audit/mail-audit.model';
import { PEPForm } from '../../pg/pep-form/pep-form.model';
import { PEPFormService } from '../../pg/pep-form/pep-form.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import {
  <PERSON>rokerRole,
  CheckListTag,
  CheckListValue,
  Estate,
  EstateDocument,
  isCheckListItemChecked,
} from '../../sync/schema/estate.schema';
import { Seller, SellerDocument } from '../../sync/schema/seller.schema';
import { populatePEPMessageTemplate } from '../../utils/message.utils';
import { EstateAssignmentTypeGroup } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';

@Injectable()
export class PoliticallyExposedPersonFormNotificationService {
  private readonly logger = new ConsoleLogger(PoliticallyExposedPersonFormNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE_TEMPLATE_SELLER =
    'Hei,\n\nSelvdeklarering for [address] er klart til utfylling. Følg linken under for å fylle ut skjemaet.\n\n[URL]\n\nMed vennlig hilsen\n\nNordvik';
  private static readonly NOTIFICATION_MESSAGE_HTML_TEMPLATE_SELLER =
    'Selvdeklarering for [address] er klart til utfylling. Følg linken under for å fylle ut skjemaet.<br><br><a href="[URL]">[URL]<a/>';

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly mailService: MailService,
    private readonly pepFormSellerService: PEPFormService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
  ) {}

  isPepSellerFormCreationEnabled(estate: Estate): boolean {
    if (!estate) {
      return false;
    }
    switch (estate.assignmentTypeGroup) {
      case EstateAssignmentTypeGroup.SALE:
        return isCheckListItemChecked(estate.checkList, CheckListTag.ACTIVATE_PEP_SELLER_FOR_SALE);
      case EstateAssignmentTypeGroup.SETTLEMENT:
        return isCheckListItemChecked(estate.checkList, CheckListTag.ACTIVATE_PEP_SELLER_FOR_SETTLEMENT);
      case EstateAssignmentTypeGroup.FORECLOSURE:
        return isCheckListItemChecked(estate.checkList, CheckListTag.ACTIVATE_PEP_SELLER_FOR_FORECLOSURE);
      case EstateAssignmentTypeGroup.PROJECT_SALE:
        return isCheckListItemChecked(estate.checkList, CheckListTag.ACTIVATE_PEP_SELLER_FOR_PROJECT);
      case EstateAssignmentTypeGroup.VALUATION:
        return isCheckListItemChecked(estate.checkList, CheckListTag.ACTIVATE_PEP_SELLER_FOR_EVALUATION);
      default:
        return false;
    }
  }

  async createFormAndSendSellerNotificationsIfNotSent(estate: Estate) {
    const pepForm = await this.getOrCreatePepForm(estate.estateId);
    if (pepForm.isNotificationSent || pepForm.signingStarted) {
      this.logger.log(
        `Skipping pep-seller notification sending: the notification is already sent or signing already started for estate: ${estate.estateId}`,
      );
      return;
    }

    if (
      !this.isSmsEnabled(estate.estateId) &&
      !this.isEmailEnabled(estate.estateId) &&
      !this.isBrokerSmsEnabled(estate.estateId)
    ) {
      this.logger.warn(
        `Skipping pep-seller-form notification sending for estate: ${estate.estateId}. The following feature flags are disabled: ${FeatureFlag.SendPepSms}, ${FeatureFlag.SendPepEmail}, ${FeatureFlag.SendPepSmsToBroker}`,
      );
      return;
    }
    const sellerIds = estate.sellers.map((s) => s.contactId);
    const sellers = await this.sellerModel.find({ contactId: { $in: sellerIds } });
    this.logger.log(
      `Found pep-sellers for estate: ${estate.estateId}, sellers: ${sellers.map((b) => b.contactId).join(',')}`,
    );

    await this.sendNotificationToSellersAndBrokers({
      estate,
      contacts: sellers,
      mailTemplate: PoliticallyExposedPersonFormNotificationService.NOTIFICATION_MESSAGE_HTML_TEMPLATE_SELLER,
      smsTemplate: PoliticallyExposedPersonFormNotificationService.NOTIFICATION_MESSAGE_TEMPLATE_SELLER,
    });
  }

  private isBrokerSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendPepSmsToBroker, estateId);
  private isSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendPepSms, estateId);

  private isEmailEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendPepEmail, estateId);

  /**
   * Used only for seller PEP form notification sending.
   */
  private async sendNotificationToSellersAndBrokers({
    estate,
    contacts,
    mailTemplate,
    smsTemplate,
  }: {
    estate: Estate;
    contacts: Seller[];
    mailTemplate: string;
    smsTemplate: string;
  }) {
    for (const contact of contacts) {
      await this.sendNotificationToContact({
        estate,
        contact,
        mailTemplate,
        smsTemplate,
      });
    }
    await this.sendNotificationToBroker({
      estate,
      mailTemplate,
      smsTemplate,
    });

    await this.pepFormSellerService.updateIsNotificationSent(estate.estateId, true);
  }

  private async sendNotificationToContact({
    estate,
    contact,
    smsTemplate,
    mailTemplate,
  }: {
    estate: Estate;
    contact: Seller;
    smsTemplate: string;
    mailTemplate: string;
  }): Promise<void> {
    const smsMessage = populatePEPMessageTemplate({
      template: smsTemplate,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
    });
    const mailMessage = populatePEPMessageTemplate({
      template: mailTemplate,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
    });

    await this.sendContactSMS({
      contact,
      estate,
      smsMessage,
    });

    await this.sendContactEmail({
      contact,
      estate,
      mailMessage,
      subject: 'Selvdeklarering Selger',
    });
  }

  private async sendNotificationToBroker({
    estate,
    smsTemplate,
    mailTemplate,
  }: {
    estate: Estate;
    smsTemplate: string;
    mailTemplate: string;
  }): Promise<void> {
    smsTemplate;
    // const smsMessage = populatePEPMessageTemplate({
    //   template: smsTemplate,
    //   address: estate.address.streetAdress,
    //   estateId: estate.estateId,
    //   appUrl: this.appConfigService.getAppUrl(),
    // });
    const mailMessage = populatePEPMessageTemplate({
      template: mailTemplate,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
    });

    // await this.sendBrokerSMS({ estate, smsMessage });

    await this.sendBrokerEmail({
      estate,
      mailMessage,
      subject: `Selvdeklarering Selger - ${estate.assignmentNum} ${estate.address.streetAdress}`,
    });
  }

  private async sendBrokerSMS({ estate, smsMessage }: { estate: Estate; smsMessage: string }) {
    const broker = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0];

    if (!broker) {
      this.logger.warn(`broker has no phone number set for estate ${estate.estateId}`);
      return;
    }

    if (!this.isBrokerSmsEnabled(estate.estateId)) {
      this.logger.warn(
        `Skipping pep broker sms notification sending for estate: ${estate.estateId}. The following feature flags are disabled: ${FeatureFlag.SendPepSms}`,
      );
      return;
    }
    this.logger.log(
      `Sending pep broker sms notification for estate: ${estate.estateId}, broker: ${broker.employee.mobilePhone}`,
    );

    await this.notificationService.delayByLastSentSms(broker.employee.mobilePhone, SmsAuditType.PEP_SELLER_SMS);

    await this.notificationService
      .triggerSmsNotification({
        message: smsMessage,
        phoneNumber: broker.employee.mobilePhone,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.PEP_SELLER_SMS,
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: '',
        },
      })
      .catch((e) =>
        this.logger.error(
          `Error sending pep sms for estate: ${estate.estateId}, to broker ${broker.employee.mobilePhone}, error: ${e}`,
        ),
      );
  }

  private async sendBrokerEmail({
    estate,
    mailMessage,
    subject,
  }: {
    estate: Estate;
    mailMessage: string;
    subject: string;
  }) {
    if (!this.isEmailEnabled(estate.estateId)) {
      this.logger.warn(
        `Skipping pep broker email notification sending for estate: ${estate.estateId}. The following feature flags are disabled: ${FeatureFlag.SendPepEmail}`,
      );
      return;
    }
    const brokerEmail = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0]?.employee
      ?.email;
    this.logger.log(`Sending pep broker email notification for estate: ${estate.estateId}, broker: ${brokerEmail}`);
    await this.mailService
      .sendMail({
        to: brokerEmail,
        subject,
        html: mailMessage,
        mailAuditType: MailAuditType.PEP,
      })
      .catch((e) =>
        this.logger.error(`Error sending pep email for estate: ${estate.estateId}, to ${brokerEmail}, error: ${e}`),
      );
  }

  private async sendContactSMS({
    contact,
    estate,
    smsMessage,
  }: {
    contact: Seller;
    estate: Estate;
    smsMessage: string;
  }) {
    if (!contact.mobilePhone) {
      this.logger.warn(`${contact.contactId} has no phone number set`);
      return;
    }
    if (!this.isSmsEnabled(estate.estateId)) {
      this.logger.warn(
        `Skipping pep sms notification sending for estate: ${estate.estateId}, to ${contact.mobilePhone}. The following feature flags are disabled: ${FeatureFlag.SendPepSms}`,
      );
      return;
    }

    this.logger.log(
      `Sending pep sms notification for estate: ${estate.estateId} ${contact.contactId}, to ${contact.mobilePhone}`,
    );
    try {
      await this.notificationService.delayByLastSentSms(contact.mobilePhone, SmsAuditType.PEP_SELLER_SMS);

      await this.notificationService.triggerSmsNotification({
        message: smsMessage,
        phoneNumber: contact.mobilePhone,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.PEP_SELLER_SMS,
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: contact.contactId,
        },
      });
    } catch (e) {
      this.logger.error(`Error sending SMS to ${contact.mobilePhone}, error ${e}`);
    }
  }

  private async sendContactEmail({
    contact,
    estate,
    mailMessage,
    subject,
  }: {
    contact: Seller;
    estate: Estate;
    mailMessage: string;
    subject: string;
  }) {
    if (!this.isEmailEnabled(estate.estateId)) {
      this.logger.warn(
        `Skipping pep email notification sending for estate: ${estate.estateId} ${contact.contactId}, to ${contact.email}. The following feature flags are disabled: ${FeatureFlag.SendPepEmail}`,
      );
      return;
    }
    this.logger.log(
      `Sending pep email notification for estate: ${estate.estateId} ${contact.contactId}, to ${contact.email}`,
    );
    try {
      await this.mailService.sendMail({
        to: contact.email,
        subject,
        html: mailMessage,
        mailAuditType: MailAuditType.PEP,
      });
    } catch (e) {
      this.logger.error(`Error sending email to customer ${contact.email} ${contact.contactId}, error ${e}`);
    }
  }

  private async getOrCreatePepForm(estateId: string): Promise<PEPForm> {
    let pepForm = await this.pepFormSellerService.getById(estateId);

    if (isNull(pepForm)) {
      try {
        const url = urljoin(this.appConfigService.getBackendUrl(), 'estate', estateId, 'pep');

        const response = await axios.post(url, {
          validateStatus: () => true,
        });
        if (response.status !== 201) {
          this.logger.error(
            `Received error from BE while trying to create Pep form ${estateId}: ${response.status}:${JSON.stringify(
              response.data,
            )}`,
          );
        }
        pepForm = await this.pepFormSellerService.getById(estateId);

        this.logger.log(`PEP form is created for estate with estateId: ${estateId}, ${response.data}`);
      } catch (error) {
        this.logger.error(
          `Received error from BE ${estateId}: ${error.response.status}:${JSON.stringify(error.response.data)}`,
        );
      }
    }

    return pepForm;
  }

  // This is not Cron, this can only be called through API endpoint
  async triggerSeller(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 3 });

    const estates = await this.estateModel.find({
      'checkList.checkListItems': {
        $elemMatch: {
          tags: {
            $in: [
              CheckListTag.ACTIVATE_PEP_SELLER_FOR_SALE,
              CheckListTag.ACTIVATE_PEP_SELLER_FOR_SETTLEMENT,
              CheckListTag.ACTIVATE_PEP_SELLER_FOR_FORECLOSURE,
              CheckListTag.ACTIVATE_PEP_SELLER_FOR_PROJECT,
              CheckListTag.ACTIVATE_PEP_SELLER_FOR_EVALUATION,
            ],
          },
          value: CheckListValue.YES,
          changedDate: {
            $gte: startDate,
          },
        },
      },
    });

    this.logger.log(
      `Estates that we are sending pep-seller notifications: ${JSON.stringify(
        estates.map((estate) => estate.estateId),
      )}`,
    );

    for (const estate of estates) {
      try {
        await this.createFormAndSendSellerNotificationsIfNotSent(estate);
      } catch (e) {
        this.logger.error(`Error sending pep-seller notifications to estate: ${estate.estateId}, error ${e}`);
      }
    }
  }
}
