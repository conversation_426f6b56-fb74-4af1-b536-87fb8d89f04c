import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { add, startOfDay, sub } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class ExponovaNotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'Vi hjelper deg med å innrede ditt nye hjem';

  private static readonly ICON_NAME = 'icon_bedrooms';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/exponova';

  private readonly logger = new ConsoleLogger(ExponovaNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: ExponovaNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled exponova notifications');
    await this.trigger();
    this.logger.log(`Scheduled exponova notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 21, hours: 2 });
    const endDate = add(startDate, { days: 1 });
    const estatesWith21dayContractMeeting = await this.estateModel.find({
      contractMeetingDate: {
        $lt: endDate,
        $gte: startDate,
      },
    });

    this.logger.debug(
      `Estates which had a contract meeting 21 days ago: ${JSON.stringify(
        estatesWith21dayContractMeeting.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estatesWith21dayContractMeeting,
      iconName: ExponovaNotificationService.ICON_NAME,
      message: ExponovaNotificationService.NOTIFICATION_MESSAGE,
      url: ExponovaNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { buyer: true },
      pushFeatureFlag: FeatureFlag.SendExponovaPush,
      feedFeatureFlag: FeatureFlag.SendExponovaFeed,
    });
  }
}
