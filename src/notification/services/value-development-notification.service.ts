import { ConsoleLogger, Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import axios from 'axios';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { EstatePriceHistoriesService } from '../../pg/estate-price-histories/estate-price-histories.service';
import { UserService } from '../../pg/user/user.service';
import { sign } from '../../utils/jwt.utils';
import { NotificationService } from '../notification.service';
import { UserNotifierService } from '../user-notifier.service';
import { WealthManagementSummary } from './value-distribution-notification.service';

@Injectable()
export class ValueDevelopmentNotificationService {
  private readonly logger = new ConsoleLogger(ValueDevelopmentNotificationService.name);
  private static readonly NOTIFICATION_MESSAGE = 'Den estimerte verdien på dine boliger er nå [new value]';

  private static readonly ICON_NAME = 'icon_interfaces_empty_heart';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/wealth';

  private static readonly VALUE_DEVELOPMENT_EMAIL_HTML_TEMPLATE_FIRST_PARAGRAPH_ONE_ESTATE = `[name]. Ny måned betyr oppdaterte boligpriser. Den estimerte verdien på boligen din i [address] har endret seg med kr. [valueChange], som betyr at ny estimert verdi er kr. [newValue].`;
  private static readonly VALUE_DEVELOPMENT_EMAIL_HTML_TEMPLATE_FIRST_PARAGRAPH_MULTIPLE_ESTATES = `[user first name]. Ny måned betyr oppdaterte boligpriser. Her er oppdaterte estimat for dine boliger:`;
  private static readonly VALUE_DEVELOPMENT_EMAIL_HTML_TEMPLATE_BODY = `Husk at du kan følge verdiutviklingen på boligen din, måned for måned, i Nordvik-appen. Her finner du også estimerte priser hver måned tilbake til 2022. Du kan i tillegg se belåningsgraden din, og ta kontakt med vår samarbeidspartner Storebrand for å refinanisere betingelsene på boliglånet ditt.<br><br><br><br>Verdiovervåkningen baserer seg på en generell prisutvikling i ditt område, og siste omsetninger av lignende boliger i nærområdt. Dette er ikke en eksakt prisvurdering av din bolig, men en beregning som er basert på utviklingen i boligprisene i henhold til oppdatert boligprisstatistikk. Individuelle forskjeller kan medføre avvik fra gjennomsnittet. Dersom det er gjort endringer eller oppgradering med din bolig vil vi anbefale deg en oppdatert verdivurdering fra en av våre meglere.<br><br>`;
  private static readonly VALUE_DEVELOPMENT_EMAIL_SUBJECT = `Oppdaterte boligpriser`;

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly userService: UserService,
    private readonly notificationService: NotificationService,
    private readonly userNotifierService: UserNotifierService,
    private readonly mailService: MailService,
    private readonly estatePriceHistoriesService: EstatePriceHistoriesService,
  ) {}

  @Cron('00 20 1 * *', {
    name: ValueDevelopmentNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering value development notifications');
    await this.trigger();
    this.logger.log(`Scheduled value development notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const users = await this.userService.findAll();
    this.logger.debug(`There are ${users.length} users`);

    for (const user of users) {
      const token = sign({ userID: user.id, phoneNumber: user.phoneNumber, role: 'user' }, user.passwordCode);
      const valuationUrl = urljoin(this.appConfigService.getBackendUrl(), '/estates/valuation');
      const response = await axios.get<WealthManagementSummary>(valuationUrl, {
        headers: { authorization: `Bearer ${token}` },
        validateStatus: () => true,
      });

      if (response.status !== 200) {
        this.logger.error(`Received error from backend: ${response.status}, ${response.data}`);
        continue;
      }

      if (!response.data?.aggregation?.totalEstimatedValue) {
        continue;
      }

      this.logger.log(
        `Valuation for user ${user.id}: ${JSON.stringify(response.data.aggregation.totalEstimatedValue)}`,
      );

      if (response.data.aggregation.totalEstimatedValue > 0) {
        this.logger.log(`Sending value development notification to user ${user.id}`);

        const message = ValueDevelopmentNotificationService.NOTIFICATION_MESSAGE.replace(
          '[new value]',
          response.data.aggregation?.totalEstimatedValue.toLocaleString().replace(/,/g, ' '),
        );

        await this.userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
          feedFeatureFlag: FeatureFlag.SendValueDevelopmentFeed,
          iconName: ValueDevelopmentNotificationService.ICON_NAME,
          isFeatureEnabled: this.authorizationService.isFeatureEnabled,
          message,
          pushFeatureFlag: FeatureFlag.SendValueDevelopmentPush,
          redirectUrl: ValueDevelopmentNotificationService.NOTIFICATION_REDIRECT_URL,
          userId: user.id,
        });

        const estates = response.data.estates;

        let firstParagraph = '';
        if (estates.length > 1) {
          const multipleEstatesList = [''];
          for (const estate of estates) {
            const { newValue, valueChange } = await getNewValueAndValueChange.call(this, estate.estateId);
            multipleEstatesList.push(
              `<li>${estate.streetAddress}: Endring i verdi = ${valueChange}. Ny verdi = ${newValue}</li>`,
            );
          }
          firstParagraph = `${ValueDevelopmentNotificationService.VALUE_DEVELOPMENT_EMAIL_HTML_TEMPLATE_FIRST_PARAGRAPH_MULTIPLE_ESTATES.replace(
            '[name]',
            user.name || '',
          )}<br><ul>${multipleEstatesList.join('<br>')}}<ul>`;
        } else {
          const { newValue, valueChange } = await getNewValueAndValueChange.call(this, estates[0].estateId);

          firstParagraph =
            ValueDevelopmentNotificationService.VALUE_DEVELOPMENT_EMAIL_HTML_TEMPLATE_FIRST_PARAGRAPH_ONE_ESTATE.replace(
              '[name]',
              user.name || '',
            )
              .replace('[address]', estates[0].streetAddress)
              .replace('[valueChange]', `${valueChange}`)
              .replace('[newValue]', `${newValue}`);
        }

        this.logger.log(`Sending value development email for user: ${user.id}`);
        try {
          const mailMessage = `${firstParagraph}<br><br>${ValueDevelopmentNotificationService.VALUE_DEVELOPMENT_EMAIL_HTML_TEMPLATE_BODY}`;

          await this.mailService.sendMail({
            to: user.email,
            subject: ValueDevelopmentNotificationService.VALUE_DEVELOPMENT_EMAIL_SUBJECT,
            html: mailMessage,
          });
        } catch (e) {
          this.logger.error(`Error sending email to user ${user.email}, error ${e}`);
        }
      } else {
        this.logger.warn(
          `Feed and push notification was not sent to user ${user.id} because the total valuation is less than zero (${response.data.aggregation?.totalEstimatedValue})`,
        );
      }
    }

    async function getNewValueAndValueChange(estateId: string) {
      const estateHistory = await this.estatePriceHistoriesService.getAllEstateHistory(estateId);
      const newValue = estateHistory[estateHistory.length - 1].evPrice;
      const oldValue = estateHistory[estateHistory.length - 2].evPrice;
      const valueChange = newValue - oldValue;
      return { newValue, valueChange };
    }
  }
}
