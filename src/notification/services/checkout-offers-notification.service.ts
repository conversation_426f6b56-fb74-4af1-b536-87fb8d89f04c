import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { set } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class CheckoutOffersNotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'Sjekk ut dine ekslusive tilbud fra våre partnere';

  private static readonly ICON_NAME = 'icon_finance_package';

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services';

  private readonly logger = new ConsoleLogger(CheckoutOffersNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: CheckoutOffersNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled checkoutoffers notifications');
    await this.trigger();
    this.logger.log(`Scheduled checkoutoffers notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const dayBeforeOvertakeEstates = await this.estateModel.find({
      // otp at today 22:00 in vitec ->  otp tomorrow irl
      takeOverDate: set(new Date(), { hours: 22, minutes: 0, milliseconds: 0, seconds: 0 }),
    });

    this.logger.debug(
      `Estates which have OTP tomorrow: ${JSON.stringify(dayBeforeOvertakeEstates.map((estate) => estate.estateId))}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: dayBeforeOvertakeEstates,
      iconName: CheckoutOffersNotificationService.ICON_NAME,
      message: CheckoutOffersNotificationService.NOTIFICATION_MESSAGE,
      url: CheckoutOffersNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { seller: true },
      pushFeatureFlag: FeatureFlag.SendCheckoutOffersPush,
      feedFeatureFlag: FeatureFlag.SendCheckoutOffersFeed,
    });
  }
}
