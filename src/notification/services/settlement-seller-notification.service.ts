import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { startOfDay, sub } from 'date-fns';
import { ParseError, parsePhoneNumberWithError } from 'libphonenumber-js';
import { isNull } from 'lodash';
import { Model } from 'mongoose';
import urljoin from 'url-join';
import AppConfigService from '../../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../../authorization/authorization.service';
import { MailService } from '../../mail/mail.service';
import { MailAuditType } from '../../pg/mail-audit/mail-audit.model';
import { SettlementSeller, SettlementSellerParticipant } from '../../pg/settlement-seller/settlement-seller.model';
import { SettlementSellerService } from '../../pg/settlement-seller/settlement-seller.service';
import { SmsAuditType } from '../../pg/sms-audit/sms-audit.model';
import { BrokerRole, CheckListTag, CheckListValue, Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { Seller, SellerDocument } from '../../sync/schema/seller.schema';
import { populateSettlementMessageTemplate } from '../../utils/message.utils';
import { VitecEstateStatus } from '../../vitec/dto/estate.dto';
import { NotificationService } from '../notification.service';

@Injectable()
export class SettlementSellerNotificationService {
  private readonly logger = new ConsoleLogger(SettlementSellerNotificationService.name);

  private static readonly NOTIFICATION_MESSAGE_TEMPLATE = `Hei,\n\nOppgjørsskjema for salget av [address] er klart til utfylling. Følg linken under for å fylle ut skjemaet. Merk at skjemaet må signeres av alle selgere.\n\n[URL]\n\nMed vennlig hilsen\n\nNordvik`;
  private static readonly NOTIFICATION_MESSAGE_HTML_TEMPLATE = `Oppgjørsskjema for salget av [address] er klart til utfylling. Følg linken under for å fylle ut skjemaet. Merk at skjemaet må signeres av alle selgere.<br><br><a href="[URL]">[URL]</a>`;

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly mailService: MailService,
    private readonly settlementSellerService: SettlementSellerService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
  ) {}

  isSettlementSellerFormCreationEnabled(estate: Estate): boolean {
    if (!estate || estate.status !== VitecEstateStatus.OVERSOLD) {
      return false;
    }
    return this.notificationService.estateHasOneOfTheChecklistsClicked({
      estate,
      tags: [
        CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_SALE,
        CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_SETTLEMENT,
        CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_FORECLOSURE,
        CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_PROJECT,
      ],
    });
  }

  private isSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementSellerSms, estateId);

  private isBrokerSmsEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementSellerBrokerSms, estateId);

  private isEmailEnabled = (estateId: string): boolean =>
    this.authorizationService.isFeatureEnabledForEstate(FeatureFlag.SendSettlementSellerEmail, estateId);

  private async getOrCreateSettlementSellerForm(estateId: string): Promise<SettlementSeller> {
    let settlementSeller = await this.settlementSellerService.getByEstateId(estateId);
    if (isNull(settlementSeller)) {
      try {
        const url = urljoin(this.appConfigService.getBackendUrl(), 'estate', estateId, 'settlement-seller');

        const response = await axios.post<SettlementSeller>(url, {
          validateStatus: () => true,
        });
        settlementSeller = response.data;

        this.logger.log(`Settlement seller form created for estate with estateId: ${estateId}`);
      } catch (error) {
        this.logger.error(`Received error from BE: ${error.response.status}:${JSON.stringify(error.response.data)}`);
      }
    }

    return settlementSeller;
  }

  async createFormAndSendNotificationsIfNotSent(estate: Estate) {
    if (
      !this.isSmsEnabled(estate.estateId) &&
      !this.isEmailEnabled(estate.estateId) &&
      !this.isBrokerSmsEnabled(estate.estateId)
    ) {
      this.logger.warn(
        `Skipping settlement-seller notification sending for estate: ${estate.estateId}. The following feature flags are disabled: ${FeatureFlag.SendSettlementSellerEmail}, ${FeatureFlag.SendSettlementSellerSms}, ${FeatureFlag.SendSettlementSellerBrokerSms}`,
      );
      return;
    }

    const settlementSeller = await this.getOrCreateSettlementSellerForm(estate.estateId);
    if (isNull(settlementSeller)) {
      // some error occured, continue to next estate
      return;
    }

    if (settlementSeller.isNotificationSent || settlementSeller.signingStarted) {
      this.logger.log(
        `Skipping settlement-seller notification sending: the notification is already sent or signing already started for estate: ${estate.estateId}`,
      );
      return;
    }

    // getOrCreateSettlementSellerForm will create the form and the participants on the backend that need to be notified
    const sellers = await this.settlementSellerService.getParticipantsForForm(settlementSeller.id);
    this.logger.log(
      `Found settlement-sellers for estate: ${estate.estateId}, sellers: ${sellers.map((s) => s.id).join(',')}`,
    );

    await this.sendEmailAndSmsNotifications(estate, sellers);
    await this.settlementSellerService.setIsNotificationSent(estate.estateId, true);
  }

  async sendEmailAndSmsNotifications(estate: Estate, sellers: SettlementSellerParticipant[]): Promise<void> {
    const message = populateSettlementMessageTemplate({
      template: SettlementSellerNotificationService.NOTIFICATION_MESSAGE_TEMPLATE,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
      type: 'seller',
    });
    const mailMessage = populateSettlementMessageTemplate({
      template: SettlementSellerNotificationService.NOTIFICATION_MESSAGE_HTML_TEMPLATE,
      address: estate.address.streetAdress,
      estateId: estate.estateId,
      appUrl: this.appConfigService.getAppUrl(),
      type: 'seller',
    });

    const brokerPhoneNumber = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0]
      ?.employee?.mobilePhone;

    // if (brokerPhoneNumber && this.isBrokerSmsEnabled(estate.estateId)) {
    //   await this.sendBrokerSms(estate, brokerPhoneNumber, message);
    // } else {
    //   this.logger.warn(`Skipping settlement-seller broker sms notification sending for estate: ${estate.estateId}.
    //   The following feature flags are disabled: ${FeatureFlag.SendSettlementSellerBrokerSms}`);
    // }

    if (this.isEmailEnabled(estate.estateId)) {
      await this.sendBrokerEmail(estate, brokerPhoneNumber, mailMessage);
    } else {
      this.logger.warn(`Skipping settlement-seller broker email notification sending for estate: ${estate.estateId}.
      The following feature flags are disabled: ${FeatureFlag.SendSettlementSellerEmail}`);
    }

    for (const seller of sellers) {
      if (!seller.phoneNumber) {
        this.logger.warn(`Seller ${seller.id} has no phone number set`);
      } else {
        if (this.isSmsEnabled(estate.estateId)) {
          await this.sendSellerParticipantSms(estate, seller, message);
        } else {
          this.logger.warn(
            `Skipping settlement-seller sms notification sending for estate: ${estate.estateId}, to seller ${seller.phoneNumber}. The following feature flags are disabled: ${FeatureFlag.SendSettlementSellerSms}`,
          );
        }
      }

      if (this.isEmailEnabled(estate.estateId)) {
        await this.sendSellerParticipantEmail(estate, seller, mailMessage);
      } else {
        this.logger.warn(
          `Skipping settlement-seller email notification sending for estate: ${estate.estateId}, to seller ${seller.phoneNumber}. The following feature flags are disabled: ${FeatureFlag.SendSettlementSellerEmail}`,
        );
      }
    }
  }

  private async sendSellerParticipantEmail(estate: Estate, seller: SettlementSellerParticipant, mailMessage: string) {
    this.logger.log(
      `Sending settlement-seller email notification for estate: ${estate.estateId}, to seller ${seller.phoneNumber}`,
    );
    try {
      await this.mailService.sendMail({
        to: seller.email,
        subject: 'Oppgjørsskjema Selger',
        html: mailMessage,
        mailAuditType: MailAuditType.SETTLEMENT,
      });
    } catch (e) {
      this.logger.error(`Error sending email to customer ${seller.email}, error ${e}`);
    }
  }

  private async sendSellerParticipantSms(estate: Estate, seller: SettlementSellerParticipant, message: string) {
    this.logger.log(
      `Sending settlement-seller sms notification for estate: ${estate.estateId}, to seller ${seller.phoneNumber}`,
    );
    try {
      const parsedSellerPhoneNumber = parsePhoneNumberWithError(seller.phoneNumber, 'NO').number;
      const contact = [...estate.sellers, ...estate.proxies].find(
        (b) => parsePhoneNumberWithError(b.mobilePhone, 'NO').number === parsedSellerPhoneNumber,
      );
      await this.notificationService.delayByLastSentSms(seller.phoneNumber, SmsAuditType.SETTLEMENT_SELLER_SMS);

      await this.notificationService.triggerSmsNotification({
        message,
        phoneNumber: seller.phoneNumber,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.SETTLEMENT_SELLER_SMS,
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: contact.contactId,
        },
      });
    } catch (e) {
      this.logger.error(`Error sending SMS to ${seller.phoneNumber}, error ${e instanceof ParseError ? e.message : e}`);
    }
  }

  private async sendBrokerEmail(estate: Estate, brokerPhoneNumber: string, mailMessage: string) {
    const brokerEmail = estate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0]?.employee
      ?.email;
    this.logger.log(
      `Sending settlement-seller broker email notification for estate: ${estate.estateId}, broker: ${brokerPhoneNumber}`,
    );
    await this.mailService
      .sendMail({
        to: brokerEmail,
        subject: `Oppgjørsskjema Selger - ${estate.assignmentNum} ${estate.address.streetAdress}`,
        html: mailMessage,
        mailAuditType: MailAuditType.SETTLEMENT,
      })
      .catch((e) =>
        this.logger.error(
          `Error sending settlement-seller email for estate: ${estate.estateId}, to ${brokerEmail}, error: ${e}`,
        ),
      );
  }

  private async sendBrokerSms(estate: Estate, brokerPhoneNumber: string, message: string) {
    this.logger.log(
      `Sending settlement-seller broker sms notification for estate: ${estate.estateId}, broker: ${brokerPhoneNumber}`,
    );

    await this.notificationService.delayByLastSentSms(brokerPhoneNumber, SmsAuditType.SETTLEMENT_SELLER_SMS);

    await this.notificationService
      .triggerSmsNotification({
        message,
        phoneNumber: brokerPhoneNumber,
        audit: {
          estateId: estate.estateId,
          smsAuditType: SmsAuditType.SETTLEMENT_SELLER_SMS,
        },
        vitecContactOptions: {
          departmentId: estate.departmentId,
          fromEmployeeId: undefined,
          contactId: '',
        },
      })
      .catch((e) =>
        this.logger.error(
          `Error sending settlement-seller sms for estate: ${estate.estateId}, to broker ${brokerPhoneNumber}, error: ${e}`,
        ),
      );
  }

  // This is not Cron, this can only be called through API endpoint
  async trigger(): Promise<void> {
    const startDate = sub(startOfDay(new Date()), { days: 3 });

    const estates = await this.estateModel.find({
      status: VitecEstateStatus.OVERSOLD,
      'checkList.checkListItems': {
        $elemMatch: {
          tags: {
            $in: [
              CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_SALE,
              CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_SETTLEMENT,
              CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_FORECLOSURE,
              CheckListTag.ACTIVATE_SETTLEMENT_SELLER_FOR_PROJECT,
            ],
          },
          value: CheckListValue.YES,
          changedDate: {
            $gte: startDate,
          },
        },
      },
    });

    this.logger.log(
      `Estates that we are sending settlement-seller notifications: ${JSON.stringify(
        estates.map((estate) => estate.estateId),
      )}`,
    );

    for (const estate of estates) {
      try {
        await this.createFormAndSendNotificationsIfNotSent(estate);
      } catch (e) {
        this.logger.error(`Error sending settlement-seller notifications to estate: ${estate.estateId}, error ${e}`);
      }
    }
  }
}
