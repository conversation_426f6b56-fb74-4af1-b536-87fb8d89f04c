import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron } from '@nestjs/schedule';
import { add, startOfDay } from 'date-fns';
import { Model } from 'mongoose';
import { FeatureFlag } from '../../authorization/authorization.service';
import { Estate, EstateDocument } from '../../sync/schema/estate.schema';
import { UserNotifierService } from '../user-notifier.service';

@Injectable()
export class HmhBuyerMovingNotificationService {
  private static readonly NOTIFICATION_MESSAGE = 'HMH gjør det enkelt å flytte inn i din nye bolig';

  private static readonly ICON_NAME = 'icon_finance_package'; // box icon

  private static readonly NOTIFICATION_REDIRECT_URL = '/customer/services/service-providers/hmh-moving';

  private readonly logger = new ConsoleLogger(HmhBuyerMovingNotificationService.name);

  constructor(
    private readonly estateNotifierService: UserNotifierService,
    @InjectModel(Estate.name) private estateModel: Model<EstateDocument>,
  ) {}

  @Cron('00 20 * * *', {
    name: HmhBuyerMovingNotificationService.name,
    timeZone: 'Europe/Oslo',
  })
  private async onScheduledTrigger() {
    this.logger.log('Triggering scheduled hmhmoving notifications');
    await this.trigger();
    this.logger.log(`Scheduled hmhmoving notifications are triggered`);
  }

  async trigger(): Promise<void> {
    const startDate = add(startOfDay(new Date()), { days: 27, hours: 22 });
    const endDate = add(startDate, { days: 1 });
    const estates4weeksBeforeHandover = await this.estateModel.find({
      takeOverDate: {
        $gte: startDate,
        $lt: endDate,
      },
    });

    this.logger.debug(
      `Estates which have takeover in 28 days: ${JSON.stringify(
        estates4weeksBeforeHandover.map((estate) => estate.estateId),
      )}`,
    );

    await this.estateNotifierService.notifyEstates({
      estates: estates4weeksBeforeHandover,
      iconName: HmhBuyerMovingNotificationService.ICON_NAME,
      message: HmhBuyerMovingNotificationService.NOTIFICATION_MESSAGE,
      url: HmhBuyerMovingNotificationService.NOTIFICATION_REDIRECT_URL,
      contactType: { buyer: true },
      pushFeatureFlag: FeatureFlag.SendHmhBuyerMovingPush,
      feedFeatureFlag: FeatureFlag.SendHmhBuyerMovingFeed,
    });
  }
}
