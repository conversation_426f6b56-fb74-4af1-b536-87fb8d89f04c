import { ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { parsePhoneNumber } from 'libphonenumber-js';
import { Model } from 'mongoose';
import AuthorizationService, { FeatureFlag } from '../authorization/authorization.service';
import { UserNotificationGroupsMap } from '../pg/user/user.model';
import { UserService } from '../pg/user/user.service';
import { Buyer, BuyerDocument } from '../sync/schema/buyer.schema';
import { EstateDocument } from '../sync/schema/estate.schema';
import { Proxy, ProxyDocument } from '../sync/schema/proxy.schema';
import { Seller, SellerDocument } from '../sync/schema/seller.schema';
import { NotificationService } from './notification.service';

type EstateContactType = { seller?: boolean; buyer?: boolean; proxy?: boolean } & (
  | { seller: boolean }
  | { buyer: boolean }
  | { proxy: boolean }
);

type NotifyEstates = (params: {
  estates: EstateDocument[];
  message: string;
  url: string;
  iconName: string;
  pushFeatureFlag: FeatureFlag;
  feedFeatureFlag: FeatureFlag;
  contactType: EstateContactType;
  urlReplacer?: (url: string, estate: EstateDocument) => string;
}) => Promise<void>;

type NotifyUser = (params: {
  message: string;
  userId: string;
  iconName: string;
  redirectUrl: string;
  pushFeatureFlag: FeatureFlag;
  feedFeatureFlag: FeatureFlag;
  isFeatureEnabled: (featureFlag: FeatureFlag) => boolean;
  deletePreviousFeed?: boolean;
}) => Promise<void>;

type BroadcastPushNotifyUsersWithoutException = (params: {
  title?: string;
  message: string;
  userIds: string[];
  redirectUrl: string;
  pushFeatureFlag: FeatureFlag;
}) => Promise<void>;

type NotifyContacts = (params: {
  contacts: { mobilePhone: string; contactId: string }[];
  message: string;
  iconName: string;
  redirectUrl: string;
  pushFeatureFlag: FeatureFlag;
  feedFeatureFlag: FeatureFlag;
  isFeatureEnabled: (featureFlag: FeatureFlag) => boolean;
}) => Promise<void>;
interface UserNotifierServiceInterface {
  notifyEstates: NotifyEstates;
  notifyUserIfHisSettingsAreEnabledForThisFeature: NotifyUser;
  broadcastPushNotifyUsers: BroadcastPushNotifyUsersWithoutException;

  notifyContacts: NotifyContacts;
}

@Injectable()
export class UserNotifierService implements UserNotifierServiceInterface {
  private readonly logger = new ConsoleLogger(UserNotifierService.name);

  constructor(
    private readonly authorizationService: AuthorizationService,
    private readonly notificationService: NotificationService,
    private readonly userService: UserService,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
    @InjectModel(Seller.name) private sellerModel: Model<SellerDocument>,
    @InjectModel(Proxy.name) private proxyModel: Model<ProxyDocument>,
  ) {}

  public notifyUserIfHisSettingsAreEnabledForThisFeature: NotifyUser = async ({
    message,
    userId,
    iconName,
    redirectUrl,
    pushFeatureFlag,
    feedFeatureFlag,
    isFeatureEnabled,
    deletePreviousFeed = false,
  }) => {
    if (isFeatureEnabled(pushFeatureFlag)) {
      const appUser = await this.userService.findOne(userId);

      if (appUser) {
        const disabledGroups = Object.keys(appUser.pushNotificationSettings).filter(
          (key) => !appUser.pushNotificationSettings[key],
        );
        const disabledNotifications = disabledGroups.reduce((acc, key) => {
          return [...acc, ...UserNotificationGroupsMap[key]];
        }, []);
        if (disabledNotifications.includes(pushFeatureFlag)) {
          this.logger.warn(
            `${pushFeatureFlag}: push was not sent for user with userId: ${userId}: because the corresponding option is disabled in user settings`,
          );
          return;
        }
      }

      await this.notificationService.triggerPushNotification({
        userIDs: [userId],
        message,
        redirectUrl,
        pushType: pushFeatureFlag,
      });
      this.logger.log(`${pushFeatureFlag}: push was sent for user with userId: ${userId}`);
    } else {
      this.logger.warn(
        `${pushFeatureFlag}: push was not sent for user with userId: ${userId}: because the corresponding feature flag is disabled: ${pushFeatureFlag}`,
      );
    }

    if (isFeatureEnabled(feedFeatureFlag)) {
      if (deletePreviousFeed) {
        await this.notificationService.deleteFeedNotifications(userId, redirectUrl);
        this.logger.warn(`${feedFeatureFlag}: previous feeds deleted for user with userId: ${userId}`);
      }
      await this.notificationService.triggerFeedNotification(userId, message, iconName, redirectUrl);
      this.logger.log(`${feedFeatureFlag}: feed was sent for user with userId: ${userId}`);
    } else {
      this.logger.warn(
        `${feedFeatureFlag}: feed was not sent for user with userId: ${userId}: because the corresponding feature flag is disabled: ${feedFeatureFlag}`,
      );
    }
  };

  public broadcastPushNotifyUsers: BroadcastPushNotifyUsersWithoutException = async ({
    title,
    message,
    userIds,
    redirectUrl,
    pushFeatureFlag,
  }) => {
    await this.notificationService.triggerPushNotification({
      title,
      userIDs: userIds,
      message,
      redirectUrl,
      pushType: pushFeatureFlag,
    });
    this.logger.log(`${pushFeatureFlag}: push was sent for users(${userIds.length})`);
  };

  public notifyContacts: NotifyContacts = async ({
    contacts,
    message,
    redirectUrl,
    iconName,
    pushFeatureFlag,
    feedFeatureFlag,
    isFeatureEnabled,
  }) => {
    for (const contact of contacts) {
      if (!contact.mobilePhone) {
        this.logger.warn(`Contact with contactId ${contact.contactId} has no phone number set`);
        continue;
      }

      const phoneNumber = parsePhoneNumber(contact.mobilePhone || '', 'NO');
      if (!phoneNumber || !phoneNumber.isValid()) {
        this.logger.warn(`Contact ${contact.contactId} has an invalid phone number set: ${contact.mobilePhone}`);
        continue;
      }

      const appUser = await this.userService.findByPhoneNumber(phoneNumber.number);

      if (!appUser) {
        this.logger.warn(`App user with phone number ${phoneNumber.number} was not found`);
        continue;
      }
      this.logger.log(`App user with phone number ${phoneNumber.number} found, userId: ${appUser.id}`);

      await this.notifyUserIfHisSettingsAreEnabledForThisFeature({
        feedFeatureFlag,
        iconName,
        isFeatureEnabled,
        message,
        userId: appUser.id,
        pushFeatureFlag,
        redirectUrl,
      });
    }
  };

  public notifyEstates: NotifyEstates = async ({
    estates,
    message,
    url,
    iconName,
    pushFeatureFlag,
    feedFeatureFlag,
    contactType,
    urlReplacer,
  }) => {
    const getContacts = async (contactType: EstateContactType, estate: EstateDocument) => {
      const buyerIds = estate.buyers.map((s) => s.contactId);
      const sellerIds = estate.sellers.map((s) => s.contactId);
      const proxyIds = estate.proxies.map((p) => p.contactId);
      const buyers = contactType.buyer ? await this.buyerModel.find({ contactId: { $in: buyerIds } }) : [];
      const sellers = contactType.seller ? await this.sellerModel.find({ contactId: { $in: sellerIds } }) : [];
      const proxies = contactType.proxy ? await this.proxyModel.find({ contactId: { $in: proxyIds } }) : [];
      return [...buyers, ...sellers, ...proxies];
    };

    for (const estate of estates) {
      const contacts = await getContacts(contactType, estate);
      const contactIdsString = contacts.map((c) => c.id).join(', ');
      this.logger.debug(`Found ${contacts.length} contacts (${contactIdsString}) for estate ${estate.estateId}`);

      const redirectUrl = urlReplacer ? urlReplacer(url, estate) : url;
      const isFeatureEnabled = (featureFlag: FeatureFlag) =>
        this.authorizationService.isFeatureEnabledForEstate(featureFlag, estate.estateId);

      await this.notifyContacts({
        contacts,
        feedFeatureFlag,
        iconName,
        isFeatureEnabled,
        message,
        pushFeatureFlag,
        redirectUrl,
      });
    }
  };
}
