import { <PERSON>, ConsoleLogger, Controller, Post } from '@nestjs/common';
import { FeatureFlag } from '../authorization/authorization.service';
import Protected from '../authorization/protected.decorator';
import { SmsAuditType } from '../pg/sms-audit/sms-audit.model';
import { NotificationService } from './notification.service';
import { AfterBiddingNotificationService } from './services/after-bidding-notification.service';
import { AfterLaunchNotificationService } from './services/after-launch-notification.service';
import { AfterSaleNotificationService } from './services/after-sale-notification.service';
import { AfterSigningNotificationService } from './services/after-signing-notification.service';
import { BeforeArchiveNotificationService } from './services/before-archive-notification.service';
import {
  BrokerNewsOrIncidentNotificationService,
  CraftEntryType,
} from './services/broker-news-or-incident-notification.service';
import { ByggstartNotificationService } from './services/byggstart-notification.service';
import { CheckoutOffersNotificationService } from './services/checkout-offers-notification.service';
import { DailyPriceGuessingGameEstateNotificationService } from './services/daily-price-guessing-game-estate-notification.service';
import { EkstraNotificationService } from './services/ekstra-notification.service';
import { EtakstExpiredNotificationService } from './services/etakst-expired-notification.service';
import { Exponova2NotificationService } from './services/exponova-2-notification.service';
import { ExponovaNotificationService } from './services/exponova-notification.service';
import { FavoriteEstateListedNotificationService } from './services/favorite-estate-listed-notification.service';
import { HmhBuyerMoving2NotificationService } from './services/hmh-buyer-moving-notification-2.service';
import { HmhBuyerMovingNotificationService } from './services/hmh-buyer-moving-notification.service';
import { HmhCleaningNotificationService } from './services/hmh-cleaning-notification.service';
import { HmhSellerMovingNotificationService } from './services/hmh-seller-moving-notification.service';
import { OvertakeProtocolNotificationService } from './services/overtake-protocol-notification.service';
import { PoliticallyExposedPersonFormNotificationService } from './services/politically-exposed-person-form-notification.service';
import { PremarketEstateNotificationService } from './services/premarket-estate-notification.service';
import { SettlementBuyerNotificationService } from './services/settlement-buyer-notification.service';
import { SettlementSellerNotificationService } from './services/settlement-seller-notification.service';
import { SmsDuplicationNotificationService } from './services/sms-duplication-notification.service';
import { SettlementSigningSMSReminderNotificationService } from './services/sms-settlement-signing-reminder-notification.service';
import { StatisticsNotification } from './services/statistics-notification.service';
import { StorebrandAfterBiddingNotificationService } from './services/storebrand-after-bidding-notification.service';
import { StorebrandBuyerNotificationService } from './services/storebrand-buyer-notification.service';
import { StorebrandSellerNotificationService } from './services/storebrand-seller-notification.service';
import { UnsignedOtpDocumentNotificationService } from './services/unsigned-otp-document-notification.service';
import { UnsignedSettlementDocumentNotificationService } from './services/unsigned-settlement-document-notification.service';
import { ValueDevelopmentNotificationService } from './services/value-development-notification.service';
import { ValueDistributionNotification } from './services/value-distribution-notification.service';
import { VerisureBuyerNotificationService } from './services/verisure-buyer-notification.service';

@Controller('notification')
export class NotificationController {
  constructor(
    private readonly notificationService: NotificationService,
    private readonly overtakeProtocolNotificationService: OvertakeProtocolNotificationService,
    private readonly storebrandBuyerNotificationService: StorebrandBuyerNotificationService,
    private readonly storebrandSellerNotificationService: StorebrandSellerNotificationService,
    private readonly ekstraNotificationService: EkstraNotificationService,
    private readonly verisureBuyerNotificationService: VerisureBuyerNotificationService,
    private readonly byggstartNotificationService: ByggstartNotificationService,
    private readonly exponovaNotificationService: ExponovaNotificationService,
    private readonly hmhSellerMovingNotificationService: HmhSellerMovingNotificationService,
    private readonly hmhBuyerMovingNotificationService: HmhBuyerMovingNotificationService,
    private readonly hmhCleaningNotificationService: HmhCleaningNotificationService,
    private readonly favoriteEstateListedNotificationService: FavoriteEstateListedNotificationService,
    private readonly premarketEstateNotificationService: PremarketEstateNotificationService,
    private readonly etakstExpiredNotificationService: EtakstExpiredNotificationService,
    private readonly valueDevelopmentNotificationService: ValueDevelopmentNotificationService,
    private readonly hmhBuyerMoving2NotificatoinService: HmhBuyerMoving2NotificationService,
    private readonly valueDistributionNotificationService: ValueDistributionNotification,
    private readonly statisticsNotificationService: StatisticsNotification,
    private readonly checkoutOffersNotificationService: CheckoutOffersNotificationService,
    private readonly storebrandAfterBiddingNotificationService: StorebrandAfterBiddingNotificationService,
    private readonly exponova2NotificationService: Exponova2NotificationService,
    private readonly afterSaleNotificationService: AfterSaleNotificationService,
    private readonly afterSigningNotificationService: AfterSigningNotificationService,
    private readonly brokerNewsOrIncidentNotificationService: BrokerNewsOrIncidentNotificationService,
    private readonly unsignedOtpDocumentNotificationService: UnsignedOtpDocumentNotificationService,
    private readonly unsignedSettlementNotificationService: UnsignedSettlementDocumentNotificationService,
    private readonly afterBiddingNotificationService: AfterBiddingNotificationService,
    private readonly afterLaunchNotificationService: AfterLaunchNotificationService,
    private readonly settlementSellerNotificationService: SettlementSellerNotificationService,
    private readonly settlementBuyerNotificationService: SettlementBuyerNotificationService,
    private readonly smsDuplicationNotificationService: SmsDuplicationNotificationService,
    private readonly beforeArchiveNotificationService: BeforeArchiveNotificationService,
    private readonly pepNotificationService: PoliticallyExposedPersonFormNotificationService,
    private readonly smsSigningReminderNotificationService: SettlementSigningSMSReminderNotificationService,
    private readonly priceGuessingNotificationService: DailyPriceGuessingGameEstateNotificationService,
  ) {}

  @Post('/trigger/push')
  @Protected()
  async triggerPushNotification(@Body() body: { userID: string; message: string; redirectUrl: string }) {
    return this.notificationService.triggerPushNotification({
      userIDs: [body.userID],
      message: body.message,
      redirectUrl: body.redirectUrl,
      pushType: '/trigger/push' as FeatureFlag,
    });
  }

  @Post('/trigger/feed')
  @Protected()
  async triggerFeedNotification(
    @Body() body: { userID: string; message: string; iconName: string; redirectUrl: string },
  ) {
    return this.notificationService.triggerFeedNotification(body.userID, body.message, body.iconName, body.redirectUrl);
  }

  @Post('/trigger/sms')
  @Protected()
  async triggerSmsNotification(
    @Body()
    body: {
      phoneNumber: string;
      message: string;
      estateId: string;
      contactId: string;
      departmentId: number;
      fromEmployeeId: string;
    },
  ) {
    return this.notificationService.triggerSmsNotification({
      phoneNumber: body.phoneNumber,
      message: body.message,
      audit: { smsAuditType: SmsAuditType.SEND_SMS, estateId: body.estateId },
      vitecContactOptions: {
        contactId: body.contactId,
        departmentId: body.departmentId,
        fromEmployeeId: undefined,
      },
    });
  }

  @Post('/trigger/overtake-protocol')
  @Protected()
  async triggerOvertakeProtocolNotifications() {
    return this.overtakeProtocolNotificationService.trigger();
  }

  @Post('/trigger/overtake-protocol/:estateId')
  @Protected()
  async triggerOvertakeProtocolNotificationForEstate(@Body() body: { estateId: string }) {
    return this.overtakeProtocolNotificationService.triggerForEstate(body.estateId);
  }

  @Post('/trigger/storebrand/buyer')
  @Protected()
  async triggerStorebrandBuyerNotifications() {
    return this.storebrandBuyerNotificationService.trigger();
  }

  @Post('/trigger/storebrand/seller')
  @Protected()
  async triggerStorebrandSellerNotifications() {
    return this.storebrandSellerNotificationService.trigger();
  }

  @Post('/trigger/ekstra')
  @Protected()
  async triggerEkstraNotifications() {
    return this.ekstraNotificationService.trigger();
  }

  @Post('/trigger/verisure/buyer')
  @Protected()
  async triggerVerisureBuyerNotifications() {
    return this.verisureBuyerNotificationService.trigger();
  }

  @Post('/trigger/byggstart')
  @Protected()
  async triggerByggstartNotifications() {
    return this.byggstartNotificationService.trigger();
  }

  @Post('/trigger/exponova')
  @Protected()
  async triggerExponovaNotifications() {
    return this.exponovaNotificationService.trigger();
  }

  @Post('/trigger/hmh-moving/seller')
  @Protected()
  async triggerHmhMovingNotifications() {
    return this.hmhSellerMovingNotificationService.trigger();
  }

  @Post('/trigger/hmh-moving/buyer')
  @Protected()
  async triggerHmhMovingBuyerNotifications() {
    return this.hmhBuyerMovingNotificationService.trigger();
  }

  @Post('/trigger/hmh-cleaning/seller')
  @Protected()
  async triggerHmhCleaningNotifications() {
    return this.hmhCleaningNotificationService.trigger();
  }

  @Post('/trigger/favorite-estate-listed')
  @Protected()
  async triggerFavoriteEstateListedNotifications() {
    return this.favoriteEstateListedNotificationService.trigger();
  }

  @Post('/trigger/premarket-estate')
  @Protected()
  async triggerPremarketEstateListedNotifications() {
    return this.premarketEstateNotificationService.trigger();
  }

  @Post('/trigger/etakst-expired')
  @Protected()
  async triggerEtakstExpiredNotifications() {
    return this.etakstExpiredNotificationService.trigger();
  }

  @Post('/trigger/value-development')
  @Protected()
  async triggerValueDevelopmentNotifications() {
    return this.valueDevelopmentNotificationService.trigger();
  }

  @Post('/trigger/value-distribution')
  @Protected()
  async triggerValueDistributionNotifications() {
    return this.valueDistributionNotificationService.trigger();
  }

  @Post('/trigger/hmh-moving/buyer-2')
  @Protected()
  async triggerhmhBuyerMoving2Notificatoin() {
    return this.hmhBuyerMoving2NotificatoinService.trigger();
  }

  @Post('/trigger/statistics')
  @Protected()
  async triggerStatisticsNotificatoin() {
    return this.statisticsNotificationService.trigger();
  }

  @Post('/trigger/checkout-offers')
  @Protected()
  async triggerCheckoutOffersNotificatoin() {
    return this.checkoutOffersNotificationService.trigger();
  }

  @Post('/trigger/storebrand/after-bidding')
  @Protected()
  async triggerStorebrandAfterBiddingNotification() {
    return this.storebrandAfterBiddingNotificationService.trigger();
  }

  @Post('/trigger/exponova/2')
  @Protected()
  async triggerExponova2Notification() {
    return this.exponova2NotificationService.trigger();
  }

  @Post('/trigger/after-sale')
  @Protected()
  async triggerAfterSaleNotification() {
    return this.afterSaleNotificationService.trigger();
  }

  @Post('/trigger/after-signing')
  @Protected()
  async triggerAfterSigningNotification() {
    return this.afterSigningNotificationService.trigger();
  }

  @Post('/trigger/unsigned-otp-document')
  @Protected()
  async triggerUnsignedOtpDocumentNotification() {
    return this.unsignedOtpDocumentNotificationService.trigger();
  }

  @Post('/trigger/unsigned-settlement-document')
  @Protected()
  async triggerUnsignedSettlementDocumentNotification() {
    return this.unsignedSettlementNotificationService.trigger();
  }

  @Post('/trigger/after-bidding')
  @Protected()
  async triggerAfterBiddingNotification() {
    return this.afterBiddingNotificationService.trigger();
  }

  @Post('/trigger/after-launch')
  @Protected()
  async triggerAfterLaunchNotification() {
    return this.afterLaunchNotificationService.trigger();
  }

  @Post('/trigger/settlement-seller')
  @Protected()
  async triggerSettlementSellerNotificationService() {
    return this.settlementSellerNotificationService.trigger();
  }

  @Post('/trigger/settlement-buyer')
  @Protected()
  async triggerSettlementBuyerNotificationService() {
    return this.settlementBuyerNotificationService.trigger();
  }

  @Post('/trigger/settlement-buyer-project')
  @Protected()
  async triggerSettlementBuyerProjectNotificationService() {
    return this.settlementBuyerNotificationService.triggerProject();
  }

  @Post('/trigger/sms-duplication')
  @Protected()
  async triggerSmsDuplication() {
    return this.smsDuplicationNotificationService.trigger();
  }

  @Post('/trigger/before-archive')
  @Protected()
  async triggerBeforeArchive() {
    return this.beforeArchiveNotificationService.trigger();
  }

  @Post('/trigger/pep-seller')
  @Protected()
  async triggerPepSeller() {
    return this.pepNotificationService.triggerSeller();
  }

  @Post('/trigger/sms-signing-reminder')
  @Protected()
  async triggerSmsSigningReminder() {
    return this.smsSigningReminderNotificationService.trigger();
  }

  @Post('/trigger/price-guessing/estate-selection')
  @Protected()
  async triggerPriceGuessingEstateSelection() {
    return this.priceGuessingNotificationService.triggerEstateSelectionForDaily();
  }

  @Post('/trigger/price-guessing/notification')
  @Protected()
  async triggerPriceGuessingEstateNotificationSending() {
    return this.priceGuessingNotificationService.triggerNotification();
  }

  @Post('/trigger/broker/craft-update')
  @Protected()
  async triggerCraftEntryWasUpdated(
    @Body()
    craftWebhookData: {
      entryId: number;
      type: CraftEntryType;
      debug: boolean;
      testUserIds?: string[];
      employeeIds?: string[];
    },
  ) {
    const logger = new ConsoleLogger(BrokerNewsOrIncidentNotificationService.name);
    logger.log('[Broker Push Notification]:[/craft-update]', JSON.stringify(craftWebhookData));

    if (craftWebhookData?.entryId) {
      return this.brokerNewsOrIncidentNotificationService.trigger({
        entryId: craftWebhookData.entryId,
        testUserIds: craftWebhookData.testUserIds,
        employeeIds: craftWebhookData.employeeIds,
        type: craftWebhookData.type,
        debug: craftWebhookData.debug,
      });
    }
  }
}
