import { FeatureFlag } from '../authorization/authorization.service';
import { mockAuthorizationServiceFactory } from '../mock-classes/mock-authorization-service';
import { mockModelFactory } from '../mock-classes/mock-model';
import { mockUserServiceFactory } from '../mock-classes/mock-user-service';
import { BuyerDocument } from '../sync/schema/buyer.schema';
import { ProxyDocument } from '../sync/schema/proxy.schema';
import { SellerDocument } from '../sync/schema/seller.schema';
import { NotificationService } from './notification.service';
import { UserNotifierService } from './user-notifier.service';

describe('notifyUser', () => {
  const mockTriggerPushNotification = jest.fn();
  const mockTriggerFeedNotification = jest.fn();
  const mockDeleteFeedNotifications = jest.fn();
  const mockNotificationService = {
    ...mockUserServiceFactory(),
    triggerPushNotification: mockTriggerPushNotification,
    triggerFeedNotification: mockTriggerFeedNotification,
    deleteFeedNotifications: mockDeleteFeedNotifications,
  } as unknown as NotificationService;

  const userNotifierService = new UserNotifierService(
    mockAuthorizationServiceFactory(),
    mockNotificationService,
    mockUserServiceFactory(),
    mockModelFactory<BuyerDocument>(),
    mockModelFactory<SellerDocument>(),
    mockModelFactory<ProxyDocument>(),
  );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call both feed and push triggers if feature-flags are enabled', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      isFeatureEnabled: (_) => true,
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockTriggerPushNotification).toHaveBeenCalledTimes(1);
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(1);
  });

  it('should not call feed or push triggers if feature-flags are disabled', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      isFeatureEnabled: (_) => false,
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockTriggerPushNotification).toHaveBeenCalledTimes(0);
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(0);
  });

  it('should only call the feed trigger', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      isFeatureEnabled: (feat) => feat === ('FEED' as FeatureFlag),
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockTriggerPushNotification).toHaveBeenCalledTimes(0);
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(1);
  });

  it('should only call the push trigger', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      isFeatureEnabled: (feat) => feat === ('PUSH' as FeatureFlag),
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockTriggerPushNotification).toHaveBeenCalledTimes(1);
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(0);
  });

  it('should delete the previous feeds if the deletePreviousFeed param is set to true', async () => {
    mockDeleteFeedNotifications;
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      isFeatureEnabled: (feat) => feat === ('FEED' as FeatureFlag),
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
      deletePreviousFeed: true,
    });
    expect(mockDeleteFeedNotifications).toHaveBeenCalledTimes(1);
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(1);
  });

  it('should not delete the previous feeds if the deletePreviousFeed param is not defined', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      isFeatureEnabled: (feat) => feat === ('FEED' as FeatureFlag),
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockDeleteFeedNotifications).toHaveBeenCalledTimes(0);
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(1);
  });

  it('should call the push trigger with the correct values', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      isFeatureEnabled: (_) => true,
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockTriggerPushNotification).toHaveBeenCalledTimes(1);
    expect(mockTriggerPushNotification).toHaveBeenCalledWith({
      userIDs: ['id'],
      message: 'message',
      redirectUrl: 'url',
      pushType: 'PUSH' as FeatureFlag,
    });
  });

  it('should call the feed trigger with the correct values', async () => {
    await userNotifierService.notifyUserIfHisSettingsAreEnabledForThisFeature({
      feedFeatureFlag: 'FEED' as FeatureFlag,
      iconName: 'icon',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      isFeatureEnabled: (_) => true,
      message: 'message',
      pushFeatureFlag: 'PUSH' as FeatureFlag,
      redirectUrl: 'url',
      userId: 'id',
    });
    expect(mockTriggerFeedNotification).toHaveBeenCalledTimes(1);
    expect(mockTriggerFeedNotification).toHaveBeenCalledWith('id', 'message', 'icon', 'url');
  });
});
