import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import AppConfigModule from '../app-config/app-config.module';
import AuthorizationModule from '../authorization/authorization.module';
import { CraftCMSService } from '../craft-cms/craft-cms.service';
import EiendomsverdiModule from '../eiendomsverdi/eiendomsverdi.module';
import MailModule from '../mail/mail.module';
import MongoModule from '../mongo/mongo.module';
import NordvikboligApiModule from '../norvikbolig-api/nordvikbolig-api.module';
import AreasModule from '../pg/area/area.module';
import EstatePriceHistoriesModule from '../pg/estate-price-histories/estate-price-histories.module';
import EstatesModule from '../pg/estate/estate.module';
import FavoritesModule from '../pg/favorite/favorite.module';
import FeedsModule from '../pg/feed/feed.module';
import OvertakeProtocolParticipantModule from '../pg/overtake-protocol-participant/overtake-protocol-participant.module';
import OvertakeProtocolModule from '../pg/overtake-protocol/overtake-protocol.module';
import PEPFormModule from '../pg/pep-form/pep-form.module';
import PriceGuessingModule from '../pg/price-guessing-estates/price-guessing-estates.module';
import SettlementBuyerModule from '../pg/settlement-buyer/settlement-buyer.module';
import SettlementSellerModule from '../pg/settlement-seller/settlement-seller.module';
import SmsAuditModule from '../pg/sms-audit/sms-audit.module';
import UserActivityModule from '../pg/user-activity/user-activity.module';
import UserFirebaseTokenModule from '../pg/user-firebase-token/user-firebase-token.module';
import UserOptionsModule from '../pg/user-options/user-options.module';
import UsersModule from '../pg/user/user.module';
import UserAreaModule from '../pg/userarea/userarea.module';
import SlackModule from '../slack/slack.module';
import { Buyer, BuyerSchema } from '../sync/schema/buyer.schema';
import { Contact, ContactSchema } from '../sync/schema/contact.schema';
import { Department, DepartmentSchema } from '../sync/schema/department.schema';
import { Employee, EmployeeSchema } from '../sync/schema/employee.schema';
import { Estate, EstateSchema } from '../sync/schema/estate.schema';
import { Proxy, ProxySchema } from '../sync/schema/proxy.schema';
import { Seller, SellerSchema } from '../sync/schema/seller.schema';
import { Tip, TipSchema } from '../sync/schema/tip.schema';
import TwilioModule from '../twilio/twilio.module';
import VitecModule from '../vitec/vitec.module';
import FirebaseMessagingModule from './firebase-messaging.module';
import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';
import { AfterBiddingNotificationService } from './services/after-bidding-notification.service';
import { AfterLaunchNotificationService } from './services/after-launch-notification.service';
import { AfterSaleNotificationService } from './services/after-sale-notification.service';
import { AfterSigningNotificationService } from './services/after-signing-notification.service';
import { BeforeArchiveNotificationService } from './services/before-archive-notification.service';
import { BrokerNewsOrIncidentNotificationService } from './services/broker-news-or-incident-notification.service';
import { ByggstartNotificationService } from './services/byggstart-notification.service';
import { CheckoutOffersNotificationService } from './services/checkout-offers-notification.service';
import { DailyPriceGuessingGameEstateNotificationService } from './services/daily-price-guessing-game-estate-notification.service';
import { EkstraNotificationService } from './services/ekstra-notification.service';
import { EtakstExpiredNotificationService } from './services/etakst-expired-notification.service';
import { Exponova2NotificationService } from './services/exponova-2-notification.service';
import { ExponovaNotificationService } from './services/exponova-notification.service';
import { FavoriteEstateListedNotificationService } from './services/favorite-estate-listed-notification.service';
import { HmhBuyerMoving2NotificationService } from './services/hmh-buyer-moving-notification-2.service';
import { HmhBuyerMovingNotificationService } from './services/hmh-buyer-moving-notification.service';
import { HmhCleaningNotificationService } from './services/hmh-cleaning-notification.service';
import { HmhSellerMovingNotificationService } from './services/hmh-seller-moving-notification.service';
import { OvertakeProtocolNotificationService } from './services/overtake-protocol-notification.service';
import { PoliticallyExposedPersonFormNotificationService } from './services/politically-exposed-person-form-notification.service';
import { PremarketEstateNotificationService } from './services/premarket-estate-notification.service';
import { SettlementBuyerNotificationService } from './services/settlement-buyer-notification.service';
import { SettlementSellerNotificationService } from './services/settlement-seller-notification.service';
import { SmsDuplicationNotificationService } from './services/sms-duplication-notification.service';
import { SettlementSigningSMSReminderNotificationService } from './services/sms-settlement-signing-reminder-notification.service';
import { StatisticsNotification } from './services/statistics-notification.service';
import { StorebrandAfterBiddingNotificationService } from './services/storebrand-after-bidding-notification.service';
import { StorebrandBuyerNotificationService } from './services/storebrand-buyer-notification.service';
import { StorebrandSellerNotificationService } from './services/storebrand-seller-notification.service';
import { UnsignedOtpDocumentNotificationService } from './services/unsigned-otp-document-notification.service';
import { UnsignedSettlementDocumentNotificationService } from './services/unsigned-settlement-document-notification.service';
import { ValueDevelopmentNotificationService } from './services/value-development-notification.service';
import { ValueDistributionNotification } from './services/value-distribution-notification.service';
import { VerisureBuyerNotificationService } from './services/verisure-buyer-notification.service';
import { UserNotifierService } from './user-notifier.service';

@Module({
  imports: [
    AppConfigModule,
    AuthorizationModule,
    UsersModule,
    MongoModule,
    NordvikboligApiModule,
    TwilioModule,
    FeedsModule,
    UserAreaModule,
    AreasModule,
    UserFirebaseTokenModule,
    FavoritesModule,
    UserOptionsModule,
    EstatesModule,
    UserActivityModule,
    EiendomsverdiModule,
    SmsAuditModule,
    SettlementSellerModule,
    SettlementBuyerModule,
    SlackModule,
    MailModule,
    OvertakeProtocolParticipantModule,
    OvertakeProtocolModule,
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: Estate.name, schema: EstateSchema },
      { name: Department.name, schema: DepartmentSchema },
      { name: Employee.name, schema: EmployeeSchema },
      { name: Seller.name, schema: SellerSchema },
      { name: Buyer.name, schema: BuyerSchema },
      { name: Proxy.name, schema: ProxySchema },
      { name: Contact.name, schema: ContactSchema },
      { name: Tip.name, schema: TipSchema },
    ]),
    ScheduleModule.forRoot(),
    FirebaseMessagingModule,
    PEPFormModule,
    PriceGuessingModule,
    VitecModule,
    EstatePriceHistoriesModule,
  ],
  providers: [
    NotificationService,
    StorebrandBuyerNotificationService,
    OvertakeProtocolNotificationService,
    StorebrandSellerNotificationService,
    VerisureBuyerNotificationService,
    EkstraNotificationService,
    ByggstartNotificationService,
    ExponovaNotificationService,
    BrokerNewsOrIncidentNotificationService,
    CraftCMSService,
    HmhSellerMovingNotificationService,
    HmhCleaningNotificationService,
    UserNotifierService,
    HmhBuyerMovingNotificationService,
    FavoriteEstateListedNotificationService,
    PremarketEstateNotificationService,
    EtakstExpiredNotificationService,
    ValueDevelopmentNotificationService,
    ValueDistributionNotification,
    HmhBuyerMoving2NotificationService,
    StatisticsNotification,
    CheckoutOffersNotificationService,
    StorebrandAfterBiddingNotificationService,
    Exponova2NotificationService,
    AfterSigningNotificationService,
    AfterSaleNotificationService,
    UnsignedOtpDocumentNotificationService,
    UnsignedSettlementDocumentNotificationService,
    AfterBiddingNotificationService,
    AfterLaunchNotificationService,
    SettlementSellerNotificationService,
    SettlementBuyerNotificationService,
    SmsDuplicationNotificationService,
    BeforeArchiveNotificationService,
    {
      provide: ConsoleLogger,
      useClass: ConsoleLogger,
      scope: Scope.TRANSIENT,
    },
    PoliticallyExposedPersonFormNotificationService,
    SettlementSigningSMSReminderNotificationService,
    DailyPriceGuessingGameEstateNotificationService,
  ],
  exports: [
    NotificationService,
    UserNotifierService,
    SettlementBuyerNotificationService,
    SettlementSellerNotificationService,
    OvertakeProtocolNotificationService,
    PoliticallyExposedPersonFormNotificationService,
    DailyPriceGuessingGameEstateNotificationService,
  ],
  controllers: [NotificationController],
})
export default class NotificationModule {}
