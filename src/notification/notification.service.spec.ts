import AppConfigService from '../app-config/app-config.service';
import AuthorizationService from '../authorization/authorization.service';
import { FeedService } from '../pg/feed/feed.service';
import { SmsAuditService } from '../pg/sms-audit/sms-audit.service';
import { UserFirebaseTokenService } from '../pg/user-firebase-token/user-firebase-token.service';
import { CheckListTag, Estate } from '../sync/schema/estate.schema';
import { TwilioService } from '../twilio/twilio.service';
import { VitecService } from '../vitec/vitec.service';
import { FirebaseMessagingService } from './firebase-messaging.service';
import { NotificationService } from './notification.service';

describe('NotificationService', () => {
  describe('estateHasOneOfTheChecklistsClicked', () => {
    const twilioService: TwilioService = {} as TwilioService;
    const feedService: FeedService = {} as FeedService;
    const userFirebaseTokenService: UserFirebaseTokenService = {} as UserFirebaseTokenService;
    const firebaseMessagingService: FirebaseMessagingService = {} as FirebaseMessagingService;
    const appConfigService: AppConfigService = {} as AppConfigService;
    const smsAuditService: SmsAuditService = {} as SmsAuditService;
    const authService: AuthorizationService = {} as AuthorizationService;
    const vitecService: VitecService = {} as VitecService;

    const service = new NotificationService(
      twilioService,
      feedService,
      userFirebaseTokenService,
      firebaseMessagingService,
      appConfigService,
      smsAuditService,
      authService,
      vitecService,
    );

    const testEstate: Estate = {
      checkList: {
        checkListItems: [
          { tags: ['AMLINFOSELGER_SALG'], value: 1, changedBy: 'JOLIN', changedDate: '2023-04-11T07:39:10.000Z' },
          { tags: ['NB_BSF_1'], value: 1, changedBy: 'JOLIN', changedDate: '2023-05-25T11:46:40.000Z' },
          { tags: ['MEGLERPAKKE_BESTILT'], value: 1, changedBy: 'THLUN', changedDate: '2023-05-26T08:30:30.000Z' },
          { tags: ['NB_BSF_2'], value: 1, changedBy: 'THLUN', changedDate: '2023-06-02T12:37:32.000Z' },
          { tags: ['PROSPEKT_OPPRETTET'], value: 1, changedBy: 'THLUN', changedDate: '2023-06-02T12:37:42.000Z' },
          { tags: ['AMLSELGERMOTTATT_SALG'], value: 1, changedBy: 'JOLIN', changedDate: '2023-06-08T13:57:48.000Z' },
          { tags: ['DEAKTIVERPROTOKOLL_SALG'], value: 2, changedBy: 'THLUN', changedDate: '2023-06-13T13:58:47.000Z' },
          { tags: ['OFFMARKET'], value: 2, changedBy: 'JOLIN', changedDate: '2023-06-19T10:39:15.000Z' },
          { tags: ['RISK_MEGLER_MED_2'], value: 1, changedBy: 'JOLIN', changedDate: '2023-06-20T10:32:30.000Z' },
          { tags: ['OKFORINT'], value: 1, changedBy: 'JOLIN', changedDate: '2023-06-28T18:07:13.000Z' },
          { tags: ['HVITVASK_OPPGJOR'], value: 2, changedBy: 'RA', changedDate: '2023-07-16T11:38:49.000Z' },
          { tags: ['RISK_OPPGJ_MED_1'], value: 1, changedBy: 'AS', changedDate: '2023-07-26T07:07:16.000Z' },
          { tags: ['SOLGT_AVHL_OPPGJ_1'], value: 2, changedBy: 'AS', changedDate: '2023-07-26T07:07:17.000Z' },
          { tags: ['OT_OK'], value: 1, changedBy: 'AS', changedDate: '2023-07-26T07:07:24.000Z' },
          { tags: ['MAPPE_MANGLER_OT_OK'], value: 2, changedBy: 'AS', changedDate: '2023-07-26T07:07:24.000Z' },
          { tags: ['HUB_OVERTAKELSE_MOTTATT'], value: 1, changedBy: 'WSHub', changedDate: '2023-07-26T10:21:40.000Z' },
        ],
        lastChanged: '2023-07-26T10:21:40.000Z',
      },
    } as unknown as Estate;
    it('should return false if no estate', () => {
      const res = service.estateHasOneOfTheChecklistsClicked({
        estate: null,
        tags: [
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SALE,
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SETTLEMENT,
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_FORECLOSURE,
        ],
      });
      expect(res).toEqual(false);
    });
    it('should return false if no estate checkList', () => {
      const res = service.estateHasOneOfTheChecklistsClicked({
        estate: {} as unknown as Estate,
        tags: [
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SALE,
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SETTLEMENT,
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_FORECLOSURE,
        ],
      });
      expect(res).toEqual(false);
    });
    it('should return false if no estate checkList checkListItems', () => {
      const res = service.estateHasOneOfTheChecklistsClicked({
        estate: { checkList: {} } as unknown as Estate,
        tags: [
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SALE,
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SETTLEMENT,
          CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_FORECLOSURE,
        ],
      });
      expect(res).toEqual(false);
    });
    it('should return false if no tags', () => {
      const res = service.estateHasOneOfTheChecklistsClicked({
        estate: testEstate,
        tags: null,
      });
      expect(res).toEqual(false);
    });
    it('should return false if empty tags', () => {
      const res = service.estateHasOneOfTheChecklistsClicked({
        estate: testEstate,
        tags: [],
      });
      expect(res).toEqual(false);
    });
    it('should return false if estate checklist does not include a tag', () => {
      const res = service.estateHasOneOfTheChecklistsClicked({
        estate: testEstate,
        tags: [CheckListTag.ACTIVATE_SETTLEMENT_BUYER_FOR_SALE],
      });
      expect(res).toEqual(false);
    });
  });
});
