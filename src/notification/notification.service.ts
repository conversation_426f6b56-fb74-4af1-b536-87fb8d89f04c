import { ConsoleLogger, Injectable } from '@nestjs/common';
import { delay } from 'bluebird';
import { differenceInMilliseconds } from 'date-fns';
import parsePhoneNumberFromString from 'libphonenumber-js';
import urljoin from 'url-join';
import AppConfigService from '../app-config/app-config.service';
import AuthorizationService, { FeatureFlag } from '../authorization/authorization.service';
import { FeedService } from '../pg/feed/feed.service';
import { SmsAuditType } from '../pg/sms-audit/sms-audit.model';
import { SmsAuditService } from '../pg/sms-audit/sms-audit.service';
import { UserFirebaseTokenService } from '../pg/user-firebase-token/user-firebase-token.service';
import { CheckListTag, CheckListValue, Estate } from '../sync/schema/estate.schema';
import { TwilioService } from '../twilio/twilio.service';
import { VitecService } from '../vitec/vitec.service';
import { FirebaseMessagingService } from './firebase-messaging.service';

function splitArray(array: string[], size: number) {
  const result = [];
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  return result;
}

export enum PushType {}

interface BaseOptions {
  phoneNumber: string;
  message: string;
  audit: {
    smsAuditType: SmsAuditType;
    estateId: string;
    variant?: string;
  };
}

interface OptionsWithGenericSender extends BaseOptions {
  useTwilio: true;
  vitecContactOptions?: never;
}

interface OptionsWithVitecContactOptions extends BaseOptions {
  // Assuming useTwilio is not true (either false or not present) when vitecContactOptions are provided
  useTwilio?: false;
  vitecContactOptions: {
    contactId: string;
    departmentId: number; // 1 for Nordvik main office, used for invoicing
    fromEmployeeId?: string; // if not provided, the default sender will be used (Nordvik)
  };
}

type TriggerSmsNotification = OptionsWithVitecContactOptions | OptionsWithGenericSender;
@Injectable()
export class NotificationService {
  private readonly logger = new ConsoleLogger(NotificationService.name);

  constructor(
    private readonly twilioService: TwilioService,
    private readonly feedService: FeedService,
    private readonly userFirebaseTokenService: UserFirebaseTokenService,
    private readonly firebaseMessagingService: FirebaseMessagingService,
    private readonly appConfigService: AppConfigService,
    private readonly smsAuditService: SmsAuditService,
    private readonly authService: AuthorizationService,
    private readonly vitecService: VitecService,
  ) {}

  async triggerFeedNotification(userID: string, message: string, iconName: string, redirectUrl: string) {
    this.logger.log(
      `Triggering feed notification for user ${userID} with message ${message}, icon ${iconName} and redirect url ${redirectUrl}`,
    );

    await this.feedService.insert(userID, {
      text: message,
      iconName,
      redirectUrl,
    });
  }

  async deleteFeedNotifications(userID: string, redirectUrl: string) {
    this.logger.log(`Deleting feed notifications for user ${userID} with redirectUrl ${redirectUrl}`);
    await this.feedService.delete(userID, redirectUrl);
  }

  async delayByLastSentSms(phoneNumber: string, smsAuditType: SmsAuditType, delayInMilliseconds = 25000) {
    const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber, 'NO');
    if (!parsedPhoneNumber || !parsedPhoneNumber.isValid()) {
      this.logger.log(`not delaying, phone number not valid`);
      return;
    }

    const prevMessage = await this.smsAuditService.findByType(parsedPhoneNumber.number, smsAuditType).catch(() => null);
    if (prevMessage) {
      this.logger.log(`prevmessage found: ${prevMessage.phoneNumber}, ${smsAuditType}, ${prevMessage.createdAt}`);
    } else {
      this.logger.log(`no prev message found for number: ${parsedPhoneNumber.number}, ${smsAuditType}`);
    }

    const elapsedMillisecondsUntilLastSms = prevMessage
      ? differenceInMilliseconds(new Date(), prevMessage.createdAt)
      : delayInMilliseconds + 1;

    if (!prevMessage) {
      this.logger.log(`not delaying, no prevMessage`);
    } else if (elapsedMillisecondsUntilLastSms < delayInMilliseconds) {
      this.logger.log(`delaying for ${delayInMilliseconds - elapsedMillisecondsUntilLastSms} ms`);
      await delay(delayInMilliseconds - elapsedMillisecondsUntilLastSms);
    } else {
      this.logger.log(`not delaying, ${elapsedMillisecondsUntilLastSms}`);
    }
  }

  async triggerSmsNotification({
    phoneNumber,
    message,
    audit,
    vitecContactOptions,
    useTwilio,
  }: TriggerSmsNotification) {
    this.logger.log(`Triggering sms notification for phone number ${phoneNumber} with message ${message}`);

    const isVitecSMSEnabled = !useTwilio && this.authService.isFeatureEnabled(FeatureFlag.UseVitecSmsSending);
    const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber, 'NO');

    let response: Record<string, any> = null;

    if (!parsedPhoneNumber || !parsedPhoneNumber.isValid()) {
      response = { error: `Invalid phone number: ${phoneNumber}` };
    } else {
      if (isVitecSMSEnabled) {
        response = await this.vitecService
          .sendSMSViaVitec({
            contactId: vitecContactOptions.contactId,
            departmentId: vitecContactOptions.departmentId,
            estateId: audit.estateId,
            fromEmployeeId: undefined,
            message,
            to: parsedPhoneNumber.number,
          })
          .then((response) => ({
            response: response.data,
          }))
          .catch((error) => ({
            error,
          }));
      } else {
        response = await this.twilioService
          .sendSms(parsedPhoneNumber.number, message)
          .then((response) => ({
            response,
          }))
          .catch((error) => ({
            error,
          }));
      }
    }

    await this.smsAuditService.create({
      phoneNumber: parsedPhoneNumber && parsedPhoneNumber.isValid() ? parsedPhoneNumber.number : phoneNumber,
      estateId: audit.estateId,
      text: message,
      type: audit.smsAuditType,
      variant: audit.variant,
      response: response,
    });

    if (response.hasOwnProperty('error')) {
      throw (response as { error: any }).error;
    }
  }

  async triggerPushNotification({
    userIDs,
    message,
    title,
    redirectUrl,
    pushType,
    messageVariant,
    baseUrl,
  }: {
    userIDs: string[];
    message: string;
    title?: string;
    redirectUrl: string;
    pushType: FeatureFlag;
    messageVariant?: 'a' | 'b';
    baseUrl?: string;
  }) {
    const tokens = await this.userFirebaseTokenService.findAllByUserIds(userIDs);
    const nonNullTokens = tokens.filter((token) => token.token !== null);

    if (tokens.length !== nonNullTokens.length) {
      this.logger.error(
        `Null tokens are found for users ${tokens.map((t) => t.userID).join(',')}, this is a code issue`,
      );
    }

    if (!nonNullTokens.length) {
      this.logger.log(`No firebase tokens found for users (${userIDs.length}) ${userIDs.slice(0, 10)}, skipping`);
      return;
    }

    this.logger.log(
      `Triggering push notification for users (${userIDs.length}) among ${userIDs.slice(0, 10)} to ${
        nonNullTokens.length
      } devices with message ${message} and redirect url ${redirectUrl} and pushType: ${pushType}`,
    );

    const isBrokerPushType = pushType === FeatureFlag.SendBrokerNews;

    const url = isBrokerPushType
      ? `${redirectUrl}?utm_medium=push&utm_source=vitec-data-sync-service&utm_campaign=${pushType}&utm_content=${
          messageVariant || 'a'
        }`
      : urljoin(
          baseUrl || this.appConfigService.getAppUrl(),
          `${redirectUrl}?utm_medium=push&utm_source=vitec-data-sync-service&utm_campaign=${pushType}&utm_content=${
            messageVariant || 'a'
          }`,
        );

    const arraysWith500Ids = splitArray(
      nonNullTokens.map((t) => t.token),
      500,
    );

    this.logger.log(`Sending push notis ${arraysWith500Ids.length} times of 500 size arrays`);

    const sendMulticastTo500Targets = async (arrayWith500Tokens: string[]) => {
      const result = await this.firebaseMessagingService.sendMulticast({
        tokens: arrayWith500Tokens,
        notification: { title: title || 'Nordvik', body: message },
        data: { url },
        fcmOptions: { analyticsLabel: pushType },
      });

      if (result.failureCount > 1) {
        this.logger.warn(
          `Push notifications to ${result.successCount} targets sent successfully, ${result.failureCount} targets responded with error`,
        );
        if (result.successCount === 0) {
          this.logger.error(
            `No notification reached the users(${arrayWith500Tokens.length}): ${arrayWith500Tokens.slice(0, 10)}`,
          );
        }
        // for (const response of result.responses) {
        //   if (!response.success) {
        //     const error = response.error;
        //     this.logger.warn(
        //       `Push notification sending failed, code: ${error.code}, message: ${error.message}`,
        //       error.stack,
        //     );
        //   }
        // }
      } else {
        this.logger.log(`Push notifications to ${result.successCount} targets sent successfully`);
      }
    };

    try {
      // Promise.all spikes the CPU up to 100% which restarts the ECS instance
      for (let i = 0; i < arraysWith500Ids.length; i++) {
        await sendMulticastTo500Targets(arraysWith500Ids[i]);
        this.logger.log(`Batch of notis finished ${i + 1} / ${arraysWith500Ids.length}`);
      }
    } catch (error) {
      this.logger.error(
        `Could not send push notifications for ${userIDs.slice(0, 10)}, (${
          userIDs.length
        }), firebase responded with Error: ${error}`,
      );
    }
  }

  estateHasOneOfTheChecklistsClicked({ estate, tags }: { estate: Estate | null; tags: string[] }): boolean {
    return !!estate?.checkList?.checkListItems?.find(
      (checklistItem) =>
        checklistItem.value === CheckListValue.YES &&
        !!checklistItem.tags.find((tag) => tags?.includes(tag as CheckListTag)),
    );
  }
}
