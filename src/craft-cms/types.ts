export interface CraftCmsIncident {
  id: string;
  slug: string;
  title: string;
  postDate: string;
  resolved: boolean;
  resolvedDate?: string;
  resolvedComment?: string;
  updates?: CraftCmsIncidentUpdate[];
}

export interface CraftCmsIncidentUpdate {
  time: string;
  text: string;
}

export interface CraftCMSAllIncidentsResponse {
  data: CraftCmsIncident[];
}

export interface Module {
  type: string;
  body: string;
}

export interface CraftCMSImage {
  small: string;
  medium: string;
  large: string;
}

export interface CraftCMSAuthor {
  name: string;
  email: string;
}

export interface CraftCMSArticle {
  id: string;
  author: CraftCMSAuthor;
  slug: string;
  title: string;
  postDate: string;
  categories: CraftCMSCategory[];
  departments: CraftCMSDepartment[];
  targetRoles: Roles[];
  channels: [string];
  image: CraftCMSImage;
  excerpt: string;
  modules: Module[];
  externalUrl: string;
}

export interface CraftCMSCategory {
  id: string;
  slug: string;
  title: string;
}

export interface CraftCMSDepartment {
  departmentId: number;
  title: string;
}

export enum Roles {
  CEO = 'daglig-leder',
  Employee = 'megler',
  PowerOfAttorney = 'fullmektig',
}
