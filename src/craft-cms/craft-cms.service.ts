import { ConsoleLogger, Injectable } from '@nestjs/common';
import AppConfigService from '../app-config/app-config.service';
import { CraftCMSArticle, CraftCmsIncident } from './types';

@Injectable()
export class CraftCMSService {
  private readonly logger = new ConsoleLogger(CraftCMSService.name);
  private apiKey: string;

  constructor(private readonly appConfigService: AppConfigService) {
    if (!this.appConfigService.getCraftCmsUrl()) {
      this.logger.error('No Craft CMS URL found');
    }
  }

  private endpoints = {
    // NEWS
    news: (slugOrId: string) => `${this.appConfigService.getCraftCmsUrl()}/api/megler/content/${slugOrId}.json`,

    // INCIDENTS
    incident: (slugOrId: string) => `${this.appConfigService.getCraftCmsUrl()}/api/megler/incidents/${slugOrId}.json`,
  } as const;

  async getIncident(entrySlugOrId: string): Promise<CraftCmsIncident | null> {
    try {
      return this.fetch<CraftCmsIncident>(this.endpoints.incident(entrySlugOrId));
    } catch (error) {
      console.error('Error fetching incident from CMS', error);
      return null;
    }
  }

  async getArticle(entrySlugOrId: string): Promise<CraftCMSArticle | null> {
    try {
      return this.fetch<CraftCMSArticle>(this.endpoints.news(entrySlugOrId));
    } catch (error) {
      console.error('Error fetching article from CMS', error);
      return null;
    }
  }

  ////////////////////////////////////////////
  // GENERIC FETCH
  ////////////////////////////////////////////
  private async fetch<T>(url: string, body?: unknown): Promise<T | null> {
    try {
      console.log('Fetching from CMS', url);
      const response = await fetch(url, {
        method: body ? 'POST' : 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.appConfigService.getCraftCmsApiKey(),
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        console.error(JSON.stringify(body));
        throw new Error(response.statusText);
      }

      return response.json() as T;
    } catch (error) {
      console.error('Error fetching from CMS', error);
      return null;
    }
  }
}
