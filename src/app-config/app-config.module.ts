import Joi from '@hapi/joi';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import AppConfigService from './app-config.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        NODE_ENV: Joi.string().default('development'),

        PORT: Joi.number().default(3000),

        API_KEY: Joi.string().default('change_me'),

        MONGODB_URI: Joi.string()
          .default('**************************************************************************')
          .regex(/mongodb(\+srv)?:\/\/.+:.+@.+/),

        CLOUDFRONT_URL: Joi.string().default('https://example.cloudfront.net'),

        VITEC_API_URL: Joi.string().default('https://vitec.example'),

        VITEC_USER_NAME: Joi.string().default('username'),

        VITEC_PASSWORD: Joi.string().default('password'),

        VITEC_INSTALLATION_ID: Joi.string().default('EXAMPLE'),

        VITEC_RATE_LIMIT_MAX: Joi.number().default(3),

        VITEC_RATE_LIMIT_TIME_WINDOW: Joi.number().default(1000),

        VITEC_REQUEST_MAX_RETRY: Joi.number().default(3),

        VITEC_REQUEST_RETRY_AFTER: Joi.number().default(60),

        S3_ACCESS_KEY_ID: Joi.string().default('default'),

        S3_BUCKET: Joi.string().default('default'),

        S3_SECRET_ACCESS_KEY: Joi.string().default('default'),

        S3_REGION: Joi.string().default('eu-north-1'),

        S3_EMPLOYEES_DIRECTORY: Joi.string().default('employees'),

        S3_ESTATES_DIRECTORY: Joi.string().default('estates'),

        UNLEASH_URL: Joi.string()
          .default('')
          .regex(/http(s)?:\/\/.+/),

        UNLEASH_INSTANCE_ID: Joi.string().allow('').default(''),

        UNLEASH_APP_NAME: Joi.string().default(''),

        UNLEASH_AUTHORIZATION_HEADER: Joi.string().allow('').default(''),

        TWILIO_ACCOUNT_SID: Joi.string().default('ACchange_me'),

        TWILIO_AUTH_TOKEN: Joi.string().default('change_me'),

        TWILIO_SMS_PHONE_NUMBER: Joi.string().default('+***********'),

        APP_URL: Joi.string().default('https://nordvik.app'),

        POSTGRES_USERNAME: Joi.string().default('postgres'),

        POSTGRES_PASSWORD: Joi.string().default(''),

        POSTGRES_HOST: Joi.string().default('nordvik-dev.cscps8wwccxx.eu-north-1.rds.amazonaws.com'),

        POSTGRES_DB: Joi.string().default('nordvik'),

        POSTGRES_PORT: Joi.number().default(5432),

        FIREBASE_PROJECT_ID: Joi.string().default('nordvik-ios-android-app'),

        FIREBASE_PRIVATE_KEY: Joi.string().default(''),

        FIREBASE_CLIENT_EMAIL: Joi.string().default(
          '<EMAIL>',
        ),

        FIREBASE_SERVICE_ACCOUNT_ID: '104009257416192458757',

        SQS_URL: Joi.string().default(''),

        DISABLE_SQS: Joi.string().default('false'),

        TZ: Joi.string().default('UTC'),

        EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL: Joi.string().default(
          'https://test-api.eiendomsverdi.no/PublicInformationRealtime.svc',
        ),

        EIENDOMSVERDI_SERVICE_URL: Joi.string().default('https://test-api.eiendomsverdi.no/Service.svc'),

        EIENDOMSVERDI_USER: Joi.string().default(''),

        EIENDOMSVERDI_PASSWORD: Joi.string().default(''),

        MAPBOX_ACCESS_TOKEN: Joi.string().default(''),

        MAPBOX_BASE_URL: Joi.string().default('https://api.mapbox.com'),

        MAPBOX_USER_NAME: Joi.string().default('nordvik-test'),

        MAPBOX_STYLE: Joi.string().default('ckg0rv03t10rq19lmt0nuhzhg'),

        MAPBOX_ZOOM: Joi.number().default(15),

        BACKEND_URL: Joi.string().default('https://api-dev.nordvik.app'),

        SENDGRID_API_KEY: Joi.string().default(''),

        SLACK_WEBHOOK_BASE_URI: Joi.string().default(''),

        SLACK_DOCUMENT_ERROR_POSTFIX: Joi.string().default(''),

        SLACK_SMS_DUPLICATION_ERROR_POSTFIX: Joi.string().default(''),

        BACKEND_API_KEY: Joi.string().default(''),

        PRICE_GUESSING_ESTATE_HOUR_OF_DAILY_QUIZ: Joi.number().default(6),

        PRICE_GUESSING_ESTATE_MINUTE_OF_DAILY_QUIZ: Joi.number().default(0),

        PRICE_GUESSING_ESTATES_PER_DAY: Joi.number().default(1),

        CRAFT_CMS_URL: Joi.string().default('https://cms.nordvikbolig.no'),

        CRAFT_CMS_API_KEY: Joi.string().default(''),
      }),
    }),
  ],
  providers: [AppConfigService],
  exports: [AppConfigService],
})
export default class AppConfigModule {}
