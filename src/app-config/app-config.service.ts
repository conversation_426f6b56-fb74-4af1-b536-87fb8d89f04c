import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export default class AppConfigService {
  constructor(private readonly configService: ConfigService) {}

  getEnvironment(): string {
    return this.configService.get('NODE_ENV');
  }

  isProductionEnvironment(): boolean {
    return this.getEnvironment() === 'production';
  }

  isDevelopmentEnvironment(): boolean {
    return this.getEnvironment() === 'development';
  }

  isTestEnvironment(): boolean {
    return this.getEnvironment() === 'test';
  }

  getPort(): number {
    return this.configService.get('PORT');
  }

  getApiKey(): string {
    return this.configService.get('API_KEY');
  }

  getMongoDbUri(): string {
    return this.configService.get('MONGODB_URI');
  }

  getUnleashUrl(): string {
    return this.configService.get('UNLEASH_URL');
  }

  getUnleashInstanceId(): string {
    return this.configService.get('UNLEASH_INSTANCE_ID');
  }

  getUnleashAppName(): string {
    return this.configService.get('UNLEASH_APP_NAME');
  }

  getUnleashAuthorizationHeader(): string {
    return this.configService.get('UNLEASH_AUTHORIZATION_HEADER');
  }

  getCloudfrontUrl(): string {
    return this.configService.get('CLOUDFRONT_URL');
  }

  getVitecBaseUrl(): string {
    return this.configService.get('VITEC_API_URL');
  }

  getVitecUsername(): string {
    return this.configService.get('VITEC_USER_NAME');
  }

  getVitecPassword(): string {
    return this.configService.get('VITEC_PASSWORD');
  }

  getVitecInstallationId(): string {
    return this.configService.get('VITEC_INSTALLATION_ID');
  }

  getVitecRateLimitMax(): number {
    return this.configService.get('VITEC_RATE_LIMIT_MAX');
  }

  getVitecRateLimitTimeWindow(): number {
    return this.configService.get('VITEC_RATE_LIMIT_TIME_WINDOW');
  }

  getVitecRequestMaxRetry(): number {
    return this.configService.get('VITEC_REQUEST_MAX_RETRY');
  }

  getVitecRequestRetryAfter(): number {
    return this.configService.get('VITEC_REQUEST_RETRY_AFTER');
  }

  getS3AccessKeyId(): string {
    return this.configService.get('S3_ACCESS_KEY_ID');
  }

  getS3Bucket(): string {
    return this.configService.get('S3_BUCKET');
  }

  getS3SecretAccessKey(): string {
    return this.configService.get('S3_SECRET_ACCESS_KEY');
  }

  getS3Region(): string {
    return this.configService.get('S3_REGION');
  }

  getS3EmployeesDirectory(): string {
    return this.configService.get('S3_EMPLOYEES_DIRECTORY');
  }

  getS3EstatesDirectory(): string {
    return this.configService.get('S3_ESTATES_DIRECTORY');
  }

  getTwilioAccountSid(): string {
    return this.configService.get('TWILIO_ACCOUNT_SID');
  }

  getTwilioAuthToken(): string {
    return this.configService.get('TWILIO_AUTH_TOKEN');
  }

  getTwilioSmsPhoneNumber(): string {
    return this.configService.get('TWILIO_SMS_PHONE_NUMBER');
  }

  getAppUrl(): string {
    return this.configService.get('APP_URL');
  }

  getPgHost(): string {
    return this.configService.get('POSTGRES_HOST');
  }

  getPgUsername(): string {
    return this.configService.get('POSTGRES_USERNAME');
  }

  getPgPassword(): string {
    return this.configService.get('POSTGRES_PASSWORD');
  }

  getPgDatabase(): string {
    return this.configService.get('POSTGRES_DB');
  }

  getPgPort(): number {
    return this.configService.get('POSTGRES_PORT');
  }

  getFirebaseProjectId(): string {
    return this.configService.get('FIREBASE_PROJECT_ID');
  }

  getFirebasePrivateKey(): string {
    return this.configService.get('FIREBASE_PRIVATE_KEY');
  }

  getFirebaseClientEmail(): string {
    return this.configService.get('FIREBASE_CLIENT_EMAIL');
  }

  getFirebaseServiceAccountId(): string {
    return this.configService.get('FIREBASE_SERVICE_ACCOUNT_ID');
  }

  getSqsUrl(): string {
    return this.configService.get('SQS_URL');
  }

  getDisableSqs(): boolean {
    return this.configService.get('DISABLE_SQS')?.toLowerCase() === 'true';
  }

  getEiendomsverdiPublicInformationRealtimeUrl(): string {
    return this.configService.get('EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL');
  }

  getEiendomsverdiServiceUrl(): string {
    return this.configService.get('EIENDOMSVERDI_SERVICE_URL');
  }

  getEiendomsverdiUser(): string {
    return this.configService.get('EIENDOMSVERDI_USER');
  }

  getEiendomsverdiPassword(): string {
    return this.configService.get('EIENDOMSVERDI_PASSWORD');
  }

  getMapboxAccessToken(): string {
    return this.configService.get('MAPBOX_ACCESS_TOKEN');
  }

  getMapboxBaseUrl(): string {
    return this.configService.get('MAPBOX_BASE_URL');
  }

  getMapboxUsername(): string {
    return this.configService.get('MAPBOX_USER_NAME');
  }

  getMapboxStyle(): string {
    return this.configService.get('MAPBOX_STYLE');
  }

  getMapboxZoom(): number {
    return this.configService.get('MAPBOX_ZOOM');
  }

  getBackendUrl(): string {
    return this.configService.get('BACKEND_URL');
  }

  getSendgridApikey(): string {
    return this.configService.get('SENDGRID_API_KEY');
  }

  getSlackApiBaseUrl(): string {
    return this.configService.get('SLACK_WEBHOOK_BASE_URI');
  }

  getSlackDocumentErrorPostfix(): string {
    return this.configService.get('SLACK_DOCUMENT_ERROR_POSTFIX');
  }

  getSlackSmsDuplicationError(): string {
    return this.configService.get('SLACK_SMS_DUPLICATION_ERROR_POSTFIX');
  }

  getIdfyId(): string {
    return this.configService.get('IDFY_ID');
  }

  getIdfySecret(): string {
    return this.configService.get('IDFY_SECRET');
  }

  getBackendApiKey(): string {
    return this.configService.get('BACKEND_API_KEY');
  }

  getLipscoreApiKey(): string {
    return this.configService.get('LIPSCORE_API_KEY');
  }

  getLipscoreSecretApiKey(): string {
    return this.configService.get('LIPSCORE_SECRET_API_KEY');
  }

  getLipscoreApiUrl(): string {
    return this.configService.get('LIPSCORE_API_URL');
  }

  getPriceGuessingEstateHourOfDailyQuiz(): number {
    return this.configService.get('PRICE_GUESSING_ESTATE_HOUR_OF_DAILY_QUIZ');
  }

  getPriceGuessingEstateMinuteOfDailyQuiz(): number {
    return this.configService.get('PRICE_GUESSING_ESTATE_MINUTE_OF_DAILY_QUIZ');
  }
  getPriceGuessingEstatesPerDay(): number {
    return this.configService.get('PRICE_GUESSING_ESTATES_PER_DAY');
  }
  getCraftCmsUrl(): string {
    return this.configService.get('CRAFT_CMS_URL') ?? 'https://cms.nordvikbolig.no';
  }
  getCraftCmsApiKey(): string {
    return this.configService.get('CRAFT_CMS_API_KEY');
  }
}
