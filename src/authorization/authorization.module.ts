import { Module } from '@nestjs/common';
import AppConfigModule from '../app-config/app-config.module';
import AuthorizationService from './authorization.service';
import AuthenticationModule from '../authentication/authentication.module';

@Module({
  imports: [AppConfigModule, AuthenticationModule],
  providers: [AuthorizationService],
  exports: [AuthorizationService],
})
export default class AuthorizationModule {}
