import { ConsoleLogger, Injectable } from '@nestjs/common';
import { Unleash, UnleashConfig, initialize } from 'unleash-client';
import AppConfigService from '../app-config/app-config.service';

export enum FeatureFlag {
  SendSettlementBuyerBrokerSms = 'send-settlement-buyer-broker-sms',
  SendSettlementBuyerSms = 'send-settlement-buyer-sms',
  SendSettlementBuyerEmail = 'send-settlement-buyer-email',
  SendSettlementSellerBrokerSms = 'send-settlement-seller-broker-sms',
  SendSettlementSellerSms = 'send-settlement-seller-sms',
  SendSettlementSellerEmail = 'send-settlement-seller-email',
  SendSettlementSigningReminderSms = 'send-settlement-signing-reminder-sms',
  SendOtpSms = 'send-otp-sms',
  SendOtpFeed = 'send-otp-feed',
  SendOtpPush = 'send-otp-push',
  SendOtpEmail = 'send-otp-email',
  SendStorebrandSellerFeed = 'send-storebrand-seller-feed',
  SendStorebrandSellerPush = 'send-storebrand-seller-push',
  SendStorebrandBuyerPush = 'send-storebrand-buyer-push',
  SendStorebrandBuyerFeed = 'send-storebrand-buyer-feed',
  SendEkstraPush = 'send-ekstra-push',
  SendEkstraFeed = 'send-ekstra-feed',
  SendVerisureBuyerPush = 'send-verisure-buyer-push',
  SendVerisureBuyerFeed = 'send-verisure-buyer-feed',
  SendByggstartPush = 'send-byggstart-push',
  SendByggstartFeed = 'send-byggstart-feed',
  SendExponovaPush = 'send-exponova-push',
  SendExponovaFeed = 'send-exponova-feed',
  SendHmhSellerMovingPush = 'send-hmh-seller-moving-push',
  SendHmhSellerMovingFeed = 'send-hmh-seller-moving-feed',
  SendHmhBuyerMovingPush = 'send-hmh-buyer-moving-push',
  SendHmhBuyerMovingFeed = 'send-hmh-buyer-moving-feed',
  SendHmhBuyerMoving2Push = 'send-hmh-buyer-moving-2-push',
  SendHmhBuyerMoving2Feed = 'send-hmh-buyer-moving-2-feed',
  SendHmhCleaningPush = 'send-hmh-cleaning-push',
  SendHmhCleaningFeed = 'send-hmh-cleaning-feed',
  SendFavoriteEstateListedFeed = 'send-favorite-estate-listed-feed',
  SendFavoriteEstateListedPush = 'send-favorite-estate-listed-push',
  SendPremarketEstateFeed = 'send-premarket-estate-feed',
  SendPremarketEstatePush = 'send-premarket-estate-push',
  SendEtakstExpiredFeed = 'send-etakst-expired-feed',
  SendEtakstExpiredPush = 'send-etakst-expired-push',
  SendValueDevelopmentFeed = 'send-value-development-feed',
  SendValueDevelopmentPush = 'send-value-development-push',
  SendValueDistributionFeed = 'send-value-distribution-feed',
  SendValueDistributionPush = 'send-value-distribution-push',
  SendBrokerOtpSms = 'send-broker-otp-sms',
  SendBrokerNews = 'send-broker-news',
  SendFirstDocumentFeed = 'send-first-document-feed',
  SendFirstDocumentPush = 'send-first-document-push',
  SendFinnStatisticsPush = 'send-finn-statistics-push',
  SendFinnStatisticsFeed = 'send-finn-statistics-feed',
  SendNordvikBoligStatisticsPush = 'send-nordvik-bolig-statistics-push',
  SendNordvikBoligStatisticsFeed = 'send-nordvik-bolig-statistics-feed',
  SendCheckoutOffersPush = 'send-checkout-offers-push',
  SendCheckoutOffersFeed = 'send-checkout-offers-feed',
  SendStorebrandAfterBiddingPush = 'send-storebrand-after-bidding-push',
  SendStorebrandAfterBiddingFeed = 'send-storebrand-after-bidding-feed',
  SendExponova2Push = 'send-exponova-2-push',
  SendExponova2Feed = 'send-exponova-2-feed',
  ContractSigningSms = 'send-contract-signing-sms',
  AfterSaleSms = 'send-after-sale-sms',
  SendUnsignedDocumentNotification = 'send-unsigned-document-notification', // OTP document
  SendUnsignedSettlementDocumentNotification = 'send-unsigned-settlement-document-notification',
  SendAfterBiddingSms = 'send-after-bidding-sms',
  SendAfterLaunchSms = 'send-after-launch-sms',
  SendLipscoreEmail = 'send-lipscore-email',
  SendBeforeArchiveEmail = 'before-archive-email',
  ForceSendSettlementSeller = 'force-send-settlement-seller',
  ForceSendSettlementBuyer = 'force-send-settlement-buyer',
  SendPepSms = 'send-politically-exposed-person-sms',
  SendPepSmsToBroker = 'send-politically-exposed-person-broker-sms',
  SendPepEmail = 'send-politically-exposed-person-email',
  SendPriceGuessingDailyPush = 'send-price-guessing-daily-push',
  SendPriceGuessingDailyFeed = 'send-price-guessing-daily-feed',
  AutoFinalizePep = 'auto-finalize-pep',
  UseVitecSmsSending = 'use-vitec-sms-sending-sync',
}

@Injectable()
export default class AuthorizationService {
  private readonly unleash: Unleash;
  private readonly logger = new ConsoleLogger(AuthorizationService.name);

  private getUnleashConfig(): UnleashConfig {
    const config: UnleashConfig = {
      appName: this.appConfigService.getUnleashAppName(),
      url: this.appConfigService.getUnleashUrl(),
    };

    const instanceId = this.appConfigService.getUnleashInstanceId();
    if (instanceId) {
      config.instanceId = instanceId;
    }

    const authorizationHeader = this.appConfigService.getUnleashAuthorizationHeader();
    if (authorizationHeader) {
      config.customHeaders = {
        Authorization: authorizationHeader,
      };
    }

    return config;
  }

  constructor(private readonly appConfigService: AppConfigService) {
    const unleashConfig = this.getUnleashConfig();
    this.logger.log(`Unleash init: ${JSON.stringify(unleashConfig)}`);
    this.unleash = initialize(unleashConfig);
  }

  onApplicationShutdown(): void {
    this.unleash.destroy();
  }

  isFeatureEnabledForEstate = (featureFlag: FeatureFlag, estateId: string): boolean =>
    this.unleash.isEnabled(featureFlag, {
      userId: estateId,
    });

  isFeatureEnabled = (featureFlag: FeatureFlag): boolean => this.unleash.isEnabled(featureFlag, {});
}
