function replaceInString(string: string) {
  const find = ['å', 'æ', 'ø', 'é', 'ä', 'ö', 'ü'];
  const replace = ['a', 'ae', 'oe', 'e', 'a', 'o', 'u'];
  let replaceString = string;

  for (let i = 0; i < find.length; i++) {
    const regex = new RegExp(find[i], 'g');
    replaceString = replaceString.replace(regex, replace[i]);
  }

  return replaceString;
}

export function generateSlug(string: string) {
  const stringToLowerCase = string.trim().toLowerCase();
  const stringReplaceSpecialChar = replaceInString(stringToLowerCase);
  return stringReplaceSpecialChar.replace(/\s/g, '-').replace('.', '');
}
