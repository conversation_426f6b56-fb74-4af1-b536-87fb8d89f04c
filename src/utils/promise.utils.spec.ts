import { delay } from 'bluebird';
import { chainBy<PERSON><PERSON> } from './promise.utils';

describe('promise utils', () => {
  describe('#chainByKey', () => {
    describe('given an array of elements with different keys', () => {
      const elements = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }];

      it('should execute the functions in separate buckets', async () => {
        const executedValues = await chainByKey(
          elements,
          (e) => e.id,
          async (e) => ({ processed: true, id: e.id }),
        );

        expect(executedValues.length).toEqual(elements.length);
      });

      it("should return the function's return values for all elements", async () => {
        const executedValues = await chainByKey(
          elements,
          (e) => e.id,
          async (e) => ({ processed: true, id: e.id }),
        );

        expect(executedValues).toEqual(
          expect.not.arrayContaining([expect.not.arrayContaining([expect.objectContaining({ processed: true })])]),
        );
        for (const elem of elements) {
          expect(executedValues).toEqual(
            expect.arrayContaining([expect.arrayContaining([expect.objectContaining({ id: elem.id })])]),
          );
        }
      });
    });

    describe('given an array of elements with the same keys', () => {
      const elements = [
        { key: 1, index: 0 },
        { key: 1, index: 1 },
        { key: 1, index: 2 },
        { key: 1, index: 3 },
      ];

      it('should execute the promises in a single bucket', async () => {
        const executedValues = await chainByKey(
          elements,
          (e) => e.key,
          async (e) => ({ processed: true, key: e.key, index: e.index }),
        );

        expect(executedValues.length).toEqual(1);
      });

      it('should execute the promises in order sequentially', async () => {
        let order = 0;

        const executedValues = await chainByKey(
          elements,
          (e) => e.key,
          async (e) => {
            await delay(50 - 10 * e.index);
            return { processed: true, index: e.index, key: e.key, order: order++ };
          },
        );

        executedValues[0].every((val) => {
          expect(val.index).toEqual(val.order);
        });
      });
    });
  });
});
