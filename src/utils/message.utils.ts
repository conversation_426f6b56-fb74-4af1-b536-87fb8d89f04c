import urljoin from 'url-join';

type PopulateSettlementMessageTemplate = (params: {
  template: string;
  address: string;
  estateId: string;
  type: 'buyer' | 'seller';
  appUrl: string;
  isProject?: boolean;
}) => string;

export const populateSettlementMessageTemplate: PopulateSettlementMessageTemplate = ({
  template,
  address,
  estateId,
  type,
  appUrl,
  isProject = false,
}) =>
  template
    .replace('[address]', address)
    .replace(
      /\[URL\]/g,
      urljoin(appUrl, 'customer', 'settlement', type, isProject ? 'project' : '', estateId, 'start'),
    );

type PopulateOtpMessageTemplate = (params: { template: string; address: string; url: string }) => string;

export const populateOtpMessageTemplate: PopulateOtpMessageTemplate = ({ template, address, url }) =>
  template.replace('[address]', address).replace(/\[URL\]/g, url);

type PopulatePEPMessageTemplate = (params: {
  template: string;
  address: string;
  estateId: string;
  appUrl: string;
}) => string;

export const populatePEPMessageTemplate: PopulatePEPMessageTemplate = ({ template, address, estateId, appUrl }) =>
  template
    .replace('[address]', address)
    .replace(/\[URL\]/g, urljoin(appUrl, 'customer', 'pep-form', estateId, 'start'));
