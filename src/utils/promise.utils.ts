import { mapSeries } from 'bluebird';
import { groupWith } from 'ramda';

/**
 * Executes an async function on a group of elements and returns their results in a nested array.
 *
 * The functions are executed paralelly if the input elements have different keys
 * and sequentially if their keys are the same
 *
 * The element key is selected by keyFunc
 * @param arr {Array<T>} the input array
 * @param keyFunc {Function<T,any>} key selector, group the input by the return value
 * @param asyncFunc {Function<T, Promise<P>} an async function to execute on the elements
 * @returns All return values in an array of arrays for each unique key a seaparate array
 *     of elements is created.
 */
export const chainByKey = async <T, P>(
  arr: T[],
  keyFunc: (arg: T) => any,
  asyncFunc: (arg: T) => Promise<P>,
): Promise<P[][]> => {
  const messagesByEstates = groupWith((m1, m2) => keyFunc(m1) === keyFunc(m2), arr);
  const promises = messagesByEstates.map((e) => mapSeries(e, asyncFunc));
  return await Promise.all(promises);
};
