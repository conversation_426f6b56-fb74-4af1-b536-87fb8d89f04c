import { delay } from 'bluebird';

export async function retryFunction<T>(
  functionToRetryIfThrows: () => Promise<T>,
  retryCount = 10,
): Promise<{ response: T; error: null | string }> {
  try {
    const response = await functionToRetryIfThrows();
    return { response, error: null };
  } catch (error) {
    if (retryCount <= 1) {
      return { response: null, error: error.toString() };
    } else {
      await delay(3000);
      return await retryFunction(functionToRetryIfThrows, retryCount - 1);
    }
  }
}
