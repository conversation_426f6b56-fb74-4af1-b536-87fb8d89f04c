import { ConsoleLogger, Injectable } from '@nestjs/common';
import { TwilioService as TwilioClient } from 'nestjs-twilio';
import { MessageInstance } from 'twilio/lib/rest/api/v2010/account/message';
import AppConfigService from '../app-config/app-config.service';

@Injectable()
export class TwilioService {
  private readonly logger = new ConsoleLogger(TwilioService.name);

  constructor(private readonly appConfigService: AppConfigService, private readonly twilioClient: TwilioClient) {}

  async sendSms(to: string, body: string): Promise<MessageInstance> {
    this.logger.log(`Sending SMS to ${to} with body ${body}`);

    return this.twilioClient.client.messages.create({
      body,
      from: this.appConfigService.getTwilioSmsPhoneNumber(),
      to,
    });
  }
}
