import { Module } from '@nestjs/common';
import AppConfigModule from '../app-config/app-config.module';
import { TwilioModule as TwilioModuleBase } from 'nestjs-twilio';
import AppConfigService from '../app-config/app-config.service';
import { TwilioService } from './twilio.service';

@Module({
  imports: [
    AppConfigModule,
    TwilioModuleBase.forRootAsync({
      imports: [AppConfigModule],
      useFactory: async (appConfigService: AppConfigService) => ({
        accountSid: appConfigService.getTwilioAccountSid(),
        authToken: appConfigService.getTwilioAuthToken(),
      }),
      inject: [AppConfigService],
    }),
  ],
  providers: [TwilioService],
  exports: [TwilioService],
})
export default class TwilioModule {}
