import { ConsoleLogger, Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import AppConfigService from '../app-config/app-config.service';

export type GeoCode = {
  lat: number;
  lng: number;
};

type GeocodeResponse = {
  query: string[];
  features: {
    relevance: number;
    center: [number, number];
  }[];
};

@Injectable()
export class MapboxService {
  private readonly logger = new ConsoleLogger(MapboxService.name);
  private readonly httpClient: AxiosInstance;

  constructor(private readonly appConfigService: AppConfigService) {
    this.httpClient = axios.create();
  }

  async getGeoCodeFromAddress(address: string): Promise<GeoCode | null> {
    this.logger.debug(`Getting image for address ${address}`);

    const url = `${this.appConfigService.getMapboxBaseUrl()}/geocoding/v5/mapbox.places/${address}.json?limit=1&types=address&access_token=${this.appConfigService.getMapboxAccessToken()}`;
    try {
      const response = await axios.get<GeocodeResponse>(url);
      const geoCode = response.data.features.find(
        (result) => result.relevance > parseFloat(process.env.MAPBOX_MIN_RELEVANCE || '0.5'),
      );

      if (!geoCode) {
        return null;
      }

      return {
        lng: geoCode.center[0],
        lat: geoCode.center[1],
      };
    } catch {
      return null;
    }
  }

  async getImageFromGeoCode(geoCode: GeoCode): Promise<Buffer | null> {
    this.logger.debug(`Getting image for coordinates ${geoCode.lat},${geoCode.lng}`);
    const url = `${this.appConfigService.getMapboxBaseUrl()}/styles/v1/${this.appConfigService.getMapboxUsername()}/${this.appConfigService.getMapboxStyle()}/static/${
      geoCode.lng
    },${
      geoCode.lat
    },${this.appConfigService.getMapboxZoom()},0,0/512x512?access_token=${this.appConfigService.getMapboxAccessToken()}`;

    this.logger.debug(url);
    try {
      const response = await this.httpClient.get(url, { responseType: 'arraybuffer' });
      const buffer = response.data;
      return buffer;
    } catch (e) {
      return null;
    }
  }
}
