API_KEY=whatever-you-want-for-endpoint-calling
PORT = 3002

# Test vitec below
VITEC_API_URL=https://hubtest.megler.vitec.net
VITEC_USER_NAME=inceptech
VITEC_PASSWORD=<find here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>
VITEC_INSTALLATION_ID=MSVPAR

# DEV AWS
S3_ACCESS_KEY_ID=AKIA5LBAPSLLRXLKORAL
S3_BUCKET=vitec-data-sync-dev
S3_SECRET_ACCESS_KEY=<find here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>

MONGODB_URI=mongodb+srv://<get from cloud.mongodb.com or other devs>@vitec-staging-hubtest.i9lvi.mongodb.net/vitec-data-sync-dev?retryWrites=true\&w=majority

CLOUDFRONT_URL=<find here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>

# Postgres LOCAL
POSTGRES_USERNAME=user
POSTGRES_DB=testdb
POSTGRES_PORT=5432
POSTGRES_HOST=localhost
POSTGRES_PASSWORD=password

SENDGRID_API_KEY=<find here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>
IDFY_ID=t849847327e4c4277b73adccb6e6d4698
IDFY_SECRET=<find here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>

# Nordvik frontend
APP_URL=http://localhost:3001
# Nordvik backend
BACKEND_API_KEY=<whatever you set up in your backends .env>
BACKEND_URL=http://localhost:3000

FIREBASE_PRIVATE_KEY=<find here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>
DISABLE_SQS=true

#LIPSCORE
LIPSCORE_API_URL=https://api.lipscore.com
LIPSCORE_API_KEY=<find here, prod=dev in this case: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>
LIPSCORE_SECRET_API_KEY=<find here, prod=dev in this case: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd>

###Unleash Feature flags DEV
UNLEASH_URL=https://eu.app.unleash-hosted.com/eubb1011/api/
UNLEASH_APP_NAME=nordvik-app-sync
UNLEASH_AUTHORIZATION_HEADER=*:development.5da2de570eb366083062c31beb5729316e8fc47fd6f8cb0e1e0035a6

### If you would need any more env variables, find it here: https://gitlab.com/nordvik-team/vitec-data-sync-service/-/settings/ci_cd