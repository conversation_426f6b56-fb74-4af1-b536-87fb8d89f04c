image: node:12-alpine

variables:
  REPOSITORY_URL: 917043647191.dkr.ecr.eu-north-1.amazonaws.com/vitec-data-sync-service

services:
  - docker:dind

stages:
  - test
  - build
  - deploy

test:
  stage: test
  variables:
    ROOT_FOLDER_PATH: $CI_PROJECT_DIR
  script:
    - yarn --frozen-lockfile
    - yarn lint
    - yarn test

build:
  stage: build
  image: docker:stable
  before_script:
    - apk add --no-cache curl jq python3 py-pip
    - pip install awscli
    - $(aws ecr get-login --no-include-email --region eu-north-1)
  services:
    - docker:stable-dind
  dependencies:
    - test
  only:
    refs:
      - master
      - develop
      - hotfix
      - merge_requests
  script:
    - echo "Building image..."
    - docker build -t $REPOSITORY_URL:latest .
    - echo "Tagging image..."
    - docker tag $REPOSITORY_URL:latest $REPOSITORY_URL:$CI_COMMIT_SHORT_SHA
    - echo "Pushing image..."
    - docker push $REPOSITORY_URL:latest
    - docker push $REPOSITORY_URL:$CI_COMMIT_SHORT_SHA

deploy-dev:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  stage: deploy
  dependencies:
    - build
  only:
    refs:
      - develop
  script:
    - sed -i "s~VAR_S3_BUCKET~$DEV_S3_BUCKET~g" deploy/task_definition.json
    - sed -i "s~VAR_CLOUDFRONT_URL~$DEV_CLOUDFRONT_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_MONGODB_URI~$DEV_MONGODB_URI~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_API_URL~$DEV_VITEC_API_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_INSTALLATION_ID~$DEV_VITEC_INSTALLATION_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_PASSWORD~$DEV_VITEC_PASSWORD~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_USER_NAME~$DEV_VITEC_USER_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_AWS_REGION~$AWS_DEFAULT_REGION~g" deploy/task_definition.json
    - sed -i "s~VAR_FAMILY~$DEV_TASK_DEFINITION_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_IMAGE_URL~$REPOSITORY_URL:$CI_COMMIT_SHORT_SHA~g" deploy/task_definition.json
    - sed -i "s~VAR_AWS_LOG_GROUP~/ecs/dev-sync~g" deploy/task_definition.json
    - sed -i "s~VAR_S3_SECRET_ACCESS_KEY~$DEV_S3_SECRET_ACCESS_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_S3_ACCESS_KEY_ID~$DEV_S3_ACCESS_KEY_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_TWILIO_ACCOUNT_SID~$DEV_TWILIO_ACCOUNT_SID~g" deploy/task_definition.json
    - sed -i "s~VAR_TWILIO_AUTH_TOKEN~$DEV_TWILIO_AUTH_TOKEN~g" deploy/task_definition.json
    - sed -i "s~VAR_TWILIO_SMS_PHONE_NUMBER~$DEV_TWILIO_SMS_PHONE_NUMBER~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_URL~$DEV_UNLEASH_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_INSTANCE_ID~$DEV_UNLEASH_INSTANCE_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_APP_NAME~$DEV_UNLEASH_APP_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_AUTHORIZATION_HEADER~$DEV_UNLEASH_AUTHORIZATION_HEADER~g" deploy/task_definition.json
    - sed -i "s~VAR_APP_URL~$DEV_APP_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_API_KEY~$DEV_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_USERNAME~$DEV_POSTGRES_USERNAME~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_PASSWORD~$DEV_POSTGRES_PASSWORD~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_HOST~$DEV_POSTGRES_HOST~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_DB~$DEV_POSTGRES_DB~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_PORT~$DEV_POSTGRES_PORT~g" deploy/task_definition.json
    - sed -i "s~VAR_FIREBASE_PROJECT_ID~$DEV_FIREBASE_PROJECT_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_FIREBASE_PRIVATE_KEY~$DEV_FIREBASE_PRIVATE_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_FIREBASE_CLIENT_EMAIL~$DEV_FIREBASE_CLIENT_EMAIL~g" deploy/task_definition.json
    - sed -i "s~VAR_SQS_URL~$DEV_SQS_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_DISABLE_SQS~$DEV_DISABLE_SQS~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL~$DEV_EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_SERVICE_URL~$DEV_EIENDOMSVERDI_SERVICE_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_USER~$DEV_EIENDOMSVERDI_USER~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_PASSWORD~$DEV_EIENDOMSVERDI_PASSWORD~g" deploy/task_definition.json
    - sed -i "s~VAR_MAPBOX_ACCESS_TOKEN~$DEV_MAPBOX_ACCESS_TOKEN~g" deploy/task_definition.json
    - sed -i "s~VAR_MAPBOX_STYLE~$DEV_MAPBOX_STYLE~g" deploy/task_definition.json
    - sed -i "s~VAR_MAPBOX_USER_NAME~$DEV_MAPBOX_USER_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_TOKEN_SECRET~$DEV_TOKEN_SECRET~g" deploy/task_definition.json
    - sed -i "s~VAR_BACKEND_URL~$DEV_BACKEND_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_SENDGRID_API_KEY~$DEV_SENDGRID_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_SLACK_WEBHOOK_BASE_URI~$DEV_SLACK_WEBHOOK_BASE_URI~g" deploy/task_definition.json
    - sed -i "s~VAR_SLACK_DOCUMENT_ERROR_POSTFIX~$DEV_SLACK_DOCUMENT_ERROR_POSTFIX~g" deploy/task_definition.json
    - sed -i "s~VAR_SLACK_SMS_DUPLICATION_ERROR_POSTFIX~$DEV_SLACK_SMS_DUPLICATION_ERROR_POSTFIX~g" deploy/task_definition.json
    - sed -i "s~VAR_IDFY_ID~$DEV_IDFY_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_IDFY_SECRET~$DEV_IDFY_SECRET~g" deploy/task_definition.json
    - sed -i "s~VAR_BACKEND_API_KEY~$DEV_BACKEND_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_LIPSCORE_API_KEY~$DEV_LIPSCORE_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_LIPSCORE_SECRET_API_KEY~$DEV_LIPSCORE_SECRET_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_DD_API_KEY~$DD_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_DD_SITE~$DD_SITE~g" deploy/task_definition.json
    - sed -i "s~VAR_ECS_FARGATE~$DEV_ECS_FARGATE~g" deploy/task_definition.json
    - sed -i "s~VAR_DD_APM_ENABLED~$DEV_DD_APM_ENABLED~g" deploy/task_definition.json
    - sed -i "s~VAR_ENVIRONMENT~$DEV_ENVIRONMENT~g" deploy/task_definition.json
    - sed -i "s~VAR_LIPSCORE_API_URL~$LIPSCORE_API_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_CRAFT_CMS_URL~$DEV_CRAFT_CMS_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_CRAFT_CMS_API_KEY~$DEV_CRAFT_CMS_API_KEY~g" deploy/task_definition.json
    - cat deploy/task_definition.json
    - aws ecs register-task-definition --cli-input-json file://deploy/task_definition.json
    - echo "Updating the service..."
    - aws ecs update-service --region "${AWS_DEFAULT_REGION}" --cluster "${DEV_CLUSTER_NAME}" --service "${DEV_SERVICE_NAME}"  --task-definition "${DEV_TASK_DEFINITION_NAME}"

deploy-dev-manual:
  extends: deploy-dev
  when: manual
  only:
    refs:
      - master

deploy-prod:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  stage: deploy
  when: manual
  dependencies:
    - build
  only:
    refs:
      - master
  script:
    - sed -i "s~VAR_S3_BUCKET~$PROD_S3_BUCKET~g" deploy/task_definition.json
    - sed -i "s~VAR_CLOUDFRONT_URL~$PROD_CLOUDFRONT_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_MONGODB_URI~$PROD_MONGODB_URI~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_API_URL~$PROD_VITEC_API_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_INSTALLATION_ID~$PROD_VITEC_INSTALLATION_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_PASSWORD~$PROD_VITEC_PASSWORD~g" deploy/task_definition.json
    - sed -i "s~VAR_VITEC_USER_NAME~$PROD_VITEC_USER_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_AWS_REGION~$AWS_DEFAULT_REGION~g" deploy/task_definition.json
    - sed -i "s~VAR_FAMILY~$PROD_TASK_DEFINITION_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_IMAGE_URL~$REPOSITORY_URL:$CI_COMMIT_SHORT_SHA~g" deploy/task_definition.json
    - sed -i "s~VAR_AWS_LOG_GROUP~/ecs/prod-sync~g" deploy/task_definition.json
    - sed -i "s~VAR_S3_SECRET_ACCESS_KEY~$PROD_S3_SECRET_ACCESS_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_S3_ACCESS_KEY_ID~$PROD_S3_ACCESS_KEY_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_TWILIO_ACCOUNT_SID~$PROD_TWILIO_ACCOUNT_SID~g" deploy/task_definition.json
    - sed -i "s~VAR_TWILIO_AUTH_TOKEN~$PROD_TWILIO_AUTH_TOKEN~g" deploy/task_definition.json
    - sed -i "s~VAR_TWILIO_SMS_PHONE_NUMBER~$PROD_TWILIO_SMS_PHONE_NUMBER~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_URL~$PROD_UNLEASH_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_INSTANCE_ID~$PROD_UNLEASH_INSTANCE_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_APP_NAME~$PROD_UNLEASH_APP_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_UNLEASH_AUTHORIZATION_HEADER~$PROD_UNLEASH_AUTHORIZATION_HEADER~g" deploy/task_definition.json
    - sed -i "s~VAR_APP_URL~$PROD_APP_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_API_KEY~$PROD_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_USERNAME~$PROD_POSTGRES_USERNAME~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_PASSWORD~$PROD_POSTGRES_PASSWORD~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_HOST~$PROD_POSTGRES_HOST~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_DB~$PROD_POSTGRES_DB~g" deploy/task_definition.json
    - sed -i "s~VAR_POSTGRES_PORT~$PROD_POSTGRES_PORT~g" deploy/task_definition.json
    - sed -i "s~VAR_FIREBASE_PROJECT_ID~$PROD_FIREBASE_PROJECT_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_FIREBASE_PRIVATE_KEY~$PROD_FIREBASE_PRIVATE_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_FIREBASE_CLIENT_EMAIL~$PROD_FIREBASE_CLIENT_EMAIL~g" deploy/task_definition.json
    - sed -i "s~VAR_SQS_URL~$PROD_SQS_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_DISABLE_SQS~$PROD_DISABLE_SQS~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL~$PROD_EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_SERVICE_URL~$PROD_EIENDOMSVERDI_SERVICE_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_USER~$PROD_EIENDOMSVERDI_USER~g" deploy/task_definition.json
    - sed -i "s~VAR_EIENDOMSVERDI_PASSWORD~$PROD_EIENDOMSVERDI_PASSWORD~g" deploy/task_definition.json
    - sed -i "s~VAR_MAPBOX_ACCESS_TOKEN~$PROD_MAPBOX_ACCESS_TOKEN~g" deploy/task_definition.json
    - sed -i "s~VAR_MAPBOX_STYLE~$PROD_MAPBOX_STYLE~g" deploy/task_definition.json
    - sed -i "s~VAR_MAPBOX_USER_NAME~$PROD_MAPBOX_USER_NAME~g" deploy/task_definition.json
    - sed -i "s~VAR_TOKEN_SECRET~$PROD_TOKEN_SECRET~g" deploy/task_definition.json
    - sed -i "s~VAR_BACKEND_URL~$PROD_BACKEND_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_SENDGRID_API_KEY~$PROD_SENDGRID_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_SLACK_WEBHOOK_BASE_URI~$PROD_SLACK_WEBHOOK_BASE_URI~g" deploy/task_definition.json
    - sed -i "s~VAR_SLACK_DOCUMENT_ERROR_POSTFIX~$PROD_SLACK_DOCUMENT_ERROR_POSTFIX~g" deploy/task_definition.json
    - sed -i "s~VAR_SLACK_SMS_DUPLICATION_ERROR_POSTFIX~$PROD_SLACK_SMS_DUPLICATION_ERROR_POSTFIX~g" deploy/task_definition.json
    - sed -i "s~VAR_IDFY_ID~$PROD_IDFY_ID~g" deploy/task_definition.json
    - sed -i "s~VAR_IDFY_SECRET~$PROD_IDFY_SECRET~g" deploy/task_definition.json
    - sed -i "s~VAR_BACKEND_API_KEY~$PROD_BACKEND_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_LIPSCORE_API_KEY~$PROD_LIPSCORE_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_LIPSCORE_SECRET_API_KEY~$PROD_LIPSCORE_SECRET_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_DD_API_KEY~$DD_API_KEY~g" deploy/task_definition.json
    - sed -i "s~VAR_DD_SITE~$DD_SITE~g" deploy/task_definition.json
    - sed -i "s~VAR_ECS_FARGATE~$PROD_ECS_FARGATE~g" deploy/task_definition.json
    - sed -i "s~VAR_DD_APM_ENABLED~$PROD_DD_APM_ENABLED~g" deploy/task_definition.json
    - sed -i "s~VAR_ENVIRONMENT~$PROD_ENVIRONMENT~g" deploy/task_definition.json
    - sed -i "s~VAR_LIPSCORE_API_URL~$LIPSCORE_API_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_CRAFT_CMS_URL~$PROD_CRAFT_CMS_URL~g" deploy/task_definition.json
    - sed -i "s~VAR_CRAFT_CMS_API_KEY~$PROD_CRAFT_CMS_API_KEY~g" deploy/task_definition.json
    - cat deploy/task_definition.json
    - aws ecs register-task-definition --cli-input-json file://deploy/task_definition.json
    - echo "Updating the service..."
    - aws ecs update-service --region "${AWS_DEFAULT_REGION}" --cluster "${PROD_CLUSTER_NAME}" --service "${PROD_SERVICE_NAME}"  --task-definition "${PROD_TASK_DEFINITION_NAME}"
