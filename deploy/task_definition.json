{"executionRoleArn": "arn:aws:iam::917043647191:role/ecsTaskExecutionRole", "containerDefinitions": [{"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "VAR_AWS_LOG_GROUP", "awslogs-region": "VAR_AWS_REGION", "awslogs-stream-prefix": "ecs"}}, "entryPoint": [], "portMappings": [{"containerPort": 3000, "protocol": "tcp"}], "command": [], "cpu": 1024, "environment": [{"name": "S3_BUCKET", "value": "VAR_S3_BUCKET"}, {"name": "S3_SECRET_ACCESS_KEY", "value": "VAR_S3_SECRET_ACCESS_KEY"}, {"name": "S3_ACCESS_KEY_ID", "value": "VAR_S3_ACCESS_KEY_ID"}, {"name": "CLOUDFRONT_URL", "value": "VAR_CLOUDFRONT_URL"}, {"name": "MONGODB_URI", "value": "VAR_MONGODB_URI"}, {"name": "VITEC_API_URL", "value": "VAR_VITEC_API_URL"}, {"name": "VITEC_INSTALLATION_ID", "value": "VAR_VITEC_INSTALLATION_ID"}, {"name": "VITEC_PASSWORD", "value": "VAR_VITEC_PASSWORD"}, {"name": "VITEC_USER_NAME", "value": "VAR_VITEC_USER_NAME"}, {"name": "TWILIO_ACCOUNT_SID", "value": "VAR_TWILIO_ACCOUNT_SID"}, {"name": "TWILIO_AUTH_TOKEN", "value": "VAR_TWILIO_AUTH_TOKEN"}, {"name": "TWILIO_SMS_PHONE_NUMBER", "value": "VAR_TWILIO_SMS_PHONE_NUMBER"}, {"name": "UNLEASH_URL", "value": "VAR_UNLEASH_URL"}, {"name": "UNLEASH_INSTANCE_ID", "value": "VAR_UNLEASH_INSTANCE_ID"}, {"name": "UNLEASH_APP_NAME", "value": "VAR_UNLEASH_APP_NAME"}, {"name": "UNLEASH_AUTHORIZATION_HEADER", "value": "VAR_UNLEASH_AUTHORIZATION_HEADER"}, {"name": "APP_URL", "value": "VAR_APP_URL"}, {"name": "NO_COLOR", "value": "true"}, {"name": "API_KEY", "value": "VAR_API_KEY"}, {"name": "POSTGRES_USERNAME", "value": "VAR_POSTGRES_USERNAME"}, {"name": "POSTGRES_PASSWORD", "value": "VAR_POSTGRES_PASSWORD"}, {"name": "POSTGRES_HOST", "value": "VAR_POSTGRES_HOST"}, {"name": "POSTGRES_DB", "value": "VAR_POSTGRES_DB"}, {"name": "POSTGRES_PORT", "value": "VAR_POSTGRES_PORT"}, {"name": "FIREBASE_PROJECT_ID", "value": "VAR_FIREBASE_PROJECT_ID"}, {"name": "FIREBASE_PRIVATE_KEY", "value": "VAR_FIREBASE_PRIVATE_KEY"}, {"name": "FIREBASE_CLIENT_EMAIL", "value": "VAR_FIREBASE_CLIENT_EMAIL"}, {"name": "SQS_URL", "value": "VAR_SQS_URL"}, {"name": "DISABLE_SQS", "value": "VAR_DISABLE_SQS"}, {"name": "EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL", "value": "VAR_EIENDOMSVERDI_PUBLIC_INFORMATION_REALTIME_URL"}, {"name": "EIENDOMSVERDI_SERVICE_URL", "value": "VAR_EIENDOMSVERDI_SERVICE_URL"}, {"name": "EIENDOMSVERDI_USER", "value": "VAR_EIENDOMSVERDI_USER"}, {"name": "EIENDOMSVERDI_PASSWORD", "value": "VAR_EIENDOMSVERDI_PASSWORD"}, {"name": "MAPBOX_ACCESS_TOKEN", "value": "VAR_MAPBOX_ACCESS_TOKEN"}, {"name": "MAPBOX_STYLE", "value": "VAR_MAPBOX_STYLE"}, {"name": "MAPBOX_USER_NAME", "value": "VAR_MAPBOX_USER_NAME"}, {"name": "TOKEN_SECRET", "value": "VAR_TOKEN_SECRET"}, {"name": "BACKEND_URL", "value": "VAR_BACKEND_URL"}, {"name": "SENDGRID_API_KEY", "value": "VAR_SENDGRID_API_KEY"}, {"name": "SLACK_WEBHOOK_BASE_URI", "value": "VAR_SLACK_WEBHOOK_BASE_URI"}, {"name": "SLACK_DOCUMENT_ERROR_POSTFIX", "value": "VAR_SLACK_DOCUMENT_ERROR_POSTFIX"}, {"name": "SLACK_SMS_DUPLICATION_ERROR_POSTFIX", "value": "VAR_SLACK_SMS_DUPLICATION_ERROR_POSTFIX"}, {"name": "IDFY_ID", "value": "VAR_IDFY_ID"}, {"name": "IDFY_SECRET", "value": "VAR_IDFY_SECRET"}, {"name": "BACKEND_API_KEY", "value": "VAR_BACKEND_API_KEY"}, {"name": "LIPSCORE_API_KEY", "value": "VAR_LIPSCORE_API_KEY"}, {"name": "LIPSCORE_SECRET_API_KEY", "value": "VAR_LIPSCORE_SECRET_API_KEY"}, {"name": "ECS_FARGATE", "value": "true"}, {"name": "ENVIRONMENT", "value": "VAR_ENVIRONMENT"}, {"name": "LIPSCORE_API_URL", "value": "VAR_LIPSCORE_API_URL"}, {"name": "CRAFT_CMS_URL", "value": "VAR_CRAFT_CMS_URL"}, {"name": "CRAFT_CMS_API_KEY", "value": "VAR_CRAFT_CMS_API_KEY"}], "memoryReservation": 2048, "volumesFrom": [], "image": "VAR_IMAGE_URL", "essential": true, "links": [], "name": "node"}, {"name": "datadog-agent", "image": "public.ecr.aws/datadog/agent:latest", "essential": true, "portMappings": [{"hostPort": 8126, "protocol": "tcp", "containerPort": 8126}], "environment": [{"name": "DD_API_KEY", "value": "VAR_DD_API_KEY"}, {"name": "ECS_FARGATE", "value": "VAR_ECS_FARGATE"}, {"name": "DD_SITE", "value": "VAR_DD_SITE"}, {"name": "DD_APM_ENABLED", "value": "VAR_DD_APM_ENABLED"}]}], "placementConstraints": [], "memory": "4096", "family": "VAR_FAMILY", "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "2048", "volumes": []}